<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@drawable/common_rect_background_2_default_top_r30"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <androidx.compose.ui.platform.ComposeView
        android:id="@+id/cvLivePlaceEntrance"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:minHeight="80dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/viewPortraitBg"
        android:layout_width="116dp"
        android:layout_height="116dp"
        android:layout_marginTop="-24dp"
        android:background="@drawable/common_oval_background_2_default"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/cvLivePlaceEntrance"
        app:layout_goneMarginTop="56dp" />

    <com.interfun.buz.common.widget.view.PortraitImageView
        android:id="@+id/ivPortrait"
        android:layout_width="100dp"
        android:layout_height="100dp"
        app:layout_constraintBottom_toBottomOf="@+id/viewPortraitBg"
        app:layout_constraintEnd_toEndOf="@+id/viewPortraitBg"
        app:layout_constraintStart_toStartOf="@+id/viewPortraitBg"
        app:layout_constraintTop_toTopOf="@+id/viewPortraitBg"
        tools:background="@tools:sample/avatars" />

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftvMuteMessage"
        style="@style/iconfont_base"
        android:layout_width="100dp"
        android:layout_height="100dp"
        android:background="@drawable/common_oval_overlay_mask_black_light"
        android:text="@string/ic_sound_close"
        android:textColor="@color/color_text_white_important"
        android:textSize="36dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/ivPortrait"
        app:layout_constraintEnd_toEndOf="@+id/ivPortrait"
        app:layout_constraintStart_toStartOf="@+id/ivPortrait"
        app:layout_constraintTop_toTopOf="@+id/ivPortrait"
        tools:visibility="visible" />


    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftMenu"
        style="@style/iconfont_24"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="4dp"
        android:text="@string/ic_menu"
        android:textColor="@color/text_white_main"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

    <View
        android:id="@+id/viewQuietDotBg"
        android:layout_width="28dp"
        android:layout_height="28dp"
        android:layout_margin="4dp"
        android:background="@drawable/common_oval_background_2_default"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/viewPortraitBg"
        app:layout_constraintEnd_toEndOf="@+id/viewPortraitBg"
        tools:visibility="visible" />

    <View
        android:id="@+id/viewQuietDot"
        android:layout_width="12dp"
        android:layout_height="12dp"
        android:background="@drawable/common_oval_text_highlight_default"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/viewQuietDotBg"
        app:layout_constraintEnd_toEndOf="@+id/viewQuietDotBg"
        app:layout_constraintStart_toStartOf="@+id/viewQuietDotBg"
        app:layout_constraintTop_toTopOf="@+id/viewQuietDotBg"
        tools:visibility="visible" />

    <LinearLayout
        android:id="@+id/clInfoArea"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="20dp"
        android:orientation="vertical"
        android:gravity="center_horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/viewPortraitBg">

        <TextView
            android:id="@+id/tvName"
            style="@style/text_title_large"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:includeFontPadding="false"
            android:textColor="@color/color_text_white_important"
            tools:text="Anna Wilson" />

        <TextView
            android:id="@+id/tvBuzId"
            style="@style/text_body_medium"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:singleLine="true"
            android:textColor="@color/color_text_white_secondary"
            tools:text="Buz001" />

        <TextView
            android:id="@+id/tvNotes"
            style="@style/text_body_medium"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:gravity="center"
            android:singleLine="true"
            android:text="@string/profile_name_not_on_contacts"
            android:textColor="@color/color_text_white_primary"
            android:visibility="gone"
            app:layout_constrainedWidth="true"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tvContactName"
            style="@style/text_body_medium"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:gravity="center"
            android:singleLine="true"
            android:textColor="@color/color_text_white_secondary"
            android:visibility="gone"
            app:layout_constrainedWidth="true"
            tools:text="Phone contact: xxx"
            tools:visibility="visible" />

        <LinearLayout
            android:id="@+id/llMuteNotification"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:background="@drawable/chat_rect_overlay_background_5_default_radius_30"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingHorizontal="8dp"
            android:paddingVertical="4dp"
            android:visibility="gone"
            tools:visibility="visible">

            <com.interfun.buz.common.widget.view.IconFontTextView
                android:id="@+id/iftvMuteNotification"
                style="@style/iconfont_14"
                android:text="@string/ic_ring_off"
                android:textColor="@color/color_text_white_primary" />

            <TextView
                android:id="@+id/tvMuteNotification"
                style="@style/text_body_medium"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:text="@string/notification"
                android:textColor="@color/color_text_white_secondary" />

        </LinearLayout>

    </LinearLayout>

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/btnStartBarrier"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:barrierDirection="bottom"
            app:constraint_referenced_ids="vsBigSendMessageBtn,btnStart" />

        <include
            android:id="@+id/vsBigSendMessageBtn"
            layout="@layout/profile_big_send_message_btn"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:layout_marginTop="20dp"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:visibility="gone"
            tools:visibility="gone"
            app:layout_constraintTop_toBottomOf="@+id/clInfoArea"/>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/btnStart"
            android:layout_width="0dp"
            android:layout_height="72dp"
            android:layout_marginStart="20dp"
            android:layout_marginTop="20dp"
            android:paddingHorizontal="11dp"
            app:layout_goneMarginEnd="20dp"
            android:background="@drawable/common_rect_button_secondary_radius_16"
            app:layout_constraintEnd_toStartOf="@id/btnVoiceCall"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintHorizontal_weight="1"
            android:visibility="gone"
            tools:visibility="visible"
            app:layout_constraintTop_toBottomOf="@+id/clInfoArea">

            <com.interfun.buz.common.widget.view.IconFontTextView
                android:id="@+id/iconBtnStart"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:text="@string/ic_voice"
                android:textColor="@color/basic_primary"
                android:textSize="24sp"
                app:layout_constraintBottom_toTopOf="@id/tvBtnStartText"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_chainStyle="packed" />

            <TextView
                android:id="@+id/tvBtnStartText"
                style="@style/text_label_small"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="6dp"
                android:includeFontPadding="false"
                android:text="@string/profile_send_message"
                android:textColor="@color/color_text_white_primary"
                android:gravity="center_horizontal"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/iconBtnStart" />
        </androidx.constraintlayout.widget.ConstraintLayout>


        <com.interfun.buz.common.widget.button.CommonButton
            android:id="@+id/btnAddFriend"
            android:layout_width="0dp"
            android:layout_height="56dp"
            android:layout_marginStart="20dp"
            android:layout_marginTop="20dp"
            android:layout_marginEnd="20dp"
            android:visibility="gone"
            app:iconFont="@string/ic_contact_add"
            app:iconSize="20dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/clInfoArea"
            app:text="@string/add_friend"
            app:textSize="16sp"
            app:type="primary_larger"
            tools:visibility="gone" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/btnVoiceCall"
            android:layout_width="0dp"
            android:layout_height="72dp"
            android:layout_marginTop="20dp"
            android:paddingHorizontal="11dp"
            android:background="@drawable/common_rect_button_secondary_radius_16"
            android:visibility="gone"
            app:layout_constraintEnd_toStartOf="@id/btnVideoCall"
            app:layout_constraintStart_toEndOf="@+id/btnStart"
            android:layout_marginStart="8dp"
            app:layout_goneMarginEnd="20dp"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintTop_toBottomOf="@id/clInfoArea"
            tools:visibility="visible">

            <com.interfun.buz.common.widget.view.IconFontTextView
                android:id="@+id/iconBtnCall"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:text="@string/ic_tel"
                android:textColor="@color/basic_primary"
                android:textSize="24sp"
                app:layout_constraintBottom_toTopOf="@id/tvBtnCall"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_chainStyle="packed" />

            <TextView
                android:id="@+id/tvBtnCall"
                style="@style/text_label_small"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="6dp"
                android:includeFontPadding="false"
                android:text="@string/profile_btn_voice_call"
                android:textColor="@color/color_text_white_primary"
                android:gravity="center_horizontal"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/iconBtnCall" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/btnVideoCall"
            android:layout_width="0dp"
            android:layout_height="72dp"
            android:layout_marginTop="20dp"
            android:layout_marginEnd="20dp"
            android:paddingHorizontal="11dp"
            android:background="@drawable/common_rect_button_secondary_radius_16"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/btnVoiceCall"
            android:layout_marginStart="8dp"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintTop_toBottomOf="@id/clInfoArea"
            tools:visibility="visible">

            <com.interfun.buz.common.widget.view.IconFontTextView
                android:id="@+id/iconBtnVideoCall"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:text="@string/ic_video"
                android:textColor="@color/basic_primary"
                android:textSize="24sp"
                app:layout_constraintBottom_toTopOf="@id/tvBtnVideoCall"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_chainStyle="packed" />

            <TextView
                android:id="@+id/tvBtnVideoCall"
                style="@style/caption"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="6dp"
                android:includeFontPadding="false"
                android:text="@string/rtc_videocall"
                android:textColor="@color/text_white_main"
                android:gravity="center_horizontal"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/iconBtnVideoCall" />
        </androidx.constraintlayout.widget.ConstraintLayout>


        <LinearLayout
            android:id="@+id/llNotifyAddFriend"
            android:layout_width="0dp"
            android:layout_height="21dp"
            android:layout_marginBottom="44dp"
            android:gravity="center"
            android:orientation="horizontal"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent">

            <TextView
                android:id="@+id/tvNotify"
                style="@style/body"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:maxWidth="200dp"
                android:singleLine="true"
                android:textColor="@color/text_white_default" />

            <TextView
                android:id="@+id/tvBy"
                style="@style/body"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center_vertical"
                android:text="@string/by"
                android:textColor="@color/text_white_default" />

            <com.interfun.buz.common.widget.view.IconFontTextView
                android:id="@+id/iftvShare"
                android:layout_width="18dp"
                android:layout_height="18dp"
                android:layout_marginStart="8dp"
                android:text="@string/ic_share"
                android:textColor="@color/basic_primary"
                android:textSize="18sp" />

            <TextView
                android:id="@+id/tvShare"
                style="@style/button"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginStart="4dp"
                android:gravity="center_vertical"
                android:textColor="@color/basic_primary" />
        </LinearLayout>

        <View
            android:id="@+id/vCoordinate"
            android:layout_width="0dp"
            android:layout_height="56dp"
            android:layout_marginHorizontal="20dp"
            android:layout_marginTop="30dp"
            android:visibility="invisible"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/clInfoArea"
            tools:visibility="visible" />


        <TextView
            android:id="@+id/tvInviteMsg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:letterSpacing="0.03"
            android:textColor="@color/text_white_secondary"
            android:textSize="14sp"
            android:visibility="gone"
            android:gravity="center"
            app:layout_constraintEnd_toEndOf="@id/vCoordinate"
            app:layout_constraintStart_toStartOf="@id/vCoordinate"
            app:layout_constraintTop_toBottomOf="@id/vCoordinate"
            tools:visibility="visible" />

    <View
        android:id="@+id/viewEditAndCreateBg"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp"
        app:layout_goneMarginTop="20dp"
        android:background="@drawable/common_rect_background_4_default_radius_16"
        app:layout_constraintBottom_toBottomOf="@+id/tvShareContact"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvEditAlis" />


    <!--Set Alias-->
    <TextView
        android:id="@+id/tvEditAlis"
        style="@style/text_label_large"
        android:layout_width="0dp"
        android:layout_height="48dp"
        android:layout_marginHorizontal="20dp"
        android:layout_marginTop="20dp"
        android:gravity="start|center_vertical"
        android:textAlignment="viewStart"
        android:paddingStart="20dp"
        android:paddingEnd="36dp"
        android:singleLine="true"
        android:text="@string/profile_edit_display_name"
        android:textColor="@color/color_text_white_primary"
        android:textDirection="locale"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/btnStartBarrier" />

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftEditArrow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="20dp"
        app:autoRTL="true"
        android:text="@string/ic_arrow_right"
        android:textColor="@color/text_white_secondary"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="@+id/tvEditAlis"
        app:layout_constraintEnd_toEndOf="@+id/tvEditAlis"
        app:layout_constraintTop_toTopOf="@+id/tvEditAlis" />


    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupEditAlis"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        tools:visibility="visible"
        app:constraint_referenced_ids="tvEditAlis,iftEditArrow" />

    <!--Create group with %s-->
    <TextView
        android:id="@+id/tvCreateGroup"
        style="@style/text_label_large"
        android:layout_width="0dp"
        android:layout_height="48dp"
        android:layout_marginHorizontal="20dp"
        android:gravity="start|center_vertical"
        android:textAlignment="viewStart"
        android:paddingStart="20dp"
        android:paddingEnd="36dp"
        android:singleLine="true"
        android:text="@string/contact_do_you_like_ai"
        android:textColor="@color/color_text_white_primary"
        android:textDirection="locale"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvEditAlis"
        app:layout_goneMarginTop="20dp"
        tools:text="@string/create_group_with_who" />

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftArrow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="20dp"
        android:text="@string/ic_arrow_right"
        android:textColor="@color/text_white_secondary"
        android:textSize="16sp"
        app:autoRTL="true"
        app:layout_constraintBottom_toBottomOf="@+id/tvCreateGroup"
        app:layout_constraintEnd_toEndOf="@+id/tvCreateGroup"
        app:layout_constraintTop_toTopOf="@+id/tvCreateGroup" />

    <View
        android:id="@+id/viewCreateGroupLine"
        android:layout_width="0dp"
        android:layout_height="0.4dp"
        android:layout_marginHorizontal="20dp"
        android:background="@color/color_foreground_neutral_important_disable"
        app:layout_constraintEnd_toEndOf="@+id/viewEditAndCreateBg"
        app:layout_constraintStart_toStartOf="@+id/viewEditAndCreateBg"
        app:layout_constraintTop_toTopOf="@+id/tvCreateGroup" />


    <androidx.constraintlayout.widget.Group
        android:id="@+id/createGroup"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="tvCreateGroup,iftArrow,viewCreateGroupLine"
        tools:visibility="visible" />

    <!--ShareContact-->
    <TextView
        android:id="@+id/tvShareContact"
        style="@style/text_label_large"
        android:layout_width="0dp"
        android:layout_height="48dp"
        android:layout_marginHorizontal="20dp"
        android:gravity="start|center_vertical"
        android:textAlignment="viewStart"
        android:paddingStart="20dp"
        android:paddingEnd="36dp"
        android:singleLine="true"
        android:text="@string/contact_card_user_share"
        android:textColor="@color/color_text_white_primary"
        android:textDirection="locale"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvCreateGroup" />

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftShareContactArrow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="20dp"
        android:text="@string/ic_arrow_right"
        android:textColor="@color/text_white_secondary"
        android:textSize="16sp"
        app:autoRTL="true"
        app:layout_constraintBottom_toBottomOf="@+id/tvShareContact"
        app:layout_constraintEnd_toEndOf="@+id/tvShareContact"
        app:layout_constraintTop_toTopOf="@+id/tvShareContact" />

    <View
        android:id="@+id/viewShareContactLine"
        android:layout_width="0dp"
        android:layout_height="0.4dp"
        android:layout_marginHorizontal="20dp"
        android:background="@color/color_foreground_neutral_important_disable"
        app:layout_constraintEnd_toEndOf="@+id/viewEditAndCreateBg"
        app:layout_constraintStart_toStartOf="@+id/viewEditAndCreateBg"
        app:layout_constraintTop_toTopOf="@+id/tvShareContact" />


    <androidx.constraintlayout.widget.Group
        android:id="@+id/shareContact"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="tvShareContact,iftShareContactArrow,viewShareContactLine"
        tools:visibility="visible" />

    <View
        android:id="@+id/vLineInviteMsg"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginStart="20dp"
        android:layout_marginTop="27dp"
        android:layout_marginEnd="20dp"
        android:background="@color/color_foreground_neutral_important_disable"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvShareContact" />

    <TextView
        android:id="@+id/tvInviteMsg2"
        style="@style/body"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:textColor="@color/text_white_secondary"
        app:layout_constraintStart_toStartOf="@id/vCoordinate"
        app:layout_constraintTop_toBottomOf="@id/vLineInviteMsg"
        tools:text="@string/invite_to_register" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/gInviteMsg2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="tvInviteMsg2,vLineInviteMsg" />

    <TextView
        android:id="@+id/tvInAppNotification"
        style="@style/text_body_small_prominent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="40dp"
        android:layout_marginTop="24dp"
        android:text="@string/notification"
        android:textColor="@color/color_text_white_tertiary"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvShareContact"
        tools:visibility="visible" />

    <com.interfun.buz.chat.common.view.widget.UserProfileNotificationSettingView
        android:id="@+id/notificationSetting"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="20dp"
        android:layout_marginTop="6dp"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvInAppNotification"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvTranslateContent"
        style="@style/text_body_small_prominent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="40dp"
        android:layout_marginTop="24dp"
        android:text="@string/trans_msg_translation"
        android:textColor="@color/color_text_white_tertiary"
        android:visibility="visible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/notificationSetting" />

    <com.interfun.buz.chat.common.view.widget.UserProfileTranslateSettingView
        android:id="@+id/translateSetting"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="20dp"
        android:layout_marginTop="6dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvTranslateContent" />

    <TextView
        android:id="@+id/tvTranslateDesc"
        style="@style/text_body_small"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="40dp"
        android:layout_marginTop="6dp"
        android:text="@string/trans_translation_desc"
        android:textAlignment="viewStart"
        android:textColor="@color/color_text_white_tertiary"
        android:textDirection="locale"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/translateSetting" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupTranslate"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="tvTranslateContent,translateSetting,tvTranslateDesc"/>

        <TextView
            android:id="@+id/tvClearHistory"
            style="@style/text_label_large"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_marginHorizontal="20dp"
            android:layout_marginTop="20dp"
            android:background="@drawable/common_rect_background_4_default_radius_16"
            android:gravity="start|center_vertical"
            android:textAlignment="viewStart"
            android:paddingHorizontal="20dp"
            android:text="@string/chat_clear_chat_history"
            android:textColor="@color/color_text_white_primary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvTranslateDesc"
            android:visibility="gone"
            tools:visibility="visible"/>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clBottomBlock"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="20dp"
            android:layout_marginTop="10dp"
            android:background="@drawable/common_rect_background_4_default_radius_16"
            android:paddingBottom="1dp"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@+id/tvClearHistory"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintVertical_bias="0"
            android:layout_marginBottom="64dp"
            tools:visibility="visible">


            <TextView
                android:id="@+id/tvBlock"
                style="@style/text_label_large"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginStart="20dp"
                android:gravity="start|center_vertical"
                android:textAlignment="viewStart"
                android:text="@string/block"
                android:textColor="@color/color_text_white_primary"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/vSplitLineBlock"
                android:layout_width="0dp"
                android:layout_height="0.4dp"
                android:background="@color/color_foreground_neutral_important_disable"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@id/tvBlock"
                app:layout_constraintTop_toBottomOf="@id/tvBlock" />


            <TextView
                android:id="@+id/tvReport"
                style="@style/text_label_large"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginStart="20dp"
                android:gravity="start|center_vertical"
                android:textAlignment="viewStart"
                android:text="@string/report"
                android:textColor="@color/color_text_white_primary"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@id/tvBlock"
                app:layout_constraintTop_toBottomOf="@id/vSplitLineBlock" />


            <View
                android:id="@+id/vSplitLineReport"
                android:layout_width="0dp"
                android:layout_height="0.4dp"
                android:background="@color/color_foreground_neutral_important_disable"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@id/tvBlock"
                app:layout_constraintTop_toBottomOf="@id/tvReport" />


            <TextView
                android:id="@+id/btnDeleteFriend"
                style="@style/text_label_large"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginStart="20dp"
                android:gravity="start|center_vertical"
                android:textAlignment="viewStart"
                android:text="@string/chat_remove_this_friend"
                android:textColor="@color/secondary_error"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@id/tvBlock"
                app:layout_constraintTop_toBottomOf="@id/vSplitLineReport" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clBannedRoot"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="20dp"
        android:layout_marginTop="20dp"
        android:background="@drawable/common_rect_background_4_default_radius_16"
        android:paddingHorizontal="20dp"
        android:paddingVertical="14dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/clInfoArea">

        <com.interfun.buz.common.widget.view.IconFontTextView
            android:id="@+id/iftTip"
            style="@style/iconfont_base"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/ic_warning_solid"
            android:textColor="@color/text_white_main"
            android:textSize="18dp"
            app:layout_constraintBottom_toBottomOf="@+id/tvBannedTitle"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tvBannedTitle"
            app:pressEffect="false"
            tools:ignore="SpUsage" />

        <TextView
            android:id="@+id/tvBannedTitle"
            style="@style/text_label_large"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:text="@string/account_suspension"
            android:textColor="@color/color_text_white_primary"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toEndOf="@+id/iftTip"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvBannedTip"
            style="@style/text_body_large"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:textColor="@color/color_text_white_secondary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvBannedTitle"
            tools:text="@string/account_suspension_desc" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tvRemoveFriend"
        style="@style/text_label_large"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="20dp"
        android:layout_marginTop="10dp"
        android:background="@drawable/common_rect_background_4_default_radius_16"
        android:gravity="start|center_vertical"
        android:paddingHorizontal="20dp"
        android:paddingVertical="13dp"
        android:text="@string/chat_remove_this_friend"
        android:textColor="@color/color_text_consequential_default"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHeight_min="48dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/clBannedRoot" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupRemoveFriend"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="tvRemoveFriend,clBannedRoot" />

</merge>