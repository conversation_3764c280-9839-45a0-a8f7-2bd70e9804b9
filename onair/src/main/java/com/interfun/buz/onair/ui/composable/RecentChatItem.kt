package com.interfun.buz.onair.ui.composable

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.interfun.buz.compose.R
import com.interfun.buz.compose.components.CommonButton
import com.interfun.buz.compose.components.CommonButtonType
import com.interfun.buz.compose.components.PortraitImage
import com.interfun.buz.compose.ktx.HorizontalSpace
import com.interfun.buz.compose.ktx.VerticalSpace
import com.interfun.buz.compose.ktx.asString
import com.interfun.buz.compose.styles.TextStyles
import com.interfun.buz.onair.entity.RecentChatItemBean
import com.interfun.buz.onair.entity.RecentChatItemBean.Normal.ReqState

/**
 * <AUTHOR>
 * @date 2025/14/02
 * @desc
 */

@Composable
fun RecentChatItem(
    modifier: Modifier = Modifier,
    item: RecentChatItemBean.Normal,
    onAcceptClick: () -> Unit = {},
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .heightIn(70.dp)
            .background(color = colorResource(R.color.color_background_2_default))
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(start = 20.dp, top = 10.dp, bottom = 10.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Portrait Image
            if (item.groupInfo != null) {
                PortraitImage(
                    groupInfoBean = item.groupInfo,
                    modifier = Modifier.size(50.dp)
                )
            } else if (item.userInfo != null) {
                PortraitImage(
                    url = item.userInfo.portrait,
                    modifier = Modifier.size(50.dp)
                )
            }

            // Space between image and text
            HorizontalSpace(16.dp)

            // Column for Text Content
            Column(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.Center
            ) {
                // Title
                val content = item.content.orEmpty()
                val username = item.userInfo?.userName.orEmpty()
                Text(
                    text = content.ifEmpty { username },
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis,
                    style = TextStyles.labelLarge(),
                    color = colorResource(R.color.text_white_main)
                )

                // Description (conditionally shown)
                if (item.desc?.isNotEmpty() == true) {
                    VerticalSpace(2.dp)
                    Text(
                        text = item.desc,
                        maxLines = 3,
                        overflow = TextOverflow.Ellipsis,
                        style = TextStyles.bodyMedium(),
                        color = colorResource(R.color.text_white_secondary)
                    )
                }
            }

//            // Spacer if the button is not visible (equivalent to goneMarginEnd)
//            if (!isBtnAcceptVisible) {
//                HorizontalSpace(80.dp)
//            }

            HorizontalSpace(10.dp)
            // Accept Button (conditionally visible)
            Box (
                modifier =  Modifier.defaultMinSize(minWidth = 60.dp, minHeight = 32.dp)
                    .height(32.dp)
                    .wrapContentWidth(),
                contentAlignment = Alignment.Center
            ) {
                if (item.state == ReqState.SENT) {
                    Text(
                        text = R.string.live_place_share_sent.asString(),
                        modifier = Modifier.wrapContentSize(Alignment.Center),
                        style = TextStyles.labelMedium(),
                        color = colorResource(R.color.color_foreground_neutral_important_disable),
                        textAlign = TextAlign.Center
                    )
                } else {
                    CommonButton(
                        modifier = Modifier.alpha(if (item.state == ReqState.NORMAL) 1f else 0.3f),
                        type = CommonButtonType.TERTIARY_SMALL,
                        text = R.string.live_place_share_send.asString(),
                        textPadding = 10.dp,
                        showLoading = item.state == ReqState.LOADING,
                        loadingColor = colorResource(R.color.white),
                        onClick = {
                            if (item.state != ReqState.NORMAL) {
                                return@CommonButton
                            }
                            onAcceptClick()
                        },
                        enable = item.state == ReqState.NORMAL
                    )
                }
            }

            HorizontalSpace(20.dp)
        }
    }
}