package com.interfun.buz.notification.utils

import com.interfun.buz.base.ktx.asString
import com.interfun.buz.biz.center.voicefilter.compat.VoiceFilterHelper
import com.interfun.buz.common.R
import com.interfun.buz.common.ktx.getStringDefault
import com.interfun.buz.common.utils.HyperlinkUtil
import com.interfun.buz.domain.im.social.entity.ContactType
import com.interfun.buz.im.entity.IMType
import com.interfun.buz.im.ktx.asrText
import com.interfun.buz.im.ktx.getCommandMsgNotifyText
import com.interfun.buz.im.ktx.getVoiceFilterId
import com.interfun.buz.im.ktx.hasMentionedMe
import com.interfun.buz.im.ktx.isDecryptFail
import com.interfun.buz.im.ktx.mentionedInfoText
import com.interfun.buz.im.message.BuzFileMessage
import com.interfun.buz.im.message.BuzLivePlaceShareMessage
import com.interfun.buz.im.message.BuzShareContactMessage
import com.interfun.buz.im.message.RealTimeCallInviteMsg
import com.interfun.buz.im.message.WTVoiceEmojiMsg
import com.lizhi.im5.sdk.message.IMessage
import com.lizhi.im5.sdk.message.model.IM5TextMessage
import com.yibasan.lizhifm.sdk.platformtools.ResUtil

internal suspend fun getNotificationText(message: IMessage): String {
    if (message.isDecryptFail) return R.string.notify_decrypt_failed.asString()
    val prefix = if (message.hasMentionedMe) "${R.string.mentioned_you_prefix_noti.asString()} " else ""
    return prefix + when (message.msgType) {
        IMType.TYPE_TEXT_MSG -> {
            val text = message.mentionedInfoText().toString()
            val hasLink = HyperlinkUtil.hasLink(text)
            if (hasLink) {
                ResUtil.getString(R.string.chat_pop_msg_link_tag, text)
            } else text
        }
        IMType.TYPE_MEDIA_TEXT_NEW,
        IMType.TYPE_REAL_TIME_CALL -> (message.content as? RealTimeCallInviteMsg)?.title.getStringDefault()
        IMType.TYPE_MEDIA_TEXT -> (message.content as IM5TextMessage).text
        IMType.TYPE_IMAGE_MSG -> R.string.notification_tile_photo.asString()
        IMType.TYPE_VOICE_MSG,
        IMType.TYPE_VOICE_TEXT,
        IMType.TYPE_VOICE_TEXT_NEW -> {
            val voiceFilterId = message.getVoiceFilterId
            val voiceFilterName = if (voiceFilterId == null) {
                null
            }else {
                val vfData = VoiceFilterHelper.getCachedVoiceFilterDataById(voiceFilterId)
                vfData?.filterName
            }
            if (!voiceFilterName.isNullOrEmpty()) {
                "[$voiceFilterName] ${message.asrText ?: ""}"
            }else {
                "${R.string.notification_tile_voice.asString()} ${message.asrText ?: ""}"
            }
        }

        IMType.TYPE_VOICE_EMOJI -> "${R.string.notification_tile_voicemoji_updated.asString()} ${(message.content as? WTVoiceEmojiMsg)?.emojiIcon}"

        IMType.TYPE_VOICE_EMOJI_IMG -> {
            R.string.ve_voiceemoji_tip_updated.asString()
        }

        IMType.TYPE_COMMAND -> {
            message.getCommandMsgNotifyText() ?: R.string.chat_pop_msg_unsupported_tag.asString()
        }

        IMType.TYPE_VIDEO_MSG -> R.string.notification_tile_video.asString()
        IMType.TYPE_LOCATION_MSG -> R.string.notification_tile_location.asString()
        IMType.TYPE_VOICE_GIF -> R.string.voice_gif_tag.asString()
        IMType.TYPE_LIVE_PLACE_SHARE -> {
            ResUtil.getString(R.string.live_place_invite_card_share,(message.content as? BuzLivePlaceShareMessage)?.topic?:"")
        }
        IMType.TYPE_FILE_MSG -> {
            val fileName = (message.content as? BuzFileMessage)?.name ?: ""
            ResUtil.getString(R.string.chat_pop_msg_file_tag, fileName)
        }

        IMType.TYPE_SHARE_CONTACT_MSG -> {
            val msgContent = message.content as? BuzShareContactMessage
            val contactType = msgContent?.contactType ?: ContactType.User.type
            if (contactType == ContactType.User.type) {
                String.format(
                    R.string.contact_card_user_contact.asString(),
                    msgContent?.name ?: ""
                )
            } else {
                String.format(
                    R.string.contact_card_group_contact.asString(),
                    msgContent?.name ?: ""
                )
            }
        }
        else -> R.string.chat_pop_msg_unsupported_tag.asString()
    }
}