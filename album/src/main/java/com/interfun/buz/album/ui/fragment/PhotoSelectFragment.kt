package com.interfun.buz.album.ui.fragment

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.Gravity
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.contains
import androidx.core.view.marginTop
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.interfun.buz.album.R
import com.interfun.buz.album.R.string
import com.interfun.buz.album.databinding.AlbumPhotoListLayoutBinding
import com.interfun.buz.album.manager.AlbumManager
import com.interfun.buz.album.ui.activity.MediaPreviewActivity
import com.interfun.buz.album.ui.block.AlbumPermissionBlock
import com.interfun.buz.album.ui.block.AlbumPhotoListBlock
import com.interfun.buz.album.ui.block.AlbumSwitchViewBlock
import com.interfun.buz.album.ui.block.MediaPermissionResult
import com.interfun.buz.album.ui.viewmodel.AlbumMediaDataViewModel
import com.interfun.buz.album.ui.viewmodel.PhotoSelectViewModel
import com.interfun.buz.album.utils.AlbumTracker
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.base.binding.BaseBindingFragment
import com.interfun.buz.common.constants.KEY_SOURCE
import com.interfun.buz.common.constants.PhotoSelectSource
import com.interfun.buz.common.constants.RouterParamKey
import com.interfun.buz.common.constants.RouterParamKey.Album
import com.interfun.buz.common.eventbus.album.StartHomeSendingMediaAnimationEvent
import com.interfun.buz.common.ktx.getFloatDefault
import com.interfun.buz.common.ktx.toastSolidCorrect
import com.interfun.buz.common.service.ChatService
import com.interfun.buz.common.utils.PermissionHelper
import com.interfun.buz.media.bean.MediaType
import com.interfun.buz.im.entity.EventTrackExtra
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import com.yibasan.lizhifm.lzlogan.Logz
import com.yibasan.lizhifm.sdk.platformtools.ResUtil
import dagger.hilt.android.AndroidEntryPoint
import okhttp3.internal.toLongOrDefault
import kotlin.LazyThreadSafetyMode.NONE

/**
 * 图册浏览选择页使用场景
 * 1、全屏对接
 * 2、im面板半屏对接
 * <AUTHOR>
 * @date 2024/3/29
 */
@AndroidEntryPoint
class PhotoSelectFragment : BaseBindingFragment<AlbumPhotoListLayoutBinding>() {

    companion object {
        const val TAG = "PhotoSelectFragment"
        const val SOURCE_KEY = "SOURCE_KEY"
        const val REPLY_MSG_ID = "REPLY_MSG_ID"
        const val COLLAPSED_HEIGHT = "COLLAPSED_HEIGHT"

        fun instance(
            targetId: String,
            convType: Int,
            source: Int = PhotoSelectSource.Home.value,
            replyMsgId: Long? = null,
            albumCollapsedHeight: Float = 0f
        ): PhotoSelectFragment {
            return PhotoSelectFragment().apply {
                arguments = Bundle().apply {
                    putString(RouterParamKey.Album.KEY_TARGET_ID, targetId)
                    putInt(RouterParamKey.Album.KEY_CONV_TYPE, convType)
                    putInt(SOURCE_KEY, source)
                    if (replyMsgId != null) {
                        putLong(REPLY_MSG_ID, replyMsgId)
                    }
                    putFloat(COLLAPSED_HEIGHT, albumCollapsedHeight)
                }
            }
        }
    }

    private val targetId by lazy {
        arguments?.getString(RouterParamKey.Album.KEY_TARGET_ID)
    }
    private val convType by lazy {
        arguments?.getInt(RouterParamKey.Album.KEY_CONV_TYPE)
    }
    private val source by lazy(NONE){
        arguments?.getInt(SOURCE_KEY, PhotoSelectSource.Home.value)
    }
    private val replyMsgId by lazy {
        arguments?.getLong(REPLY_MSG_ID)
    }
    private val albumCollapsedHeight by lazy {
        arguments?.getFloat(COLLAPSED_HEIGHT).getFloatDefault()
    }
    private var alpha = 1f
    private var marginTop = 0
    private var albumPhotoListBlock: AlbumPhotoListBlock? = null
    private var albumPermissionBlock: AlbumPermissionBlock? = null
    private var onSendMediaCallback: (() -> Unit)? = null
    private var mAlbumDenyAccessListener : ((accessDenied: Boolean) -> Unit)? = null
    private var mAlbumDataEmptyListener : ((isEmpty: Boolean) -> Unit)? = null
    private val albumViewModel by fragment.fragmentViewModels<AlbumMediaDataViewModel>()
    private val photoSelectViewModel by fragment.viewModels<PhotoSelectViewModel>()
    private var hasClickSendBtn = false
    private var sendAndPreviewButtonParent: ViewGroup? = null

    private val startForResult: ActivityResultLauncher<Intent> = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            onSendMediaCallback?.invoke()
        }
    }
    private var mediaPermissionResult: MediaPermissionResult? = null
    private val mediaPermissionHelper = PermissionHelper(fragment)

    override fun initView() {
        super.initView()
        if (source == PhotoSelectSource.ChatEmbed.value) {
            initToolBar()
            addBottomMenuButtonsToParent()
        }
        if (source == PhotoSelectSource.ChatDialog.value) {
            binding.rvList.scrollToPosition(0)
            addBottomMenuButtonsToParent()
        }
        binding.rvList.post {
            marginTop = binding.rvList.marginTop
        }

        binding.tvCancel.click {
            if (parentFragment is BaseAlbumDialogFragment) {
                (parentFragment as? BaseAlbumDialogFragment)?.dismiss()
            } else {
                onBackPressed()
            }
        }

        binding.btnSend.click {
            hasClickSendBtn = true
            if (targetId.isNotNull() && convType.isNotNull()){
                logInfo(MediaPreviewFragment.TAG,"initView: sendMediaMessages")
                val conv = IM5ConversationType.setValue(convType!!)
                val selectedMediaList = AlbumManager.instance.getCurrentSelectMediaList().toList()
                val videoCount = AlbumManager.instance.getNumberOfVideoInSelectMediaList()

                lifecycleScope.launchDelay(delayTimes = 80) { // 显示按钮点击效果后，开始发送mediaMessage
                    val eventTrackExtra = EventTrackExtra(source = Album.ALBUM_SEND_SOURCE_PHOTO_SELECT_FRAGMENT)
                    photoSelectViewModel.sendMediaMessages(
                        targetId = targetId?.toLongOrDefault(0L) ?: 0L,
                        convType = conv!!,
                        selectedMedia = selectedMediaList,
                        replyId = replyMsgId,
                        isSendOriginImage = AlbumManager.instance.isHDSelected(),
                        eventTrackExtra = eventTrackExtra
                    )
                    viewLifecycleScope.launchIO {
                        selectedMediaList.firstOrNull()?.let {
                            StartHomeSendingMediaAnimationEvent.post(selectedMediaList.size > 1, it, RouterParamKey.Album.ALBUM_SEND_SOURCE_PHOTO_SELECT_FRAGMENT)
                        }
                    }
                    val type =
                        if (videoCount == 0) "image"
                        else if (selectedMediaList.size == videoCount) "video"
                        else "both"
                    AlbumTracker.onClickSendFromAlbumList(
                        type = type,
                        videoCount = videoCount,
                        imageCount = selectedMediaList.size - videoCount
                    )
                    onSendMediaCallback?.invoke()
                }

            }
        }

        binding.btnPreview.click {
            //跳转预览
            Logz.tag(TAG).d("btnPreview clicked")
            lifecycleScope.launchDelay(delayTimes = 50) {
                val intent = Intent(context, MediaPreviewActivity::class.java).apply {
                    putExtra(RouterParamKey.Album.KEY_SOURCE, RouterParamKey.Album.ALBUM_SOURCE_ALBUM_SELECT_TYPE)
                    putExtra(RouterParamKey.Album.KEY_TARGET_ID, targetId)
                    putExtra(RouterParamKey.Album.KEY_CONV_TYPE, convType)
                    if (null != replyMsgId) {
                        putExtra(RouterParamKey.Album.KEY_REFERENCE_MSG_ID, replyMsgId)
                    }
                }
                startForResult.launch(intent)
            }
        }

        binding.llTitle.click {  }

        binding.iftvHD.click {
            val currentHDSelected = AlbumManager.instance.isHDSelected()
            AlbumTracker.onClickHdButton(currentHDSelected.not())
            if (!currentHDSelected) {
                toastSolidCorrect(R.string.hd_is_on.asString())
            }
            AlbumManager.instance.setHDSelect(currentHDSelected.not())
        }

        AlbumManager.instance.selectedHDStateFlow.collectIn(viewLifecycleOwner) { hdSelected ->
            binding.iftvHD.text = if (hdSelected) string.ic_hd_on.asString() else string.ic_hd_off.asString()
        }
    }

    override fun initBlock() {
        super.initBlock()
        albumPermissionBlock = AlbumPermissionBlock(
            fragment = this,
            mediaPermissionHelper = mediaPermissionHelper,
            binding = binding,
            onPermissionResult = {
                logInfo(TAG, "the latest album permission result is [$it], permissions have been updated = ${mediaPermissionResult != it}")
                if (mediaPermissionResult == it){
                    return@AlbumPermissionBlock
                }
                mediaPermissionResult = it
                if (it.isGranted) {
                    //有权限
                    loadDataBlock()
                    mAlbumDenyAccessListener?.invoke(false)
                } else {
                    //无授权
                    //全屏相册选择页该场景应该在外面就进行拦截处理
                    //聊天页的半屏相册选择页会监听该回调
                    mAlbumDenyAccessListener?.invoke(true)
                }
            }
        ).bind(this)
    }

    private fun loadDataBlock() {
        AlbumSwitchViewBlock(
            this,
            binding
        ).bind(this)

        albumPhotoListBlock = AlbumPhotoListBlock(
            fragment = this,
            targetId = targetId,
            convType = convType,
            albumCollapsedHeight = albumCollapsedHeight,
            binding = binding,
            replyMsgId = replyMsgId,
            startForResult = startForResult,
            onAlbumEmptyResult = {
                mAlbumDataEmptyListener?.invoke(it)
            }
        ).bind(this)
    }

    private fun addBottomMenuButtonsToParent() {
        val groupBottomViews = binding.clGroupBottomBtnViews
        if (source == PhotoSelectSource.ChatEmbed.value) {
            groupBottomViews.removeFromParent()
            val parentView = parentFragment?.view
            val fl =
                parentView?.findViewWithTag<FrameLayout>(R.string.bottom_album_panel_buttons_tag.asString())
            fl?.addView(groupBottomViews)
        }
        if (source == PhotoSelectSource.ChatDialog.value) {
            if (sendAndPreviewButtonParent?.contains(groupBottomViews) == false) {
                groupBottomViews.removeFromParent()
                sendAndPreviewButtonParent?.addView(
                    groupBottomViews,
                    FrameLayout.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        groupBottomViews.layoutParams.height
                    ).apply {
                        gravity = Gravity.BOTTOM
                    })
            }
        }
    }

    // 修改发送按钮和预览按钮的父容器，由于在弹窗中需要始终保持在屏幕的最底部，无论是半屏弹窗还是全屏弹窗，
    // 所以需要修改发送按钮和预览按钮的父容器，改为和弹窗同级的容器才能解决
    fun changeSendAndPreviewButtonParent(container: ViewGroup) {
        this.sendAndPreviewButtonParent = container
        if (!bindingIsNull) {
            binding.clGroupBottomBtnViews.removeFromParent()
            sendAndPreviewButtonParent?.addView(
                binding.clGroupBottomBtnViews,
                FrameLayout.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    binding.clGroupBottomBtnViews.layoutParams.height
                ).apply {
                    gravity = Gravity.BOTTOM
                })
        }
    }

    fun changeSendAndPreviewButtonAlpha(alpha: Float) {
        binding.clGroupBottomBtnViews.alpha = alpha
    }

    fun hideToolBarWithSlideOffset(slideOffset: Float) {
        if (this.alpha == slideOffset) {
            return
        }
        val rate = if (slideOffset < alpha) {
            0.3f
        } else {
            1.2f
        }
        this.alpha = slideOffset
        binding.llTitle.translationZ = 0f
        binding.llTitle.alpha = alpha * rate

        binding.llTitle.translationY = (1 - alpha) * binding.llTitle.height


        val params = binding.rvList.layoutParams as ConstraintLayout.LayoutParams
        params.topMargin = (marginTop * alpha).toInt()
        binding.rvList.layoutParams = params
    }

    fun setAccessStatusBarAndListMarginTop(slideOffset: Float){
        if (binding.llStatusBar.isShown.not()){
            val goneMarginTop = binding.llTitle.height * slideOffset
            binding.rvList.constraintGoneMarginTop(goneMarginTop.toInt())
            return
        }
        val marginTop = binding.llTitle.height * slideOffset
        binding.llStatusBar.layoutMarginTop(marginTop)
    }

    private fun initToolBar() {
        binding.rvList.scrollToPosition(0)
        binding.llTitle.alpha = 0f
        binding.rvList.layoutMarginTop(0)
        binding.llTitle.translationY = binding.llTitle.height.toFloat()

        setAccessStatusBarAndListMarginTop(0f)
    }

    fun requestPermission(canShowCustomPermissionDialog: Boolean = true) {
        if (mediaPermissionResult == null || mediaPermissionResult?.isGranted == false) {
            albumPermissionBlock?.requestPermission(canShowCustomPermissionDialog)
        }
    }


    fun addAlbumDenyAccessListener(albumDenyAccess: (isDenied: Boolean) -> Unit){
        mAlbumDenyAccessListener = albumDenyAccess
    }

    fun setOnSendMediaCallback(callback: () -> Unit) {
        this.onSendMediaCallback = callback
    }

    fun addAlbumDataEmptyListener(albumDataEmptyListener: (isEmpty: Boolean) -> Unit){
        mAlbumDataEmptyListener = albumDataEmptyListener
    }

    fun isDenyAccessTipsShown(): Boolean{
        return binding.denyAccessTipsGroup.isShown
    }

    fun isEmptyAlbumShown(): Boolean{
        return binding.emptyDataView.isShown
    }

    fun hasEmptyMediaList(): Boolean{
        return albumPhotoListBlock?.hasEmptyMediaList()?:true
    }

    override fun onDestroy() {
        super.onDestroy()
        AlbumManager.instance.release()
//        if (!hasClickSendBtn) {
//            albumViewModel.clearAllVideoItemBitmapCache()
//        }
    }
}