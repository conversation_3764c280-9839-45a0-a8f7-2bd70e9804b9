package com.interfun.buz.common.ktx

import android.content.Context
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.Drawable
import android.widget.ImageView
import androidx.lifecycle.LifecycleOwner
import coil.Coil
import coil.ComponentRegistry
import coil.ImageLoader
import coil.annotation.ExperimentalCoilApi
import coil.imageLoader
import coil.load
import coil.request.Disposable
import coil.request.ErrorResult
import coil.request.ImageRequest
import coil.request.SuccessResult
import coil.target.ImageViewTarget
import coil.target.Target
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.R
import com.interfun.buz.common.interceptor.AesDecryptionInterceptor
import com.interfun.buz.common.interceptor.CoilLogInterceptor
import com.interfun.buz.common.interceptor.ProgressInterceptor
import com.interfun.buz.common.manager.videoCoverImageLoader
import com.interfun.buz.common.widget.portrait.PortraitUtil
import com.interfun.buz.common.widget.portrait.PortraitUtil.defaultPortraitRes
import com.interfun.buz.common.widget.portrait.PortraitUtil.defaultPortraitSize
import com.lizhi.fm.e2ee.aes.IAesComponent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.concurrent.atomic.AtomicLong

private const val TAG = "ImageView.kt"


private var imageTagValue = AtomicLong()
fun createAesImageLoader(aesComponent: IAesComponent): ImageLoader {
    //todo 这里的做法很挫，后面再优化成独立fetcher的方式
    val aesDecryptHttpClient =
        globalOkHttpClient.newBuilder().addInterceptor(AesDecryptionInterceptor(aesComponent))
            .build()
    return appContext.imageLoader.newBuilder().okHttpClient(aesDecryptHttpClient)
        .components(ComponentRegistry.Builder().apply {
            add(CoilLogInterceptor())
        }.build()).build()
}

fun ImageView.loadWithThumbnailNew(
    thumbnailUrl: Any? = null,
    finalUrl: String? = null,
    intermediateUrl: Any? = null,
    thumbnailRequest: ImageRequest? = null,
    finalRequest: ImageRequest? = null,
    intermediateRequest: ImageRequest? = null,
    aesComponent: IAesComponent? = null,
) {
    val image = this
    val defaultImageLoader = context.imageLoader
    val aesLoader = if (aesComponent == null) null else createAesImageLoader(aesComponent)
    val realLoader = aesLoader ?: defaultImageLoader
    // logMine("loadWithThumbnailNew isAesLoader:${realLoader === aesLoader},thumbnailUrl:$thumbnailUrl,intermediateUrl:$intermediateUrl,finalUrl:$finalUrl")
    val realFinalRequest =
        if (finalUrl == null) {
            null
        } else {
            createRequest(context, finalRequest)
                .data(finalUrl)
                .target(object : ImageViewTarget(image) {
                    override fun onSuccess(result: Drawable) {
                        super.onSuccess(result)
                        // logMine("realFinalRequest onSuccess:$result,finalUrl:$finalUrl")
                        aesLoader?.shutdown()
                    }

                    override fun onError(error: Drawable?) {
                        super.onError(error)
                        // logMine("realFinalRequest onError:$error，finalUrl：$finalUrl")
                        aesLoader?.shutdown()
                    }

                    override fun onDestroy(owner: LifecycleOwner) {
                        super.onDestroy(owner)
                        aesLoader?.shutdown()
                        // logMine("realFinalRequest onDestroy,finalUrl：$finalUrl")
                    }
                })
        }
    val realIntermediateRequest = if (intermediateUrl != null) {
        createRequest(context, intermediateRequest)
            .data(intermediateUrl)
            .target(object : ImageViewTarget(image) {
                override fun onSuccess(result: Drawable) {
                    super.onSuccess(result)
                    // logMine("realIntermediateRequest onSuccess:$result,intermediateUrl：$intermediateUrl")
                    if (realFinalRequest != null) {
                        realLoader.enqueue(realFinalRequest.placeholder(result).error(result).build())
                    } else {
                        aesLoader?.shutdown()
                    }
                }

                override fun onError(error: Drawable?) {
                    super.onError(error)
                    // logMine("realIntermediateRequest onError:$error,intermediateUrl：$intermediateUrl")
                    if (realFinalRequest != null) {
                        realLoader.enqueue(realFinalRequest.placeholder(error).error(error).build())
                    } else {
                        aesLoader?.shutdown()
                    }
                }

                override fun onDestroy(owner: LifecycleOwner) {
                    super.onDestroy(owner)
                    // logMine("realIntermediateRequest onDestroy,intermediateUrl：$intermediateUrl")
                    aesLoader?.shutdown()
                }
            })
    } else {
        null
    }
    val realThumbnailRequest = if (thumbnailUrl != null) {
        createRequest(context, thumbnailRequest)
            .data(thumbnailUrl)
            .target(object : ImageViewTarget(image) {
                override fun onSuccess(result: Drawable) {
                    super.onSuccess(result)
                    // logMine("realThumbnailRequest onSuccess:$result,thumbnailUrl：$thumbnailUrl")
                    if (realIntermediateRequest != null) {
                        realLoader.enqueue(realIntermediateRequest.placeholder(result).error(result).build())
                    } else if (realFinalRequest != null) {
                        realLoader.enqueue(realFinalRequest.placeholder(result).error(result).build())
                    } else {
                        aesLoader?.shutdown()
                    }
                }

                override fun onError(error: Drawable?) {
                    super.onError(error)
                    // logMine("realThumbnailRequest onSuccess:$error,thumbnailUrl：$thumbnailUrl")
                    if (realIntermediateRequest != null) {
                        realLoader.enqueue(realIntermediateRequest.placeholder(error).error(error).build())
                    } else if (realFinalRequest != null) {
                        realLoader.enqueue(realFinalRequest.placeholder(error).error(error).build())
                    } else {
                        aesLoader?.shutdown()
                    }
                }

                override fun onDestroy(owner: LifecycleOwner) {
                    super.onDestroy(owner)
                    // logMine("realThumbnailRequest onDestroy,thumbnailUrl：$thumbnailUrl")
                    aesLoader?.shutdown()
                }
            })
    } else {
        null
    }
    if (realThumbnailRequest != null) {
        realLoader.enqueue(realThumbnailRequest.build())
    } else if (realIntermediateRequest != null) {
        realLoader.enqueue(realIntermediateRequest.build())
    } else if (realFinalRequest != null) {
        realLoader.enqueue(realFinalRequest.build())
    }
}

private fun createRequest(context: Context, request: ImageRequest?): ImageRequest.Builder {
    return if (request == null) {
        ImageRequest.Builder(context)
    } else {
        ImageRequest.Builder(request)
    }
}

/**
 * 从给定的URL加载[ImageView]中的图像。如果提供了缩略图，则首先加载它以提高性能。
 * Load the image in [ImageView] from the given URL. If a thumbnail is provided, load it first to improve performance.
 *
 * @param thumbnailUrl 可选的缩略图URL.  Optional thumbnail URL.
 * @param url 要加载的图像的URL. The URL of the image to be loaded.
 * @param aesComponent AES解密处理组件. AES decryption processing component
 * @param builder 一个附加的[ImageRequest.Builder]配置. An additional[ImageRequest.Builder]configuration
 */
@Deprecated("这里管理很乱，size不会用view的大小，请用loadWithThumbnailNew替代")
@OptIn(ExperimentalCoilApi::class)
fun ImageView.loadWithThumbnail(
    thumbnailUrl: Any? = null,
    url: String?,
    aesComponent: IAesComponent? = null,
    onSuccess: TwoParamCallback<ImageRequest, SuccessResult>? = null,
    onError: TwoParamCallback<ImageRequest, ErrorResult>? = null,
    builder: ImageRequest.Builder.() -> Unit = {},
) {
    val useTagValue = imageTagValue.incrementAndGet()
    setTag(R.id.common_image_load_id,useTagValue)
    val defaultImageLoader = context.imageLoader
    val sourceImageLoader = if (aesComponent != null) {
        val aesDecryptHttpClient =
            globalOkHttpClient.newBuilder().addInterceptor(AesDecryptionInterceptor(aesComponent))
                .build()
        defaultImageLoader.newBuilder().okHttpClient(aesDecryptHttpClient)
            .components(ComponentRegistry.Builder().apply {
                add(CoilLogInterceptor())
            }.build())
            .build()
    } else defaultImageLoader

    val cachedFile = defaultImageLoader.getCachedFile(url)

    val sourceRequestBuilder =
        ImageRequest.Builder(context).data(cachedFile ?: url).target(this).crossfade(true).apply(builder)
            .listener(
                onSuccess = { request, result ->
                    logInfo(TAG, "loadWithThumbnail->source image load success, url=${url}")
                    onSuccess?.invoke(request, result)
                    if (sourceImageLoader != defaultImageLoader){
                        sourceImageLoader.shutdown()
                    }
                },
                onError = { request, error ->
                    logInfo(
                        TAG, "loadWithThumbnail->source image load error, url=${url}," +
                                " errorInfo=${error.throwable.message}"
                    )
                    onError?.invoke(request, error)
                    if (sourceImageLoader != defaultImageLoader){
                        sourceImageLoader.shutdown()
                    }
                },
                onCancel = {
                    if (sourceImageLoader != defaultImageLoader){
                        sourceImageLoader.shutdown()
                    }
                }

            )

    val thumbnailRequestBuilder = if (cachedFile == null && thumbnailUrl != null && thumbnailUrl != url) {
        ImageRequest.Builder(context).data(thumbnailUrl).target(object : Target {
            override fun onSuccess(result: Drawable) {
                logInfo(
                    TAG,
                    "loadWithThumbnail->thumbnail image load success, thumbnailUrl=${thumbnailUrl}"
                )
                val currentTagValue = getTag(R.id.common_image_load_id)
                if (currentTagValue != useTagValue) return

                val sourceRequest = sourceRequestBuilder.placeholder(result).error(result).build()
                sourceImageLoader.enqueue(sourceRequest)
            }

            override fun onError(error: Drawable?) {
                logInfo(
                    TAG,
                    "loadWithThumbnail->thumbnail image load error, thumbnailUrl=${thumbnailUrl}"
                )
                sourceImageLoader.enqueue(sourceRequestBuilder.build())
            }
        }).crossfade(true).apply(builder)
    } else null

    // The thumbnail is loaded first,then loaded the source pic.
    if (thumbnailRequestBuilder != null) {
        defaultImageLoader.enqueue(thumbnailRequestBuilder.build())
    } else {
        sourceImageLoader.enqueue(sourceRequestBuilder.build())
    }

}

/**
 * 支持视频url 加载封面图
 *
 * @param url 要加载的图像的URL. The URL of the image to be loaded.
 */
fun ImageView.loadImageSupportVideo(
    url: String?,
    onSuccess: TwoParamCallback<ImageRequest, SuccessResult>? = null,
    onError: TwoParamCallback<ImageRequest, ErrorResult>? = null,
    builder: ImageRequest.Builder.() -> Unit = {}
): Disposable {
    val sourceRequestBuilder =
        ImageRequest.Builder(context).data(url).target(this).crossfade(true).apply(builder)
            .listener(
                onSuccess = { request, result ->
                    onSuccess?.invoke(request, result)
                    logInfo(TAG, "loadImageSupportVideo->image load success, url=${url}")
                },
                onError = { request, error ->
                    onError?.invoke(request, error)
                    logInfo(
                        TAG, "loadImageSupportVideo->source image load error, url=${url}," +
                                " errorInfo=${error.throwable.message}"
                    )
                }
            )

    return videoCoverImageLoader.enqueue(sourceRequestBuilder.build())
}

/**
 * 限制 ImageView 的大小，调整它的尺寸以保持在给定的最大和最小界限之间
 *
 * @param imageWidth 图片原始宽度
 * @param imageHeight 图片原始高度
 * @param maxSize 最大尺寸
 * @param minSize 最小尺寸
 */
fun ImageView.limitSize(imageWidth: Int, imageHeight: Int, maxSize: Int, minSize: Int) {
    log(
        "ImageView.limitSize",
        "imageWidth:${imageWidth},imageHeight:${imageHeight},maxSize:${maxSize},minSize:${minSize}"
    )
    var width = imageWidth
    var height = imageHeight
    if (width > maxSize || height > maxSize) {
        // 只要宽度或者高度超出最大界限,就需要缩小
        if (width < height) {
            // 如果图片的高度比较长，则以 maxSize 为界限来调整高度，并按比例来调整宽度
            // 计算要缩小的百分比
            val heightRatio = maxSize * 1f / height
            height = Math.min(maxSize, height)
            width = Math.max(minSize.toFloat(), width * heightRatio).toInt()
        } else {
            // 如果图片的宽度相对较长，则以 maxSize 为界限来调整宽度，并按比例来调整高度
            // 计算要缩小的百分比
            val widthRatio = maxSize * 1f / width
            width = Math.min(maxSize, width)
            height = Math.max(minSize.toFloat(), height * widthRatio).toInt()
        }
    } else if (width < minSize || height < minSize) {
        // 只要宽度或者高度小于最小界限,就需要放大
        if (width < height) {
            // 如果图片的宽度比较小，则以 minSize 为界限来调整宽度，并按比例来调整高度
            // 计算要放大的百分比
            val widthRatio = minSize * 1f / width
            width = Math.max(minSize, width)
            height = Math.min(maxSize.toFloat(), height * widthRatio).toInt()
        } else {
            // 如果图片的高度比较小，则以 minSize 为界限来调整高度，并按比例来调整宽度
            // 计算要放大的百分比
            val heightRatio = minSize * 1f / height
            height = Math.max(minSize, height)
            width = Math.min(maxSize.toFloat(), width * heightRatio).toInt()
        }
    }
    layoutParams = layoutParams.apply {
        this.width = width
        this.height = height
    }
}

suspend fun Context.loadImage(
    url: String,
    isAllowHardware: Boolean,
    successCallback: DefaultCallback? = null,
    errorCallback: DefaultCallback? = null
): Drawable? {
    return withContext(Dispatchers.IO) {
        val request = ImageRequest.Builder(this@loadImage)
            .data(url)
            .allowHardware(isAllowHardware)
            .listener(
                onSuccess = { _, _ -> successCallback?.invoke() },
                onError = { _, _ -> errorCallback?.invoke() }
            )
            .build()
        Coil.imageLoader(this@loadImage).execute(request).drawable
    }
}

suspend fun requestBitmap(
    url: Any,
    aesComponent: IAesComponent? = null,
    progressListener: ProgressInterceptor.ProgressListener? = null,
    builder: ImageRequest.Builder.() -> Unit = {}
): BitmapDrawable? {
    return requestDrawable(
        url = url,
        aesComponent = aesComponent,
        progressListener = progressListener,
        builder = builder
    ) as? BitmapDrawable
}

suspend fun requestDrawable(
    url: Any,
    aesComponent: IAesComponent? = null,
    progressListener: ProgressInterceptor.ProgressListener? = null,
    builder: ImageRequest.Builder.() -> Unit = {}
): Drawable? {
    val needOkHttpClient = aesComponent.isNotNull() || progressListener.isNotNull()
    val okHttpClient = globalOkHttpClient.newBuilder().apply {
        if (null != aesComponent) {
            addInterceptor(AesDecryptionInterceptor(aesComponent))
        }
        if (null != progressListener) {
            addInterceptor(ProgressInterceptor(progressListener))
        }
    }.build()

    val imageLoader = if (needOkHttpClient) {
        Coil.imageLoader(appContext).newBuilder().okHttpClient(okHttpClient).build()
    } else Coil.imageLoader(appContext)

    return imageLoader.execute(
        ImageRequest.Builder(appContext)
            .data(url)
            .apply(builder)
            .build()
    ).drawable
}

fun ImageView.setPortrait(
    url: String?,
    size: Int? = defaultPortraitSize,
) {
    if (url.isNullOrEmpty()) {
        load(defaultPortraitRes)
        return
    }
    context.imageLoader.enqueue(
        PortraitUtil.buildImageRequest(
            context = context,
            target = ImageViewTarget(this),
            url = url,
            size = size
        )
    )
}

