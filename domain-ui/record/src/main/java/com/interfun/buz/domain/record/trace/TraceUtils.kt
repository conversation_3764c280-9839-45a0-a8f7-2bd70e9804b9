package com.interfun.buz.domain.record.trace

import com.interfun.buz.common.constants.TrackConstant
import com.interfun.buz.common.utils.BuzTracker
import com.interfun.buz.domain.im.social.entity.ShareType
import com.interfun.buz.domain.im.social.entity.ShareType.*
import com.lizhi.im5.sdk.conversation.IM5ConversationType

object TraceUtils {
    /**
     * when user share the voice filter video
     */
    fun onResultShareVoiceFilterVideo(
        isPrivate: <PERSON><PERSON><PERSON>,
        targetId: String,
        source: String,
        isSuccess: Boolean,
        shareType: ShareType,
        voiceFilterId: Long?,
        failReason: String? = null,
        eventId: String?,
    ) {
        val shareTypeStr = when (shareType) {
            SYSTEM_SHARE -> "Share"
            DOWN_LOAD -> "Download"
            LINE -> "Line"
            INSTAGRAM -> "Instagram"
            WHATSAPP -> "WhatsApp"
            TELEGRAM -> "Telegram"
            MESSENGER -> "Messenger"
            MESSAGE -> "Message"
            SNAPCHAT -> "Snapchat"
            TIKTOK -> "TikTok"
            X -> "X"
            DISCORD -> "Discord"
            VIBER -> "Viber"
            FACEBOOK -> "Facebook"
            else -> null
        }
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2025061902")
            put(TrackConstant.KEY_RESULT_TYPE, "voice_filter_share_result")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, if (isPrivate) "private" else "group")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, targetId)
            put(TrackConstant.KEY_SOURCE, source)
            eventId?.let { eventId ->
                put(TrackConstant.KEY_CONTENT_ID, eventId)
            }
            put(TrackConstant.KEY_IS_SUCCESS, if (isSuccess) "success" else "fail")
            shareTypeStr?.let {
                put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, it.toString())
            }
            voiceFilterId?.let {
                put(TrackConstant.KEY_ELEMENT_BUSINESS_ID, it.toString())
            }
            failReason?.let {
                put(TrackConstant.KEY_FAIL_REASON, it)
            }
        }
    }

    fun onResultRB2025071002(
        source: String,
        recordVoiceConvType: IM5ConversationType,
        currentPlayVoiceConvType: IM5ConversationType,
        atCurrentTarget: Boolean
    ) {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2025071002")
            put(TrackConstant.KEY_RESULT_TYPE, "record_voicemsg_conflict_with_playing_result")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_SOURCE, source)
            put(
                TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT,
                if (currentPlayVoiceConvType == IM5ConversationType.PRIVATE) "private" else "group"
            )
            put(
                TrackConstant.KEY_ELEMENT_BUSINESS_PAGE,
                if (atCurrentTarget) "same" else "different"
            )
            put(
                TrackConstant.KEY_ELEMENT_BUSINESS_ID,
                if (recordVoiceConvType == IM5ConversationType.PRIVATE) "private" else "group"
            )
        }
    }
}