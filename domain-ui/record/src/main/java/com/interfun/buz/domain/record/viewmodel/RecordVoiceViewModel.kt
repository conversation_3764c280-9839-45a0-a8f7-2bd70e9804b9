package com.interfun.buz.domain.record.viewmodel

import android.Manifest
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.interfun.buz.base.ktx.*
import com.interfun.buz.base.utils.VibratorUtil
import com.interfun.buz.base.widget.area.IArea
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.audio.AudioManagerHelper
import com.interfun.buz.common.bean.HomeWTItemType
import com.interfun.buz.common.bean.voicecall.ChannelType
import com.interfun.buz.common.constants.JUMP_INFO
import com.interfun.buz.common.constants.RecordingConstant
import com.interfun.buz.common.constants.RouterParamKey
import com.interfun.buz.common.ktx.toastLeaveCurrentVoiceCall
import com.interfun.buz.common.manager.chat.ChannelPendStatusManager
import com.interfun.buz.common.service.ChatService
import com.interfun.buz.common.service.RealTimeCallService
import com.interfun.buz.core.widget_record.entity.RecordAreaType
import com.interfun.buz.core.widget_record.entity.RecordOperateStatus
import com.interfun.buz.domain.record.R
import com.interfun.buz.domain.record.entity.*
import com.interfun.buz.domain.record.trace.TraceUtils
import com.interfun.buz.im.ktx.getConversationId
import com.interfun.buz.onair.standard.IGlobalOnAirController
import com.interfun.buz.social.repo.GroupRepository
import com.interfun.buz.social.repo.UserRepository
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * <AUTHOR>
 * @date 2024/11/14
 * @desc
 */
@HiltViewModel
class RecordVoiceViewModel @Inject constructor(
    private val userRepository: UserRepository,
    private val groupRepository: GroupRepository,
    private val savedStateHandle: SavedStateHandle
): ViewModel() {

    companion object {
        const val TAG = "RecordVoiceViewModel"
    }
    // 从聊天页面的 JumpInfo 中获取参数
    private val jumpInfo = savedStateHandle.get<Any>(RouterParamKey.Chat.JUMP_INFO)
    private val isHome = jumpInfo == null

    private val chatServiceImpl get() = routerServices<ChatService>().value
    //理论上应该将录音逻辑放到repository里面，然后各自的页面自行处理自行的UI业务逻辑，
    //但是这里都放到viewModel来共享数据以及逻辑了，这期这部分内容不是重点，先简单改一下，使用view来桥接两个viewmodel通信
    private val currentTargetFlow = MutableStateFlow<Pair<Long, HomeWTItemType>?>(null)
    private val _recordingInfoFlow = MutableStateFlow<RecordInfo?>(null)
    val recordingInfoFlow = _recordingInfoFlow.asStateFlow()

    private val currentItemEnableTypeFlow =
        currentTargetFlow.flatMapLatest { targetPair ->
            val (targetId, itemType) = targetPair
                ?: return@flatMapLatest flowOf(RecordBtnEnableType.DisableUnSpeakable)
            when (itemType) {
                HomeWTItemType.AddBtn -> flowOf(RecordBtnEnableType.DisableInAddBtn)
                HomeWTItemType.User -> userEnableTypeFlow(targetId)
                HomeWTItemType.Group -> groupEnableTypeFlow(targetId)
            }
        }

    val recordEnableStateFlow = combine(
        chatServiceImpl?.getWtSwitchFlow() ?: emptyFlow(),
        chatServiceImpl?.isPlayingFlow ?: emptyFlow(),
        currentItemEnableTypeFlow
    ) { isWtOn, isPlaying, currentItemEnableType ->
        logDebug(TAG, "isRecordEnableStateFlow isPlaying:$isPlaying")
        if (!isWtOn) return@combine RecordBtnEnableType.DisableIsWTOff
        if (isPlaying) return@combine RecordBtnEnableType.DisableIsPlaying.also {
            logLineWarn(TAG, logLine = LogLine.VOICE_MSG_RECORD, "recordEnableState: $it")
        }
        return@combine currentItemEnableType.also {
            if (it != RecordBtnEnableType.Enable) {
                logLineWarn(TAG, logLine = LogLine.VOICE_MSG_RECORD, "recordEnableState: $it")
            }
        }
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.Companion.Eagerly,
        initialValue = RecordBtnEnableType.DisableIdle
    )

    val isRecordingFlow =
        _recordingInfoFlow.map { it != null }.stateIn(viewModelScope, SharingStarted.Lazily, _recordingInfoFlow.value != null)

    private val _currentPressAreaFlow = MutableStateFlow(RecordAreaType.None)
    val currentPressAreaFlow = _currentPressAreaFlow.asStateFlow()

    private val _recordStatusSharedFlow: MutableSharedFlow<RecordStatusType> = MutableSharedFlow()
    val recordStatusSharedFlow = _recordStatusSharedFlow.asSharedFlow()

    private val _recordBgStateFlow = MutableStateFlow(RecordBgType.Normal)
    val recordBgStateFlow = _recordBgStateFlow.asStateFlow()

    private val _areaMapStateFlow: MutableStateFlow<Map<RecordAreaType, IArea>> =
        MutableStateFlow(emptyMap())

    val lastStartRecordTime = MutableStateFlow(0L)

    val recordOperateStatusFlow = combine(
        recordEnableStateFlow,
        _recordStatusSharedFlow.onStart { emit(RecordStatusType.Default) },
    ) { enableType, pressState ->
        RecordOperateStatus(
            isEnable = enableType == RecordBtnEnableType.Enable,
            isPressing = pressState is RecordStatusType.Pressed,
            isLocking = pressState is RecordStatusType.Locked,
        )
    }

    val isLockingFlow = recordOperateStatusFlow.map { it.isLocking }

    val isRecordingOrLockingFlow = combine(
        isRecordingFlow,
        isLockingFlow
    ) { isRecording, isLocking ->
        isRecording || isLocking
    }

    init {
        viewModelScope.launch {
            combine(
                recordEnableStateFlow,
                _recordStatusSharedFlow
            ) { enableType, pressState ->
                enableType != RecordBtnEnableType.Enable && pressState is RecordStatusType.Pressed
            }.stateIn(
                scope = viewModelScope,
                started = SharingStarted.Companion.Eagerly,
                initialValue = false
            ).collect {
                if (it) {
                    // 当按下时触发不可录制状态，取消录制
                    logLineInfo(TAG, LogLine.VOICE_MSG_RECORD,"cancel press when record is disable")
                    _recordStatusSharedFlow.emit(
                        RecordStatusType.Release(reason = ReleaseReason.USER_CANCEL)
                    )
                }
            }
        }
    }

    fun updateCurrentTarget(pair: Pair<Long, HomeWTItemType>?) {
        currentTargetFlow.value = pair
    }

    fun onStartRecording(recordInfo: RecordInfo) {
        _recordingInfoFlow.value = recordInfo
    }

    fun onStopRecording() {
        _recordingInfoFlow.value = null
    }

    fun onRecordPressDown(){
        _currentPressAreaFlow.value = RecordAreaType.None
        launch {
            _recordStatusSharedFlow.emit(RecordStatusType.Pressed())
        }
    }

    fun onRecordLocked() {
        _currentPressAreaFlow.value = RecordAreaType.None
        launch {
            _recordStatusSharedFlow.emit(RecordStatusType.Locked())
        }
    }

    fun onRecordRelease(reason: ReleaseReason) {
        _currentPressAreaFlow.value = RecordAreaType.None
        launch {
            _recordStatusSharedFlow.emit(RecordStatusType.Release(reason))
        }
    }

    fun updateCurrentPressArea(type: RecordAreaType)  {
        _currentPressAreaFlow.value = type
        if (type == RecordAreaType.Cancel || type == RecordAreaType.Preview || type == RecordAreaType.Lock) {
            VibratorUtil.vibratorMaxAmplitude(from = "$TAG isInArea: $type")
        }
    }

    fun updateRecordBgType(type: RecordBgType) {
        _recordBgStateFlow.value = type
    }

    fun getArea(type: RecordAreaType): IArea {
        return _areaMapStateFlow.value[type] ?: IArea.Empty
    }

    fun updateArea(type: RecordAreaType, newArea: IArea) {
        val newMap = _areaMapStateFlow.value.toMutableMap()
        newMap[type] = newArea
        _areaMapStateFlow.value = newMap
    }

    fun updateLastStartRecordTime() {
        lastStartRecordTime.value = System.currentTimeMillis()
    }

    private fun userEnableTypeFlow(targetId: Long) =
        userRepository.getUserCompositeFlow(targetId).map { user ->
            if (user.isFriend || user.isMe) {
                RecordBtnEnableType.Enable
            } else {
                RecordBtnEnableType.DisableUnSpeakable
            }
        }

    private fun groupEnableTypeFlow(targetId: Long) =
        groupRepository.getGroupCompositeFlow(targetId).map { group ->
            if (group.buzGroupExtra?.isInGroup == true) {
                RecordBtnEnableType.Enable
            } else {
                RecordBtnEnableType.DisableUnSpeakable
            }
        }

    fun checkEnableAndToastWhenDisable(
        isRecordEnable: Boolean,
        onRequestRecordPermission: () -> Unit,
    ): Boolean {
        if (!isRecordEnable) {
            logLineWarn(TAG, LogLine.VOICE_MSG_RECORD, "isRecordEnable false return")
            onClickRecordWhenNotEnabled()
            return false
        }
        val voiceCallService = routerServices<RealTimeCallService>().value
        val isOnRealTimeCall = voiceCallService?.isOnRealTimeCall() == true
        val isBeingCall = ChannelPendStatusManager.isBeingCall
        val isInOnAir = routerServices<IGlobalOnAirController>().value?.isInOnAir() == true
        val channelType = ChannelPendStatusManager.statusFlow.value.second?.channelType ?: 0
        if (isOnRealTimeCall || (isBeingCall && ChannelType.Companion.isVoiceCallType(channelType))) {
            toastLeaveCurrentVoiceCall()
            logLineWarn(TAG, LogLine.VOICE_MSG_RECORD,"isOnRealTimeCall return")
            return false
        }

        if (isInOnAir || (isBeingCall && ChannelType.Companion.isLivePlaceType(channelType)) ) {
            toast(R.string.exit_current_call_and_try.asString())
            logLineWarn(TAG, LogLine.VOICE_MSG_RECORD,"isInOnAir return")
            return false
        }
        if (AudioManagerHelper.isCommunicationMode()) {
            RecordingConstant.prohibitedRecording("1")
            toast(R.string.mic_already_in_use_tip.asString())
            logLineWarn(TAG, LogLine.VOICE_MSG_RECORD,"isCommunicationMode return")
            return false
        }
        if (!isPermissionGranted(Manifest.permission.RECORD_AUDIO)) {
            onRequestRecordPermission.invoke()
            logLineWarn(TAG, LogLine.VOICE_MSG_RECORD, "is no 'RECORD_AUDIO' permission return")
            return false
        }
        return true
    }

    fun onClickRecordWhenNotEnabled() {
        when (recordEnableStateFlow.value) {
            RecordBtnEnableType.DisableInAddBtn -> {
                cancelToast()
                if ((chatServiceImpl?.getHomeListSize()) == 1) {
                    R.string.chat_add_friend_selected_ptt_tips_1.toast()
                } else {
                    R.string.chat_add_friend_selected_ptt_tips_2.toast()
                }
            }

            RecordBtnEnableType.DisableIsPlaying -> {
                reportDisableTouchEvent()
            }

            else -> {}
        }
    }

    fun reportDisableTouchEvent() {
        viewModelScope.launch {
            val recordVoiceConvType = when (
                currentTargetFlow.value?.second
            ) {
                HomeWTItemType.User -> {
                    IM5ConversationType.PRIVATE
                }

                else -> {
                    IM5ConversationType.GROUP
                }
            }

            val targetId = (currentTargetFlow.value?.first ?: 0L).toString()
            val globalPlayMsg = chatServiceImpl?.getGlobalPlayedMsgFlow()?.first()?.iMessage

            val currentPlayVoiceConvType = globalPlayMsg?.conversationType
                ?: IM5ConversationType.PRIVATE
            val atTargetPosition = globalPlayMsg?.getConversationId() == targetId

            logDebug(TAG,"atTargetPosition=${atTargetPosition}," +
                    "globalPlayMsg?.targetId=${globalPlayMsg?.targetId}," +
                    "targetId=${targetId}")

            TraceUtils.onResultRB2025071002(
                source = if (isHome) "homepage" else "chat_history",
                recordVoiceConvType = recordVoiceConvType,
                currentPlayVoiceConvType = currentPlayVoiceConvType,
                atCurrentTarget = atTargetPosition
            )
        }
    }
}