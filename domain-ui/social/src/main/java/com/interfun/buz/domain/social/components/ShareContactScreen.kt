package com.interfun.buz.domain.social.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.interfun.buz.base.ktx.DefaultCallback
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.base.ktx.doOnLifecycle
import com.interfun.buz.common.R
import com.interfun.buz.compose.components.CommonButton
import com.interfun.buz.compose.components.CommonButtonType
import com.interfun.buz.compose.components.InitPreview
import com.interfun.buz.compose.components.PortraitImage
import com.interfun.buz.compose.components.bottomsheet.BuzModalBottomSheet
import com.interfun.buz.compose.components.bottomsheet.buzSheetDraggable
import com.interfun.buz.compose.components.bottomsheet.rememberBuzModalBottomSheetState
import com.interfun.buz.compose.ktx.HorizontalSpace
import com.interfun.buz.compose.ktx.VerticalSpace
import com.interfun.buz.compose.ktx.getScreenHeight
import com.interfun.buz.compose.styles.TextStyles
import com.interfun.buz.domain.im.social.entity.ShareType
import com.interfun.buz.domain.im.social.entity.UserType
import com.interfun.buz.domain.social.bean.ChatShareItem
import com.interfun.buz.domain.social.bean.ShareState
import com.interfun.buz.domain.social.bean.displayBuzId
import com.interfun.buz.domain.social.viewmodel.ShareContactViewModel
import com.yibasan.lizhifm.sdk.platformtools.ResUtil
import kotlinx.collections.immutable.persistentListOf

/**
 * Author: ChenYouSheng
 * Date: 2025/7/3
 * Email: <EMAIL>
 * Desc: 分享群链接和个人资料链接到第三方平台 或者 以名片的方式分享到聊天页
 */

@Composable
fun ShareContactScreen(
    viewModel: ShareContactViewModel = hiltViewModel(),
    onDismissRequest: DefaultCallback = {},
) {
    val uiState by viewModel.uiStateFlow.collectAsStateWithLifecycle()
    ScreenRoot(uiState)

    val lifecycleOwner = LocalLifecycleOwner.current
    LaunchedEffect(Unit) {
        viewModel.eventToUIFlow.collect { event ->
            if (event is ShareContactViewModel.EventToUI.CloseScreen) {
                onDismissRequest.invoke()
            }
        }
    }
    LaunchedEffect(lifecycleOwner) {
        lifecycleOwner.doOnLifecycle(onResume = {
            uiState.uiEvent.invoke(ShareContactViewModel.UIEvent.OnScreenExposed)
        })
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ScreenRoot(uiState: ShareContactViewModel.UiState) {
    val sheetState = rememberBuzModalBottomSheetState()
    val maxHeight = getScreenHeight() * 0.75f
    Box(modifier = Modifier.fillMaxSize()) {
        BuzModalBottomSheet(
            containerColor = colorResource(R.color.color_background_2_default),
            shape = RoundedCornerShape(topStart = 30.dp, topEnd = 30.dp),
            onDismissRequest = { uiState.uiEvent.invoke(ShareContactViewModel.UIEvent.CloseScreen) },
            sheetState = sheetState,
            statusBarLightMode = false,
            navBarLightMode = false,
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .heightIn(min = maxHeight, max = maxHeight)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(colorResource(R.color.color_background_2_default)),
            ) {
                TitleBar(
                    modifier = Modifier
                        .fillMaxWidth()
                        .buzSheetDraggable(sheetState)
                )
                Box(modifier = Modifier.weight(1f)) {
                    ShareContentList(uiState = uiState)
                }
            }
        }
    }
}

@Composable
private fun ShareContentList(uiState: ShareContactViewModel.UiState) {
    LazyColumn(
        modifier = Modifier.fillMaxWidth()
    ) {
        // 第三方分享
        item {
            val context = LocalContext.current
            HorizontalShareListScreenNew(
                list = uiState.thirdPartyShareList,
                modifier = Modifier.fillMaxWidth(),
                addBorder = false,
                onItemClick = { shareItem ->
                    uiState.uiEvent.invoke(
                        ShareContactViewModel.UIEvent.ShareLinkToPlatform(
                            context = context,
                            shareItem = shareItem
                        )
                    )
                })
        }


        // Recent Chats
        item {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(48.dp)
                    .background(color = colorResource(id = R.color.color_background_2_default))
                    .padding(horizontal = 20.dp), contentAlignment = Alignment.CenterStart
            ) {
                Text(
                    text = R.string.home_search_recent_chats.asString(),
                    color = colorResource(R.color.text_white_secondary),
                    style = TextStyles.bodyMedium()
                )
            }
        }

        items(uiState.recentChatList.size) { index ->
            val item = uiState.recentChatList[index]
            RecentChatItem(item = item, onSendClick = {
                uiState.uiEvent.invoke(
                    ShareContactViewModel.UIEvent.ShareContactToChat(item)
                )
            })
        }
    }
}

@Composable
private fun TitleBar(modifier: Modifier = Modifier) {
    Box(modifier = modifier) {
        Text(
            text = stringResource(R.string.contact_card_share_to),
            style = TextStyles.titleSmall(),
            color = Color.White,
            textAlign = TextAlign.Center,
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 13.5.dp, bottom = 31.5.dp)
        )
    }
}

@Composable
private fun RecentChatItem(
    modifier: Modifier = Modifier,
    item: ChatShareItem,
    onSendClick: DefaultCallback = {},
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .heightIn(70.dp)
            .background(color = colorResource(R.color.color_background_2_default))
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(start = 20.dp, top = 10.dp, bottom = 10.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Portrait Image
            if (item is ChatShareItem.GroupShareItem) {
                PortraitImage(
                    urlList = item.firstFewPortraits, modifier = Modifier.size(50.dp)
                )
            } else if (item is ChatShareItem.UserShareItem) {
                PortraitImage(
                    url = item.userPortrait, modifier = Modifier.size(50.dp)
                )
            }

            // Space between image and text
            HorizontalSpace(16.dp)

            // Column for Text Content
            Column(
                modifier = Modifier.weight(1f), verticalArrangement = Arrangement.Center
            ) {

                val content = when (item) {
                    is ChatShareItem.GroupShareItem -> item.groupName
                    is ChatShareItem.UserShareItem -> item.userName
                }

                val desc = when (item) {
                    is ChatShareItem.GroupShareItem -> {
                        if (item.groupMemberCount == 1) {
                            R.string.contact_card_one_memeber.asString()
                        } else if (item.groupMemberCount > 1) {
                            ResUtil.getString(
                                R.string.contact_card_memebers,
                                item.groupMemberCount
                            )
                        } else ""
                    }

                    is ChatShareItem.UserShareItem -> item.displayBuzId()
                }

                // Title
                Text(
                    text = content,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis,
                    style = TextStyles.labelLarge(),
                    color = colorResource(com.interfun.buz.compose.R.color.text_white_main)
                )

                // Description
                if (desc.isNotEmpty()) {
                    VerticalSpace(2.dp)
                    Text(
                        text = desc,
                        maxLines = 3,
                        overflow = TextOverflow.Ellipsis,
                        style = TextStyles.bodyMedium(),
                        color = colorResource(R.color.text_white_secondary)
                    )
                }
            }

            HorizontalSpace(10.dp)

            // Send Button
            Box(
                modifier = Modifier.defaultMinSize(minWidth = 60.dp, minHeight = 32.dp)
                    .height(32.dp)
                    .wrapContentWidth(),
                contentAlignment = Alignment.Center
            ) {
                if (item.shareState == ShareState.Sent) {
                    Text(
                        text = R.string.live_place_share_sent.asString(),
                        modifier = Modifier.wrapContentSize(Alignment.Center),
                        style = TextStyles.labelMedium(),
                        color = colorResource(R.color.color_foreground_neutral_important_disable),
                        textAlign = TextAlign.Center
                    )
                } else {
                    CommonButton(
                        modifier = Modifier.alpha(if (item.shareState == ShareState.Normal) 1f else 0.3f),
                        type = CommonButtonType.TERTIARY_SMALL,
                        text = R.string.live_place_share_send.asString(),
                        textPadding = 10.dp,
                        showLoading = item.shareState == ShareState.Loading,
                        loadingColor = colorResource(com.interfun.buz.compose.R.color.white),
                        onClick = {
                            if (item.shareState != ShareState.Normal) {
                                return@CommonButton
                            }
                            onSendClick()
                        },
                        enable = item.shareState == ShareState.Normal
                    )
                }
            }

            HorizontalSpace(20.dp)
        }
    }
}

@Composable
@Preview
private fun PreviewContent() {
    InitPreview()
    ShareContentList(
        uiState = ShareContactViewModel.UiState(
            thirdPartyShareList = persistentListOf(
                ShareType.SYSTEM_SHARE,
                ShareType.COPY_LINK,
                ShareType.LINE,
                ShareType.INSTAGRAM,
                ShareType.WHATSAPP,
                ShareType.TELEGRAM,
                ShareType.MESSENGER,
                ShareType.MESSAGE,
                ShareType.TIKTOK,
                ShareType.X,
                ShareType.DISCORD,
                ShareType.VIBER,
                ShareType.SNAPCHAT,
                ShareType.FACEBOOK,
            ),
            recentChatList = persistentListOf(
                ChatShareItem.GroupShareItem(
                    groupId = 123213213L,
                    groupName = "testGroup",
                    groupPortrait = "",
                    firstFewPortraits = emptyList(),
                    groupMemberCount = 10,
                    shareState = ShareState.Normal,
                ),
                ChatShareItem.UserShareItem(
                    userId = 123213213L,
                    userName = "Night",
                    displayName = "Night",
                    realFullName = "Night",
                    userPortrait = "",
                    userType = UserType.Normal,
                    buzId = "nightfox",
                    shareState = ShareState.Loading,
                ),
                ChatShareItem.UserShareItem(
                    userId = 1113213213L,
                    userName = "Buz Research",
                    displayName = "Buz Research",
                    realFullName = "Buz Research",
                    userPortrait = "",
                    userType = UserType.UserResearch,
                    buzId = "buz0003926",
                    shareState = ShareState.Sent,
                ),
                ChatShareItem.UserShareItem(
                    userId = 1113213213L,
                    userName = "Buz Official",
                    displayName = "Buz Official",
                    realFullName = "Buz Official",
                    userPortrait = "",
                    userType = UserType.Official,
                    buzId = "buzofficialaccount",
                ),
                ChatShareItem.UserShareItem(
                    userId = 1113213213L,
                    userName = "Translator",
                    displayName = "Translator",
                    realFullName = "Translator",
                    userPortrait = "",
                    userType = UserType.AIBot,
                    buzId = "buz00005258",
                )
            ),
            uiEvent = {}
        )
    )
}