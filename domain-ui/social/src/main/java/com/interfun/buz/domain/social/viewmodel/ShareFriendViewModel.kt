package com.interfun.buz.domain.social.viewmodel

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.interfun.buz.common.constants.RouterParamKey
import com.interfun.buz.domain.im.social.usecase.ShareContactCardUserCase
import com.interfun.buz.domain.social.bean.ShareContactInfo
import com.interfun.buz.domain.social.utils.SocialTracker
import com.interfun.buz.social.entity.SelectableUser
import com.interfun.buz.social.entity.SimpleBuzUser
import com.interfun.buz.social.repo.SearchFriendRepository
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.collections.immutable.PersistentList
import kotlinx.collections.immutable.toPersistentList
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Author: ChenYouSheng
 * Date: 2025/7/3
 * Email: <EMAIL>
 * Desc: 将你的好友分享给其他人或者群
 */
@HiltViewModel
class ShareFriendViewModel @Inject constructor(
    private val shareContactCardUserCase: ShareContactCardUserCase,
    private val searchFriendRepository: SearchFriendRepository,
    private val savedStateHandle: SavedStateHandle,
) : ViewModel() {

    companion object {
        const val TAG = "ShareContactViewModel"
    }

    private val shareContactInfo =
        savedStateHandle.get<ShareContactInfo>(RouterParamKey.ShareContact.KEY_SHARE_INFO)
            ?: throw IllegalArgumentException("shareContactInfo is null")

    private val uiEvent: (UIEvent) -> Unit = {
        handleUIEvent(it)
    }

    val eventToUIFlow = MutableSharedFlow<EventToUI>()

    data class UiState(
        val isInSearch: Boolean = false,
        val selectableList: PersistentList<SelectableUser>?,
        val selectedSize: Int,
        val uiEvent: (UIEvent) -> Unit
    )

    sealed interface UIEvent {
        data object OnScreenExposed : UIEvent
        data object CloseScreen : UIEvent
        data class SelectFriend(val userId: Long) : UIEvent
        data class UnSelectFriend(val userId: Long) : UIEvent
        data class SubmitSelection(val user: SimpleBuzUser?) : UIEvent
        data class SearchKeywordUpdate(val keyword: CharSequence) : UIEvent
        data object ClickSearchBar : UIEvent
    }

    sealed interface EventToUI {
        data object CloseScreen : EventToUI
    }

    val uiStateFlow = searchFriendRepository.getSelectableFriendListFlow().map {
        UiState(
            isInSearch = it.isInSearch,
            selectableList = it.selectableList.toPersistentList(),
            selectedSize = it.selectedSize,
            uiEvent = uiEvent
        )
    }.stateIn(
        viewModelScope,
        SharingStarted.WhileSubscribed(5000L),
        UiState(isInSearch = false, selectableList = null, selectedSize = 0, uiEvent = uiEvent)
    )

    private fun handleUIEvent(event: UIEvent) {
        when (event) {
            is UIEvent.CloseScreen -> closeScreen()
            is UIEvent.SelectFriend -> selectFriend(event.userId)
            is UIEvent.UnSelectFriend -> unSelectFriend(event.userId)
            is UIEvent.SubmitSelection -> {
                submitSelection(event.user)
            }

            is UIEvent.SearchKeywordUpdate -> search(event.keyword)
            UIEvent.OnScreenExposed -> {}
            UIEvent.ClickSearchBar -> {}
        }
    }

    private fun closeScreen() {
        viewModelScope.launch {
            eventToUIFlow.emit(EventToUI.CloseScreen)
        }
    }

    private fun search(keyword: CharSequence) {
        viewModelScope.launch {
            searchFriendRepository.searchFriend(keyword.toString())
        }
    }

    private fun submitSelection(user: SimpleBuzUser?) {
        viewModelScope.launch {
            val flow = shareContactCardUserCase(
                fromId = user?.userId ?: 0,
                isGroupContact = false,
                targetId = shareContactInfo.id,
                convType = when (shareContactInfo.type) {
                    ShareContactInfo.Companion.TargetType.Group -> IM5ConversationType.GROUP
                    ShareContactInfo.Companion.TargetType.User -> IM5ConversationType.PRIVATE
                }
            )
            unSelectFriend(user?.userId ?: 0)
            eventToUIFlow.emit(EventToUI.CloseScreen)
        }
        if (null != user) {
            SocialTracker.onClickAC2025071002(
                isPrivate = shareContactInfo.type == ShareContactInfo.Companion.TargetType.User,
                targetId = shareContactInfo.id,
                source = shareContactInfo.source
            )
        }
    }

    private fun selectFriend(userId: Long) {
        searchFriendRepository.selectFriend(userId)
    }

    private fun unSelectFriend(userId: Long) {
        searchFriendRepository.unSelectFriend(userId)
    }

}

