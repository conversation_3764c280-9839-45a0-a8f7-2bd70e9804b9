package com.interfun.buz.domain.social.components

import androidx.compose.foundation.background
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.PressInteraction
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.input.rememberTextFieldState
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.interfun.buz.base.ktx.DefaultCallback
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.base.ktx.doOnLifecycle
import com.interfun.buz.common.R
import com.interfun.buz.common.widget.view.EmptyDataType
import com.interfun.buz.compose.components.*
import com.interfun.buz.compose.components.bottomsheet.BuzModalBottomSheet
import com.interfun.buz.compose.components.bottomsheet.rememberBuzModalBottomSheetState
import com.interfun.buz.compose.ktx.VerticalSpace
import com.interfun.buz.compose.ktx.getStatusBarHeightWithoutInsets
import com.interfun.buz.compose.modifier.debouncedClickable
import com.interfun.buz.compose.styles.TextStyles
import com.interfun.buz.domain.social.viewmodel.ShareFriendViewModel
import com.interfun.buz.social.entity.SelectableUser
import com.interfun.buz.social.entity.SimpleBuzUser
import kotlinx.collections.immutable.persistentListOf
import kotlinx.coroutines.flow.filter

/**
 * Author: ChenYouSheng
 * Date: 2025/7/9
 * Email: <EMAIL>
 * Desc: 将好友分享到聊天
 */
@Composable
fun ShareFriendScreen(
    viewModel: ShareFriendViewModel = hiltViewModel(), onDismissRequest: DefaultCallback
) {
    val uiState by viewModel.uiStateFlow.collectAsStateWithLifecycle()
    ScreenRoot(uiState)

    val lifecycleOwner = LocalLifecycleOwner.current
    LaunchedEffect(Unit) {
        viewModel.eventToUIFlow.collect { event ->
            if (event is ShareFriendViewModel.EventToUI.CloseScreen) {
                onDismissRequest.invoke()
            }
        }
    }
    LaunchedEffect(lifecycleOwner) {
        lifecycleOwner.doOnLifecycle(onResume = {
            uiState.uiEvent.invoke(ShareFriendViewModel.UIEvent.OnScreenExposed)
        })
    }
}

@Composable
@OptIn(ExperimentalMaterial3Api::class)
fun ScreenRoot(uiState: ShareFriendViewModel.UiState) {
    val sheetState = rememberBuzModalBottomSheetState()
    val statusBarHeight = getStatusBarHeightWithoutInsets()
    BuzModalBottomSheet(
        containerColor = colorResource(R.color.color_background_2_default),
        shape = RoundedCornerShape(topStart = 30.dp, topEnd = 30.dp),
        onDismissRequest = { uiState.uiEvent.invoke(ShareFriendViewModel.UIEvent.CloseScreen) },
        sheetState = sheetState,
        statusBarLightMode = false,
        navBarLightMode = false,
        modifier = Modifier
            .fillMaxSize()
            .padding(top = statusBarHeight)

    ) {
        ShareContentList(uiState)
    }
}


@Composable
private fun ShareContentList(
    uiState: ShareFriendViewModel.UiState
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(colorResource(R.color.color_background_2_default)),
    ) {
        val hasNoFriends =
            !uiState.isInSearch && uiState.selectableList != null && uiState.selectableList.isEmpty()

        TitleBar(uiState = uiState)
        if (hasNoFriends) {
            EmptyView(
                emptyType = EmptyDataType.NO_CONTACT,
                desc = R.string.no_related_friends.asString()
            )
        } else if (uiState.selectableList != null) {
            SearchInputField(uiState = uiState)
            if (uiState.isInSearch && uiState.selectableList.isEmpty()) {
                EmptyView(
                    emptyType = EmptyDataType.NO_CONTACT,
                    desc = R.string.no_results_found.asString()
                )
            } else {
                SelectableList(allUsers = uiState.selectableList, uiEvent = uiState.uiEvent)
            }
        }
    }
}


@Composable
private fun TitleBar(
    modifier: Modifier = Modifier, uiState: ShareFriendViewModel.UiState
) {
    val isSelected = uiState.selectedSize > 0
    val shareIconColor = if (isSelected) colorResource(R.color.color_foreground_highlight_default)
    else colorResource(R.color.color_foreground_highlight_disable)
    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(64.dp), contentAlignment = Alignment.Center
    ) {
        // 关闭按钮
        IconFontText(
            modifier = Modifier
                .debouncedClickable {
                    uiState.uiEvent.invoke(ShareFriendViewModel.UIEvent.CloseScreen)
                }
                .align(Alignment.CenterStart)
                .padding(start = 20.dp),
            iconRes = R.string.ic_exit,
            iconColor = colorResource(R.color.color_foreground_neutral_important_default),
        )

        // 中间标题
        Text(
            text = stringResource(R.string.contact_card_select_contact),
            style = TextStyles.titleMedium(),
            color = colorResource(R.color.color_foreground_neutral_important_default),
            textAlign = TextAlign.Center
        )

        // 分享按钮
        Text(
            modifier = Modifier
                .debouncedClickable {
                    if (isSelected) {
                        val selectUser = uiState.selectableList?.firstOrNull { it.isSelected }
                        uiState.uiEvent.invoke(
                            ShareFriendViewModel.UIEvent.SubmitSelection(selectUser?.user)
                        )
                    }
                }
                .align(Alignment.CenterEnd)
                .padding(end = 20.dp),
            text = stringResource(R.string.contact_card_share),
            style = TextStyles.labelLarge(),
            color = shareIconColor)
    }
}

@Composable
private fun SearchInputField(modifier: Modifier = Modifier, uiState: ShareFriendViewModel.UiState) {
    val interactionSource = remember { MutableInteractionSource() }
    val searchState = rememberTextFieldState("")
    val inputFlow = snapshotFlow { searchState.text }
    LaunchedEffect(Unit) {
        inputFlow.collect {
            uiState.uiEvent.invoke(ShareFriendViewModel.UIEvent.SearchKeywordUpdate(it))
        }
    }
    SearchInputField(
        modifier = modifier.padding(bottom = 10.dp, start = 20.dp, end = 20.dp),
        hilt = stringResource(R.string.search),
        state = searchState,
        interactionSource = interactionSource
    )
    LaunchedEffect(Unit) {
        interactionSource.interactions.filter { it is PressInteraction.Release }.collect {
            when (it) {
                is PressInteraction.Release -> {
                    uiState.uiEvent.invoke(ShareFriendViewModel.UIEvent.ClickSearchBar)
                }
            }
        }
    }
}

@Composable
private fun SelectableList(
    allUsers: List<SelectableUser>, uiEvent: (ShareFriendViewModel.UIEvent) -> Unit
) {
    val keyboardController = LocalSoftwareKeyboardController.current
    val listState = rememberLazyListState()
    LaunchedEffect(listState.isScrollInProgress) {
        if (listState.isScrollInProgress) {
            keyboardController?.hide()
        }
    }
    LazyColumn(
        modifier = Modifier.fillMaxSize(), state = listState
    ) {
        items(allUsers, key = { it.user.userId }) { item ->
            SelectableUserItem(
                modifier = Modifier
                    .animateItem()
                    .debouncedClickable {
                        if (item.isSelected) {
                            uiEvent.invoke(ShareFriendViewModel.UIEvent.UnSelectFriend(item.user.userId))
                        } else {
                            uiEvent.invoke(ShareFriendViewModel.UIEvent.SelectFriend(item.user.userId))
                        }
                    }, item = item
            )
        }
    }
}

@Composable
private fun SelectableUserItem(modifier: Modifier = Modifier, item: SelectableUser) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .height(64.dp)
            .padding(horizontal = 20.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        PortraitImage(
            url = item.user.portrait, modifier = Modifier.size(48.dp)
        )
        val color = if (item.isSelected) {
            R.color.color_text_highlight_default
        } else {
            R.color.color_foreground_neutral_important_default
        }
        val text = buildAnnotatedString {
            this.append(item.user.displayName)
            if (item.keywordIndex != null) {
                item.keywordIndex?.forEach { range ->
                    this.addStyle(
                        SpanStyle(color = colorResource(R.color.color_text_highlight_default)),
                        range.start,
                        range.endInclusive
                    )
                }
            }
        }
        Column(
            modifier = Modifier
                .weight(1f)
                .padding(start = 16.dp)
        ) {
            Text(
                modifier = Modifier.wrapContentSize(),
                text = text,
                style = TextStyles.labelLarge(),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                color = colorResource(color),
            )
            if (item.user.buzId.isNotEmpty()) {
                VerticalSpace(2.dp)
                Text(
                    modifier = Modifier.wrapContentSize(),
                    text = item.user.buzId,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    style = TextStyles.bodyMedium(),
                    color = colorResource(R.color.text_white_secondary)
                )
            }
        }
        CheckBox(
            modifier = Modifier.padding(start = 16.dp),
            isChecked = item.isSelected,
        )
    }
}

@Composable
@Preview
private fun PreviewContent() {
    ShareContentList(
        uiState = ShareFriendViewModel.UiState(
            isInSearch = false,
            selectableList = persistentListOf(
                SelectableUser(
                    user = SimpleBuzUser(
                        userId = 123213213213L,
                        displayName = "John Doe",
                        portrait = "",
                    ),
                    isSelected = true
                ),
                SelectableUser(
                    user = SimpleBuzUser(
                        userId = 2343245235L,
                        displayName = "Tom jack",
                        portrait = "",
                    ),
                    isSelected = false
                )
            ),
            selectedSize = 0,
            uiEvent = {}
        )
    )
}