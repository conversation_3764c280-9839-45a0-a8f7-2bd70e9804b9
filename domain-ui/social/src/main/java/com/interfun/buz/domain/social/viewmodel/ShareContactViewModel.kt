package com.interfun.buz.domain.social.viewmodel

import android.content.Context
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.common.constants.RouterParamKey
import com.interfun.buz.common.manager.AppConfigRequestManager
import com.interfun.buz.domain.im.social.entity.*
import com.interfun.buz.domain.im.social.usecase.BuzConversationListUseCase
import com.interfun.buz.domain.im.social.usecase.ShareContactCardUserCase
import com.interfun.buz.domain.im.social.usecase.ShareContactLinkUserCase
import com.interfun.buz.domain.social.bean.*
import com.interfun.buz.domain.social.bean.ShareContactInfo.Companion.TargetType
import com.interfun.buz.domain.social.utils.SocialTracker
import com.interfun.buz.im.entity.SendMsgResult
import com.interfun.buz.social.entity.ShareLinkType
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.collections.immutable.PersistentList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toPersistentList
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Author: ChenYouSheng
 * Date: 2025/7/3
 * Email: <EMAIL>
 * Desc: 将当前群或者个人分享给其他会话
 */
@HiltViewModel
class ShareContactViewModel @Inject constructor(
    private val buzConversationListUseCase: BuzConversationListUseCase,
    private val shareContactLinkUserCase: ShareContactLinkUserCase,
    private val shareContactCardUserCase: ShareContactCardUserCase,
    private val savedStateHandle: SavedStateHandle,
) : ViewModel() {

    companion object {
        const val TAG = "ShareContactViewModel"
    }

    private val shareContactInfo =
        savedStateHandle.get<ShareContactInfo>(RouterParamKey.ShareContact.KEY_SHARE_INFO)
            ?: throw IllegalArgumentException("shareContactInfo is null")

    private data class ShareStateIdentifier(
        val shareState: ShareState,
        val item: ChatShareItem
    )

    private val _shareStateListFlow = MutableStateFlow<List<ShareStateIdentifier>>(emptyList())

    private val thirdPartyShareFlow: Flow<PersistentList<ShareType>> = flow {
        val list = mutableListOf<Int>()
        list.add(ShareType.SYSTEM_SHARE.type)
        list.add(ShareType.COPY_LINK.type)
        list.addAll(AppConfigRequestManager.sharePlatformList)
        emit(ShareType.getShareTypeList(list).toPersistentList())
    }

    private val _chatShareItemFlow: Flow<List<ChatShareItem>> = flow {
        val recentChatList = buzConversationListUseCase.invoke().firstOrNull()?.mapNotNull { conv ->
            when (conv) {
                is GroupConversation -> {
                    ChatShareItem.GroupShareItem(
                        groupId = conv.convId,
                        groupName = conv.groupComposite?.buzGroup?.groupName ?: "",
                        groupPortrait = conv.groupComposite?.buzGroup?.serverPortraitUrl ?: "",
                        firstFewPortraits = conv.groupComposite?.buzGroup?.firstFewPortraits
                            ?: emptyList(),
                        groupMemberCount = conv.groupComposite?.buzGroup?.memberNum ?: 0
                    )
                }

                is UserConversation -> {
                    if (conv.userComposite?.user?.isNormalUser == true) {
                        ChatShareItem.UserShareItem(
                            userId = conv.convId,
                            userName = conv.displayName,
                            displayName = conv.userComposite?.fullNickName ?: "",
                            realFullName = conv.userComposite?.user?.realFullName ?: "",
                            userPortrait = conv.userComposite?.user?.portrait ?: "",
                            buzId = conv.userComposite?.user?.buzId ?: "",
                            userType = when {
                                conv.userComposite?.user?.isRobot == true -> UserType.AIBot
                                conv.userComposite?.user?.isOfficialAccount == true -> UserType.Official
                                conv.userComposite?.user?.isResearchAccount == true -> UserType.UserResearch
                                else -> UserType.Normal
                            }
                        )
                    } else null
                }
            }
        }
        emit(recentChatList)
    }.filterNotNull()

    // 最近分享的联系人（和首页会话列表一样）
    private val recentChatFlow: Flow<List<ChatShareItem>> = combine(
        _chatShareItemFlow,
        _shareStateListFlow
    ) { recentList, stateList ->
        val stateMap = stateList.associateBy { it.item.targetId() }

        recentList.map { item ->
            val state = stateMap[item.targetId()]?.shareState ?: ShareState.Normal
            item.copyState(state)
        }
    }

    sealed interface UIEvent {
        data object OnScreenExposed : UIEvent
        data object CloseScreen : UIEvent
        data class ShareContactToChat(val shareItem: ChatShareItem) : UIEvent
        data class ShareLinkToPlatform(val context: Context, val shareItem: ShareItemBean) : UIEvent
    }

    sealed interface EventToUI {
        data object CloseScreen : EventToUI
    }

    private val uiEvent: (UIEvent) -> Unit = {
        handleUIEvent(it)
    }


    data class UiState(
        val thirdPartyShareList: PersistentList<ShareType>,
        val recentChatList: PersistentList<ChatShareItem>,
        val uiEvent: (UIEvent) -> Unit
    )

    val eventToUIFlow = MutableSharedFlow<EventToUI>()

    val uiStateFlow: StateFlow<UiState> = combine(
        thirdPartyShareFlow,
        recentChatFlow
    ) { thirdPartyShareList, recentChatList ->
        UiState(
            thirdPartyShareList = thirdPartyShareList,
            recentChatList = recentChatList.toPersistentList(),
            uiEvent = uiEvent
        )
    }.stateIn(
        viewModelScope,
        SharingStarted.WhileSubscribed(5000),
        UiState(persistentListOf(), persistentListOf(), uiEvent)
    )

    private fun handleUIEvent(uiEvent: UIEvent) {
        when (uiEvent) {
            is UIEvent.OnScreenExposed -> {
                SocialTracker.onPageExposeAVS2025071001(
                    targetId = shareContactInfo.id,
                    source = shareContactInfo.source
                )
            }

            is UIEvent.CloseScreen -> {
                closeScreen()
            }

            is UIEvent.ShareContactToChat -> {
                handleChatShareClick(uiEvent.shareItem)
            }

            is UIEvent.ShareLinkToPlatform -> {
                handleThirdPartyShareClick(context = uiEvent.context, shareItem = uiEvent.shareItem)
            }
        }
    }


    /**
     * 将当前群或者个人分享给其他会话
     */
    fun handleChatShareClick(shareItem: ChatShareItem) {
        // show loading state
        updateShareItemState(shareItem, ShareState.Loading)

        val (targetId, convType) = when (shareItem) {
            is ChatShareItem.GroupShareItem -> shareItem.groupId to IM5ConversationType.GROUP
            is ChatShareItem.UserShareItem -> shareItem.userId to IM5ConversationType.PRIVATE
        }

        // send to im
        viewModelScope.launch {
            val sendResult = shareContactCardUserCase(
                fromId = shareContactInfo.id,
                isGroupContact = shareContactInfo.type == TargetType.Group,
                targetId = targetId,
                convType = convType
            ).firstOrNull { it is SendMsgResult } as? SendMsgResult
            logDebug(
                TAG,
                "handleChatShareClick==>${sendResult}，shareContactInfo:${shareContactInfo}"
            )

            // update share state
            updateShareItemState(shareItem, ShareState.Sent)
        }

        SocialTracker.onClickAC2025071004(
            targetId = shareContactInfo.id,
            source = shareContactInfo.source
        )
    }

    /**
     * 分享链接到第三方平台
     */
    fun handleThirdPartyShareClick(context: Context, shareItem: ShareItemBean) {
        val convType = when (shareContactInfo.type) {
            TargetType.Group -> ShareLinkType.GROUP.serverNum
            TargetType.User -> ShareLinkType.PRIVATE.serverNum
        }
        viewModelScope.launch {
            val result = shareContactLinkUserCase(
                context = context,
                targetId = shareContactInfo.id,
                convType = convType,
                shareItem = shareItem
            )
            logDebug(TAG, "handleThirdPartyShareClick==>${result}")

            val shareType = when (result) {
                is ShareContactLinkUserCase.ShareLinkResult.Success -> result.shareType
                is ShareContactLinkUserCase.ShareLinkResult.Error -> result.shareType
            }
            SocialTracker.onClickAC2025071006(
                targetId = shareContactInfo.id,
                source = shareContactInfo.source,
                platformName = shareType.platformName
            )
        }
    }

    private fun updateShareItemState(item: ChatShareItem, newState: ShareState) {
        _shareStateListFlow.update { currentList ->
            val updated = currentList.map { state ->
                if (state.item.isTheSame(item)) {
                    state.copy(shareState = newState)
                } else {
                    state
                }
            }

            val exists = currentList.any { it.item.isTheSame(item) }
            if (!exists) {
                updated + ShareStateIdentifier(newState, item)
            } else {
                updated
            }
        }
    }

    private fun closeScreen() {
        viewModelScope.launch {
            eventToUIFlow.emit(EventToUI.CloseScreen)
        }
    }
}

