package com.interfun.buz.domain.chat.repo

import com.interfun.buz.base.ktx.asString
import com.interfun.buz.base.ktx.collectInScope
import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.biz.center.voicefilter.repository.VoiceFilterDataRepository
import com.interfun.buz.biz.center.voicemoji.repository.voiceemoji.VoiceEmojiRepository
import com.interfun.buz.common.bean.push.PushBusinessType
import com.interfun.buz.common.bean.voicecall.CallType
import com.interfun.buz.common.constants.CommonMMKV
import com.interfun.buz.common.database.entity.UserStatus
import com.interfun.buz.common.ktx.getBooleanDefault
import com.interfun.buz.common.manager.ABTestManager
import com.interfun.buz.common.manager.AppConfigRequestManager
import com.interfun.buz.common.manager.AppConfigRequestManager.enableFriendVoiceCall
import com.interfun.buz.common.manager.AppConfigRequestManager.enableVideoCall
import com.interfun.buz.common.voicecall.CallEntryHelper
import com.interfun.buz.component.hilt.UserQualifier
import com.interfun.buz.domain.chat.R
import com.interfun.buz.domain.chat.repo.ChatMorePanelAction.*
import com.interfun.buz.signal.ISignalManagerPresenter
import com.interfun.buz.signal.getProtocolDataChangeFlow
import com.interfun.buz.social.repo.BotRepository
import com.interfun.buz.social.repo.GroupRepository
import com.interfun.buz.social.repo.UserRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

sealed interface ChatMorePanelAction {
    data object VoiceFilter : ChatMorePanelAction
    data object VoiceCall : ChatMorePanelAction
    data object VideoCall : ChatMorePanelAction
    data object Photo : ChatMorePanelAction
    data object Camera : ChatMorePanelAction
    data object File: ChatMorePanelAction
    data object Location : ChatMorePanelAction
    data object ShareContact : ChatMorePanelAction
}

data class MorePanelOption(
    val title: String,
    val icon: Int,
    val type: ChatMorePanelAction
)

class MorePanelRepositoryImpl @Inject constructor(
    @UserQualifier private val userScope: CoroutineScope,
    @UserQualifier private val userRepository: UserRepository,
    @UserQualifier private val groupRepository: GroupRepository,
    @UserQualifier private val botRepository: BotRepository,
    private val signalRepository: ISignalManagerPresenter,
    private val voiceFilterRepository: VoiceFilterDataRepository,
) : MorePanelRepository {
    companion object {
        private const val TAG = "MorePanelRepositoryImpl"
    }

    private val supportVoiceFilter get() = ABTestManager.showVoiceFilter && AppConfigRequestManager.openVoiceFilterFunction


    private val _groupMemberChangeFlow = MutableStateFlow<Long?>(null)
    override fun groupMemberChangeFlow(): Flow<Long> = _groupMemberChangeFlow.filterNotNull()

    override val voiceFilterReminderFlow = combine(
        AppConfigRequestManager.openVoiceFilterFunctionFlow,
        ABTestManager.showVoiceFilterFlow,
        AppConfigRequestManager.voiceFilterLatestTimestampFlow,
        voiceFilterRepository.getVFLatestEntryNotifyTimestamp(),
    ) { openVoiceFilterFunction, showVoiceFilter, serverVoiceFilterLatestTimestamp, localVoiceEmojiLatestTimestamp ->
        val supportVoiceFilter = openVoiceFilterFunction && showVoiceFilter
        supportVoiceFilter &&
                localVoiceEmojiLatestTimestamp > 0 &&
                serverVoiceFilterLatestTimestamp > localVoiceEmojiLatestTimestamp
    }

    // home & chat plus button red dot
    override val showMoreButtonRedDotStateFlow =
        voiceFilterReminderFlow.stateIn(userScope, SharingStarted.Eagerly, false)

    init {
        signalRepository.getProtocolDataChangeFlow()
            .filter {
                it.businessType == PushBusinessType.GROUP_MEMBERS_CHANGE.type
                        || it.businessType == PushBusinessType.GROUP_ROB_MEMBER_CHANGE.type
            }
            .collectInScope(userScope) {
                logInfo(TAG, "getProtocolDataChangeFlow: $it")
                val groupId = it.businessId?.toLongOrNull() ?: return@collectInScope
                _groupMemberChangeFlow.value = groupId
            }
    }

    override suspend fun getOptionsList(isGroup: Boolean, targetId: Long): List<MorePanelOption> {
        if (!isGroup) {
            val user = userRepository.getUserFromCache(targetId)
            if (user?.isOfficial == true && !user.isOfficialAccount) {
                return emptyList()
            }
        }

        val options = listOf(
            MorePanelOption(
                title = R.string.voice_filter.asString(),
                icon = R.string.ic_wt_speaking,
                type = VoiceFilter
            ),
            MorePanelOption(
                title = R.string.home_more_panel_voice_call.asString(),
                icon = R.string.ic_tel,
                type = VoiceCall
            ),
            MorePanelOption(
                title = R.string.rtc_videocall.asString(),
                icon = R.string.ic_video,
                type = VideoCall
            ),
            MorePanelOption(
                title = R.string.camera_photo.asString(),
                icon = R.string.ic_album_solid,
                type = Photo
            ),
            MorePanelOption(
                title = R.string.home_more_panel_camera.asString(),
                icon = R.string.ic_camera,
                type = Camera
            ),
            MorePanelOption(
                title = R.string.more_panel_file_option.asString(),
                icon = R.string.ic_file,
                type = File
            ),
            MorePanelOption(
                title = R.string.home_more_panel_location.asString(),
                icon = R.string.ic_location,
                type = Location
            ),
            MorePanelOption(
                title = R.string.contact_card.asString(),
                icon = R.string.ic_contacts_solid,
                type = ShareContact
            ),
        ).filter { option ->
            when (option.type) {
                VoiceFilter -> shouldShowVoiceFilter()
                VoiceCall -> shouldShowCall(isGroup, targetId, CallType.TYPE_VOICE)
                VideoCall -> shouldShowCall(isGroup, targetId, CallType.TYPE_VIDEO)
                Photo, Camera -> shouldShowImageOptions(isGroup, targetId)
                File -> shouldShowFile(isGroup, targetId)
                Location -> shouldShowLocation(isGroup, targetId)
                ShareContact -> shouldShowShareContact(isGroup, targetId)
            }
        }
        return options
    }

    private suspend fun shouldShowShareContact(isGroup: Boolean, targetId: Long): Boolean {
        if (isGroup) return true
        val buzUserInfo = userRepository.getUserFromCache(targetId) ?: return false
        return buzUserInfo.isNormalUser
    }

    private fun shouldShowVoiceFilter() = supportVoiceFilter

    private suspend fun shouldShowCall(isGroup: Boolean, targetId: Long, @CallType callType: Int): Boolean {
        return if (isGroup) {
            shouldShowGroupCall(targetId, callType)
        } else {
            shouldShowUserCall(targetId, callType)
        }
    }

    private suspend fun shouldShowGroupCall(groupId: Long, @CallType callType: Int): Boolean {
        val buzGroupInfo = groupRepository.getGroupFromCache(groupId) ?: return false
        return CallEntryHelper.isRealTimeCallEntryEnable(
            isBigGroup = buzGroupInfo.isBigGroup,
            callType = callType,
        )
    }

    private suspend fun shouldShowUserCall(userId: Long, @CallType callType: Int): Boolean {
        val buzUserCompositeInfo = userRepository.getUserCompositeFromCache(userId) ?: return false
        val isUserBlocked = buzUserCompositeInfo.relationInfo?.isBlocked ?: false
        val isSupported = !isUserBlocked
                && buzUserCompositeInfo.isFriend
                && !buzUserCompositeInfo.user.isRobot
                && !buzUserCompositeInfo.user.isOfficial
                && buzUserCompositeInfo.user.userStatus == UserStatus.STATUS_NORMAL

        val enabledByConfig = if (callType == CallType.TYPE_VOICE) {
            enableFriendVoiceCall
        } else {
            enableVideoCall
        }
        return enabledByConfig && isSupported
    }

    private suspend fun shouldShowImageOptions(isGroup: Boolean, targetId: Long): Boolean {
        if (isGroup) return true

        val buzUserInfo = userRepository.getUserFromCache(targetId) ?: return false
        if (!buzUserInfo.isRobot) return true

        val botExtraInfo = botRepository.getBotExtra(targetId) ?: return false
        return botExtraInfo.botUIConfig?.showImageButton == true
    }

    private suspend fun shouldShowFile(isGroup: Boolean, targetId: Long): Boolean {
        if (isGroup) return true

        val buzUserCompositeInfo =
            userRepository.getUserCompositeFromCache(targetId) ?: return false
        val isFriendWithUser =
            !buzUserCompositeInfo.user.isRobot && !buzUserCompositeInfo.user.isOfficial && buzUserCompositeInfo.isFriend
        return isFriendWithUser
    }

    private suspend fun shouldShowLocation(isGroup: Boolean, targetId: Long): Boolean {
        if (!AppConfigRequestManager.enableMap) return false

        return if (isGroup) {
            true
        } else {
            val user = userRepository.getUserFromCache(targetId)
            user?.isRobot?.not() ?: false
        }
    }

}