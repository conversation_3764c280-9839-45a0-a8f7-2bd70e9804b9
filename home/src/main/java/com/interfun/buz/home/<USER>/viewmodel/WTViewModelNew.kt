package com.interfun.buz.home.view.viewmodel

import android.content.Context
import android.os.SystemClock
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.recyclerview.widget.RecyclerView
import com.buz.idl.common.request.RequestParseIMLink
import com.buz.idl.common.request.RequestReportFeatureGuideComplete
import com.buz.idl.common.service.BuzNetCommonServiceClient
import com.interfun.buz.base.ktx.*
import com.interfun.buz.biz.center.voicemoji.repository.blindbox.BlindBoxRepository
import com.interfun.buz.biz.center.voicemoji.repository.voiceemoji.VoiceEmojiRepository
import com.interfun.buz.chat.R
import com.interfun.buz.chat.common.constants.ChatMMKV
import com.interfun.buz.chat.common.ktx.asBuzMediaItem
import com.interfun.buz.chat.common.manager.LastReadMsgManager
import com.interfun.buz.chat.common.manager.TranslationMessageManager
import com.interfun.buz.chat.common.manager.TranslationMessageManager.translateResult
import com.interfun.buz.chat.common.repository.ChatGlobalRepository
import com.interfun.buz.chat.common.utils.ChatTracker
import com.interfun.buz.chat.map.receive.view.model.LocationDetailInfo
import com.interfun.buz.chat.map.send.model.BuzAddressBean
import com.interfun.buz.chat.map.send.model.BuzLocation
import com.interfun.buz.chat.media.view.fragment.ChatMediaPreviewListFragment.Companion.ChatMediaPreviewArgs
import com.interfun.buz.chat.wt.entity.HomeMsgPreviewModel
import com.interfun.buz.chat.wt.entity.HomeMsgPreviewModel.*
import com.interfun.buz.chat.wt.manager.WTMessageManager
import com.interfun.buz.chat.wt.utils.WTTracker
import com.interfun.buz.chat.wt.utils.enableTranslate
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.bean.DataState
import com.interfun.buz.common.bean.DataUpdate
import com.interfun.buz.common.bean.Resp.Error
import com.interfun.buz.common.bean.Resp.Success
import com.interfun.buz.common.database.UserDatabase
import com.interfun.buz.common.database.entity.ConvType.GroupChat
import com.interfun.buz.common.database.entity.ConvType.PrivateChat
import com.interfun.buz.common.database.entity.LivePlaceLastChannelId
import com.interfun.buz.common.database.entity.UserRelationInfo
import com.interfun.buz.common.interfaces.DeleteCacheType
import com.interfun.buz.common.interfaces.DeleteCacheType.ExitGroup
import com.interfun.buz.common.ktx.*
import com.interfun.buz.common.manager.ActionInfoHandler.getRouter
import com.interfun.buz.common.manager.AppConfigRequestManager
import com.interfun.buz.common.manager.ChannelStatusManager
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.manager.cache.user.UserRelationCacheManager
import com.interfun.buz.common.net.withConfig
import com.interfun.buz.common.service.StorageService
import com.interfun.buz.common.utils.CommonTracker
import com.interfun.buz.common.utils.NotificationUtil
import com.interfun.buz.common.utils.PromptUtil
import com.interfun.buz.common.utils.parse
import com.interfun.buz.domain.chat.repo.MorePanelRepository
import com.interfun.buz.domain.im.social.entity.*
import com.interfun.buz.domain.im.social.repository.SendIMRepository
import com.interfun.buz.domain.im.social.usecase.BuzConversationListUseCase
import com.interfun.buz.domain.record.entity.RecordBgType
import com.interfun.buz.domain.record.helper.TraceUtils
import com.interfun.buz.download.bean.DownloadStateInfo
import com.interfun.buz.download.bean.DownloadStatus.*
import com.interfun.buz.download.bean.FileIdentity
import com.interfun.buz.home.data.entity.OneShotEvent
import com.interfun.buz.home.data.main.MainItemFactory
import com.interfun.buz.home.data.preview.PreviewFactory
import com.interfun.buz.home.data.transform.MainListCombinedTransform
import com.interfun.buz.home.data.transform.PreviewCombinedTransform
import com.interfun.buz.home.data.usecase.HomeRobotStateUseCase
import com.interfun.buz.home.data.usecase.LivePlayingMsgUseCase
import com.interfun.buz.home.entity.*
import com.interfun.buz.home.entity.HomeMoreItemState.ClickType
import com.interfun.buz.home.entity.HomeSelectedItem.Group
import com.interfun.buz.home.entity.HomeSelectedItem.User
import com.interfun.buz.home.manager.HomePreviewPlayer
import com.interfun.buz.home.view.utils.FTUEHomeTracker
import com.interfun.buz.home.view.utils.HomeEventTracker
import com.interfun.buz.im.IMAgent
import com.interfun.buz.im.entity.IMSendStateEvent
import com.interfun.buz.im.entity.ShowTranslateTextOp
import com.interfun.buz.im.entity.translation.TranslateState
import com.interfun.buz.im.entity.translation.TranslateState.TranslateSuccess
import com.interfun.buz.im.ktx.*
import com.interfun.buz.im.message.BuzLocationMessage
import com.interfun.buz.im.repo.FileOpenResult
import com.interfun.buz.im.repo.FileOpenResult.*
import com.interfun.buz.im.repo.IMFileDownloadRepository
import com.interfun.buz.im.signal.HeartBeatManager
import com.interfun.buz.im.signal.HeartBeatType
import com.interfun.buz.im.util.IMMsgIdentity
import com.interfun.buz.onair.bean.ConvChannelInfo
import com.interfun.buz.social.entity.SimpleBuzUser
import com.interfun.buz.social.repo.*
import com.interfun.buz.translator.repo.AutoTranslationSettingRepository
import com.lizhi.component.basetool.ntp.NtpTime
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import com.lizhi.im5.sdk.conversation.IM5ConversationType.GROUP
import com.lizhi.im5.sdk.conversation.IM5ConversationType.PRIVATE
import com.lizhi.im5.sdk.conversation.IM5ConversationUpdateTimeParam
import com.lizhi.im5.sdk.message.IMessage
import com.yibasan.lizhifm.sdk.platformtools.ResUtil
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.*
import kotlinx.coroutines.channels.getOrElse
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.selects.select
import javax.inject.Inject

typealias InitialDownloadData = List<Pair<FileIdentity, DownloadStateInfo>>
typealias UpdateDownloadData = Pair<FileIdentity, DownloadStateInfo>

/**
 * @Desc
 * @Author:<EMAIL>
 * @Date: 2022/10/24
 */
@HiltViewModel
class WTViewModelNew @Inject constructor(
    private val botRepository: BotRepository,
    private val userRepository: UserRepository,
    private val groupRepository: GroupRepository,
    private val autoTranslationSettingRepository: AutoTranslationSettingRepository,
    private val sendIMRepository : SendIMRepository,
    private val imFileDownloadRepository: IMFileDownloadRepository,
    private val groupOnlineMembersRepository: GroupOnlineMembersRepository,
    private val groupMembersRepository: GroupMembersRepository,
    private val chatGlobalRepository: ChatGlobalRepository,
    private val conversationListUseCase: BuzConversationListUseCase,
    private val previewTransforms: PreviewCombinedTransform,
    private val mainItemTransforms: MainListCombinedTransform,
    private val morePanelRepository: MorePanelRepository,
    private val robotStatusUseCase: HomeRobotStateUseCase,
    private val livePlayingMsgUseCase : LivePlayingMsgUseCase,
) : ViewModel() {

    companion object {
        const val TAG = "WTViewModelNew"
    }
    private val scrollToTargetFlow = MutableSharedFlow<ScrollEvent?>()
    private val emojiPanelTargetFlow = MutableStateFlow<HomeSelectedItem.Conversation?>(null)
    private val morePanelTargetFlow = MutableStateFlow<HomeSelectedItem.Conversation?>(null)
    private val recordingTarget = MutableStateFlow<HomeSelectedItem.Conversation?>(null)
    private val addingFriendSetFlow = MutableStateFlow<Set<Long>>(HashSet())
    private val _oneShotEventFlow = MutableSharedFlow<OneShotEvent>()
    val oneShotEventFlow = _oneShotEventFlow.asSharedFlow()

    private val lockTargetFlow = combine(
        emojiPanelTargetFlow,
        morePanelTargetFlow,
        recordingTarget
    ) { emojiPanelTarget, morePanelTarget, recordingTarget ->
        recordingTarget ?: morePanelTarget ?: emojiPanelTarget
    }.stateIn(viewModelScope, SharingStarted.Lazily, null)

    private val selectedItem = MutableStateFlow<HomeSelectedItem>(HomeSelectedItem.None)
    val selectedItemFlow = selectedItem
    private val mentionedBotEventFlow = MutableStateFlow<Pair<Long,SimpleBuzUser?>?>(null)
    val currentGroupAiList = flow {
        coroutineScope {
            var groupId : Long? = null
            var botList : List<SimpleBuzUser>? = null
            var selectedBot: SimpleBuzUser? = null
            val currentGroupAiListChannel = selectedItem.flatMapLatest { selectedItem ->
                if (selectedItem is HomeSelectedItem.Group) {
                    groupMembersRepository.getGroupBotMemberFromCacheFlow(selectedItem.targetId)
                        .map { list -> selectedItem.targetId to list.mapNotNull { it.buzUserComposite?.toSimpleBuzUser(true) } }
                } else {
                    flowOf(null to null)
                }
            }.produceIn(this)
            val mentionBotChannel = mentionedBotEventFlow.produceIn(this)
            while (isActive){
                val result = select {
                    currentGroupAiListChannel.onReceiveCatching {
                        DataUpdate.Init(it.getOrElse { null })
                    }
                    mentionBotChannel.onReceiveCatching {
                        DataUpdate.Update(it.getOrElse { null })
                    }
                }
                when(result){
                    is DataUpdate.Init -> {
                        val preGroupId = groupId
                        groupId = result.data?.first
                        botList = result.data?.second
                        if (preGroupId != groupId){
                            //切换群组，清空选中
                            selectedBot = null
                        }
                    }
                    is DataUpdate.Update -> {
                        if (result.data?.first == groupId){
                            selectedBot = result.data?.second
                        }
                    }
                }
                if (groupId == null) {
                    emit(null)
                } else {
                    emit(HomeGroupAiList(groupId, botList ?: kotlin.collections.emptyList(), selectedBot))
                }
            }
        }
    }.stateIn(viewModelScope, SharingStarted.WhileSubscribed(5000L), null)

    private val conversationListFlow =
        conversationListUseCase().map { DataState.Data(it) }
            .stateIn(viewModelScope, SharingStarted.WhileSubscribed(5000L), DataState.Loading)

    val selectedConversation =
        combine(conversationListFlow.filter { it is DataState.Data }, selectedItemFlow) { convData, selectedItem ->
            val conversationList = (convData as DataState.Data).data
            conversationList.find { it.convId == selectedItem.targetIdOrNull() }
        }

    //为了做去重，有select 状态的话会一直变更
    val currentGroupAiListWithoutSelected =
        currentGroupAiList.map { it?.groupId to it?.botList }
            .stateIn(viewModelScope, SharingStarted.Lazily, null to null)

    val currentMentionedBotFlow = currentGroupAiList.map { it?.groupId to it?.selectedBot }
        .stateIn(viewModelScope, SharingStarted.Lazily, null to null)

    val currentMentionedBotWithInfoFlow = currentGroupAiList.mapLatest { currentGroupAiList ->
        val userId = currentGroupAiList?.selectedBot?.userId
            ?: return@mapLatest currentGroupAiList?.groupId to null
        currentGroupAiList.groupId to userRepository.getUserCompositeFromCache(userId)
            ?.toOldUserRelationInfo()
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    val homeList = conversationListFlow.filter { it is DataState.Data }
        .map { convData ->
            val conversationList = (convData as DataState.Data).data
            val homeListFlow = getHomeListFlow(conversationList)
            val previewListFlow = getPreviewListFlow(conversationList)
            homeListFlow to previewListFlow
        }.flatMapLatest { (homeListFlow, previewListFlow) ->
            combine(homeListFlow, previewListFlow) { homeList, previewList ->
                val result = ArrayList<HomeCombinedItem>(homeList.size + 1)
                result.add(HomeCombinedItem(HomeAddBtnItem, HomeMsgPreviewModel.InvitePreview))
                homeList.forEachIndexed { index, homeItem ->
                    result.add(HomeCombinedItem(homeItem, previewList[index]))
                }
                result
            }
        }.flowOn(Dispatchers.Default)
        .stateIn(viewModelScope, SharingStarted.WhileSubscribed(10000L), null)

    val homeUIState = combine(
        homeList,
        scrollToTargetFlow.onSubscription { emit(null) },
        lockTargetFlow
    ) { homeList, scrollToTarget, lockTarget ->
        val realScrollToTarget = if (lockTarget != null) {
            scrollToTarget?.isConsumed = true
            //保证录音等逻辑的锁定优先，其他情况不能让其滑动
            ScrollEvent(lockTarget.targetId)
        } else {
            scrollToTarget
        }
        HomeUIState(homeList, realScrollToTarget)
    }.stateIn(viewModelScope, SharingStarted.WhileSubscribed(10000L), null)

    private val livePlayingMsgFlow =
        livePlayingMsgUseCase().stateIn(viewModelScope, SharingStarted.WhileSubscribed(5000L), null)

    private fun getHomeListFlow(
        conversationList: List<Conversation>,
    ): Flow<List<HomeConversationItem>> {
        return mainItemTransforms.transform(
            addingFriendSetFlow,
            conversationList,
            recordingTarget,
            currentMentionedBotFlow
        ).map { liveInfo ->
            conversationList.map { conv ->
                MainItemFactory.create(conv, liveInfo)
            }
        }.flowOn(Dispatchers.Default)
    }

    private fun getPreviewListFlow(homeList: List<Conversation>): Flow<List<HomeMsgPreviewModel>> {
        return previewTransforms.transform(homeList, livePlayingMsgFlow).map { liveInfo ->
            homeList.map { conv ->
                PreviewFactory.create(conv, liveInfo)
            }
        }.flowOn(Dispatchers.Default)
    }

    val chatHistoryPageAddressBotFlow = chatGlobalRepository.chatHistoryPageAddressBotFlow

    //请注意这个可能会有异步操作，数据会比selectedItemFlow慢,默认拿本地数据，本地没有会请求服务端
    val selectedItemBasicInfoFlow : Flow<HomeSelectedItemBasicInfo> = selectedItemFlow.flatMapLatest { selectedItem ->
        when (selectedItem) {
            HomeSelectedItem.None -> flow<HomeSelectedItemBasicInfo> { emit(HomeSelectedItemBasicInfo.None) }
            HomeSelectedItem.AddButton -> flow<HomeSelectedItemBasicInfo> { emit(HomeSelectedItemBasicInfo.AddButton) }
            is HomeSelectedItem.Group -> groupRepository.getGroupCompositeFromCacheFlow(selectedItem.targetId,refreshIfNull = true)
                .map { HomeSelectedItemBasicInfo.Group(selectedItem.targetId, it) }

            is HomeSelectedItem.User -> userRepository.getUserCompositeFromCacheFlow(selectedItem.targetId,refreshIfNull = true)
                .map { HomeSelectedItemBasicInfo.User(selectedItem.targetId, it) }
        }
    }

    val recordButtonBgType = combine(
        selectedItemBasicInfoFlow,
        currentMentionedBotFlow
    ) { selectedItem, (currentGroupId, addressedAI) ->
        when (selectedItem) {
            HomeSelectedItemBasicInfo.AddButton, HomeSelectedItemBasicInfo.None -> RecordBgType.Normal
            is HomeSelectedItemBasicInfo.Group -> {
                if (addressedAI != null && selectedItem.targetId == currentGroupId) {
                    RecordBgType.AddressBot
                } else {
                    RecordBgType.Normal
                }
            }

            is HomeSelectedItemBasicInfo.User -> {
                if (selectedItem.userComposite?.user?.isRobot == true) {
                    RecordBgType.Robot
                } else {
                    RecordBgType.Normal
                }
            }
        }
    }.stateIn(viewModelScope, SharingStarted.Lazily, RecordBgType.Normal)

    private val currentItemMoreOptionsFlow = selectedItemFlow.mapLatest {
        when (it) {
            is HomeSelectedItem.Group -> morePanelRepository.getOptionsList(true, it.targetId)
            is HomeSelectedItem.User -> morePanelRepository.getOptionsList(false, it.targetId)
            else -> kotlin.collections.emptyList()
        }
    }.flowOn(Dispatchers.Default)

    private val _moreDialogStateFlow = MutableStateFlow(false)
    val moreDialogStateFlow: StateFlow<Boolean> = _moreDialogStateFlow
    private val _voiceEmojiDialogShowingState = MutableStateFlow(false)
    val voiceEmojiDialogShowingState: StateFlow<Boolean> = _voiceEmojiDialogShowingState

    val moreButtonStateFlow = combine(
        selectedItemBasicInfoFlow,
        currentItemMoreOptionsFlow,
        morePanelRepository.showMoreButtonRedDotStateFlow,
        moreDialogStateFlow,
    ) { selectedItem, optionList, showRedDot, isDialogShowing ->
        val isEnableForTargetType = when (selectedItem) {
            HomeSelectedItemBasicInfo.AddButton, HomeSelectedItemBasicInfo.None -> false
            is HomeSelectedItemBasicInfo.Group -> true
            is HomeSelectedItemBasicInfo.User -> true
        }
        val isEnable = isEnableForTargetType && optionList.isNotEmpty()
        val clickType =
            if (selectedItem is HomeSelectedItemBasicInfo.User && selectedItem.userComposite?.isMe == false && !selectedItem.userComposite.isFriend) {
                ClickType.DisableNotFriend
            } else {
                ClickType.Normal
            }
        HomeMoreItemState(isEnable, clickType, selectedItem, showRedDot && !isDialogShowing)
    }.stateIn(viewModelScope, SharingStarted.Lazily, null)

    val voiceEmojiButtonStateFlow = combine(
        selectedItemBasicInfoFlow,
        VoiceEmojiRepository.getVELatestEntryNotifyTimestamp(),
        BlindBoxRepository.hasNewBlindBox()
    ) { selectedItem, voiceEmojiLatestTimestampFlow, hasNewBlindBox ->
        val isEnable =
            selectedItem !is HomeSelectedItemBasicInfo.AddButton && selectedItem !is HomeSelectedItemBasicInfo.None
        val clickType =
            if (selectedItem is HomeSelectedItemBasicInfo.User && selectedItem.userComposite?.isMe == false && !selectedItem.userComposite.isFriend) {
                HomeVoiceEmojiItemState.ClickType.DisableNotFriend
            } else {
                HomeVoiceEmojiItemState.ClickType.Normal
            }
        val showBadge = isEnable && (hasNewBlindBox || voiceEmojiLatestTimestampFlow.let {
            it > 0 && AppConfigRequestManager.voiceEmojiLatestTimestamp > it
        })
        HomeVoiceEmojiItemState(isEnable, clickType, selectedItem, showBadge)
    }.stateIn(viewModelScope, SharingStarted.Lazily, null)


    val wtStrangerAddFriendStateFlow = MutableSharedFlow<Pair<Long, UserRelationInfo>?>()
    var currentSpeakingTargetId: Long? = null
    private val userDb get() = UserDatabase.currInstance
    private val client by lazy { BuzNetCommonServiceClient().withConfig() }
    private val _onSendLocationFlow = MutableSharedFlow<Any>()
    val onSendLocationFlow: Flow<Any> = _onSendLocationFlow
    private val _openVEPanelFlow = MutableSharedFlow<Pair<Long,IM5ConversationType>>()
    val openVEPanelFlow: Flow<Pair<Long,IM5ConversationType>> = _openVEPanelFlow
    private val homePreviewPlayer = HomePreviewPlayer
    //Pair: targetId to location
    private val _previewLocationFlow = MutableSharedFlow<Pair<String,LocationDetailInfo>>()
    val previewLocationFlow : Flow<Pair<String,LocationDetailInfo>> = _previewLocationFlow
    private val _previewMediaFlow = MutableSharedFlow<ChatMediaPreviewArgs>()
    val previewMediaFlow : Flow<ChatMediaPreviewArgs> = _previewMediaFlow
    private val _toChatListFlow = MutableSharedFlow<Pair<IM5ConversationType,Long>>()
    val toChatListFlow : Flow<Pair<IM5ConversationType,Long>> = _toChatListFlow
    private val _loadingUrlParsing = MutableStateFlow<Boolean>(false)
    val loadingUrlParsing : Flow<Boolean> = _loadingUrlParsing
    private val _parseUrlResult = MutableSharedFlow<ParseUrlResult>()
    val parseUrlResult: Flow<ParseUrlResult> = _parseUrlResult

    val selectedCombinedItem = combine(selectedItemFlow, homeList) { item, homeList ->
        val targetId = item.targetIdOrNull() ?: return@combine null
        homeList?.find { it.preview is ConversationPreview && it.preview.convInfo.convTargetId == targetId }
    }.distinctUntilChanged().flowOn(Dispatchers.IO)

    private val recyclerViewScrollStateFlow = MutableStateFlow<Int>(RecyclerView.SCROLL_STATE_IDLE)

    val selectedRobotStateFlow = selectedConversation.flatMapLatest { conv ->
        combine(recyclerViewScrollStateFlow,robotStatusUseCase(conv)){ scrollState, robotState ->
            if (scrollState != RecyclerView.SCROLL_STATE_IDLE) {
                return@combine null
            }
            robotState
        }
    }.stateIn(viewModelScope, SharingStarted.Lazily, null)

    private val _showFileDownloadDialogFlow = MutableSharedFlow<FilePreview>()
    val showFileDownloadDialogFlow: Flow<FilePreview> = _showFileDownloadDialogFlow
    val msgHasBeenRejectedEventFlow = IMAgent.msgSendStateFlow.mapNotNull { imSendState ->
        if (imSendState is IMSendStateEvent.OnError && imSendState.errorCode == 3) {
            val isGroup = imSendState.msg.conversationType == IM5ConversationType.GROUP
            if (isGroup){
                val groupInfo = groupRepository.getGroupFromCache(imSendState.msg.getConvTargetIdLong())
                MsgHasBeenRejectedEvent(groupInfo?.groupName ?: imSendState.msg.getConvTargetIdLong().toString())
            }else {
                val userInfo = userRepository.getUserCompositeFromCache(imSendState.msg.getConvTargetIdLong())
                MsgHasBeenRejectedEvent(userInfo?.firstNickName ?: imSendState.msg.getConvTargetIdLong().toString())
            }
        }else {
            null
        }
    }

    //翻译语言变更需要重新翻译
    val shouldTranslateMessages = autoTranslationSettingRepository.observeSelectedCode().flatMapLatest {
        merge(
            livePlayingMsgFlow.mapNotNull { if (it != null && canTranslate(it)) listOf(it) else null },
            conversationListFlow.mapNotNull { if (it is DataState.Data) it.data else null }
                .map { convList ->
                    val list = ArrayList<IMessage>()
                    for (conv in convList) {
                        val msg = conv.lastMessage
                        if (msg != null && canTranslate(msg)) {
                            list.add(msg)
                        }
                    }
                    list
                }).flowOn(Dispatchers.Default)
    }

    init {
        logSelectedItemChange()
        observeSelectedItemChange()
        observeRecordingState()
    }

    private fun canTranslate(msg: IMessage): Boolean {
        return msg.isReceive && (msg.translateResult.state == TranslateState.Idle
                || !msg.content.isTargetLanguage) && msg.enableTranslate()
                && TranslationMessageManager.isSupportTranslate(msg)
    }

    fun translateMessage(msg: List<IMessage>) {
        TranslationMessageManager.translateMessage(msg)
    }

    fun setRecording(targetId: Long?,isGroup: Boolean?) {
        if (targetId == null){
            recordingTarget.value = null
            return
        }
        recordingTarget.value = if (isGroup == true) HomeSelectedItem.Group(targetId) else HomeSelectedItem.User(targetId)
    }

    private fun observeRecordingState() {
        recordingTarget.collectInScope(viewModelScope) { target ->
            val id = target?.targetId ?: return@collectInScope
            val convType = target.imConvTypeOrNull() ?: return@collectInScope
            updateConversationTime(id, convType)
        }
    }

    private fun observeSelectedItemChange() {
        selectedItemFlow.collectInScope(viewModelScope) { selectedItem ->
            HeartBeatManager.unsubscribe(HeartBeatType.WT_ONLINE_GROUP_SELECTED)
            HeartBeatManager.unsubscribe(HeartBeatType.T_GROUP_CHAT)
            if (selectedItem is HomeSelectedItem.Group) {
                HeartBeatManager.subscribe(
                    HeartBeatType.WT_ONLINE_GROUP_SELECTED,
                    selectedItem.targetId.toString()
                )
                HeartBeatManager.subscribe(HeartBeatType.T_GROUP_CHAT, selectedItem.targetId.toString())
                refreshOnlineGroupMembers(groupId = selectedItem.targetId)
            }
        }
    }

    fun openVoiceEmojiPanel(targetId: Long, convType: IM5ConversationType) {
        viewModelScope.launch {
            _openVEPanelFlow.emit(targetId to convType)
        }
    }

    private fun refreshOnlineGroupMembers(groupId: Long){
        viewModelScope.launch {
            groupOnlineMembersRepository.refreshOnlineGroupMembers(groupId)
        }
    }

    fun refreshGroupAiList(groupId: Long){
        viewModelScope.launch {
            groupMembersRepository.syncGroupMemberList(groupId)
        }
    }

    fun selectedMentionedBot(groupId: Long, bot: SimpleBuzUser?) {
        viewModelScope.launch {
            mentionedBotEventFlow.emit(groupId to bot)
        }
    }

    fun onClickLeaveMsgTakePhoto() {
        viewModelScope.launch {
            val currentItem = selectedItemFlow.value
            when (currentItem) {
                is Group -> {
                    ChatTracker.onClickLeaveMsgTakePhoto(currentItem.targetId, true)
                }

                is User -> {
                    val isRobot =
                        userRepository.getUserFromCache(currentItem.targetId)?.isRobot == true
                    ChatTracker.onClickLeaveMsgTakePhoto(currentItem.targetId, false, isRobot)
                }

                else -> {}
            }
        }
    }

    @Deprecated("聊天历史列表还有需要传递这个，理论上应该只传id,看了改动太大，先这么处理，等列表重构再移除这个逻辑")
    suspend fun getMentionedBot(groupId: Long): UserRelationInfo? {
        val currentGroupAiList = currentGroupAiList.value
        if (currentGroupAiList?.groupId != groupId) {
            return null
        }
        val userId = currentGroupAiList.selectedBot?.userId ?: return null
        return userRepository.getUserCompositeFromCache(userId)?.toOldUserRelationInfo()
    }

    private fun logSelectedItemChange(){
        viewModelScope.launch {
            selectedItemBasicInfoFlow.distinctUntilChanged { old, new ->
                //因为用户、群信息会不断变更，所以只需要取首次数据
                when(new){
                    HomeSelectedItemBasicInfo.AddButton -> old is HomeSelectedItemBasicInfo.AddButton
                    HomeSelectedItemBasicInfo.None -> old is HomeSelectedItemBasicInfo.None
                    is HomeSelectedItemBasicInfo.Group -> old is HomeSelectedItemBasicInfo.Group && old.targetId == new.targetId
                    is HomeSelectedItemBasicInfo.User -> old is HomeSelectedItemBasicInfo.User && old.targetId == new.targetId
                }
            }.collect { item ->
                val (trackItemType,targetId) = when(item){
                    HomeSelectedItemBasicInfo.None,
                    HomeSelectedItemBasicInfo.AddButton -> return@collect
                    is HomeSelectedItemBasicInfo.Group -> "group" to item.targetId
                    is HomeSelectedItemBasicInfo.User -> {
                        if (item.userComposite?.user?.isOfficialAccount == true){
                            "official_account" to item.targetId
                        } else if (item.userComposite?.user?.isRobot == true){
                            "robot" to item.targetId
                        }else{
                            "private" to item.targetId
                        }
                    }
                }
                WTTracker.onScrollOrClickWTItem(trackItemType, targetId)
            }
        }
    }

    fun leaveGroup(groupId: Long) {
        viewModelScope.launch(Dispatchers.IO) {
            val resp = groupRepository.quitGroup(groupId)
            PromptUtil.parse(resp.prompt)
            when(resp){
                is Error -> {
                    if (resp.prompt == null) {
                        toastNetworkErrorTips()
                    }
                }
                is Success -> {
                    IMAgent.deleteConversationSync(IM5ConversationType.GROUP, groupId.toString())
                    routerServices<StorageService>().value?.deleteBatchMsgCache(
                        userId = UserSessionManager.uid,
                        deleteFrom = ExitGroup,
                        targetId = groupId,
                        convType = GroupChat.value
                    )
                    ChatMMKV.putGroupInviteExposureFlag(groupId.toString(), false)
                }
            }
        }
    }

    fun onScrollStateChanged(state : Int){
        recyclerViewScrollStateFlow.value = state
    }

    fun onItemSelected(item: HomeItem) {
        selectedItem.value = when (item) {
            HomeAddBtnItem -> HomeSelectedItem.AddButton
            is HomeGroupItem -> HomeSelectedItem.Group(item.targetId)
            is HomeRobotItem -> HomeSelectedItem.User(item.targetId)
            is HomeUserItem -> HomeSelectedItem.User(item.targetId)
        }
    }

    fun switchSourceAndTargetLanguage(id: Long) {
        viewModelScope.launch {
            botRepository.switchSourceAndTargetLanguage(id)
            ChatTracker.onClickSwitchLanguageInHome(id)
        }
    }

    fun clickPreviewArea(preview: IMsgPreview) {
        when (preview) {
            is IVoicePreview -> {
                clickPreviewVoice(preview)
            }
            is LocationPreview -> {
                previewLocation(preview)
            }
            is ImagePreview,is VideoPreview -> {
                previewImageOrVideo(preview)
            }
            is HyperlinkPreview -> {
                previewLink(preview)
            }
            else -> {}
        }
        val isToPlayingVoice = if (preview is IVoicePreview) !preview.isPlaying else null
        HomeEventTracker.onClickToPlayPreview(preview, isToPlayingVoice)
    }

    fun parseIMLink(link: String) {
        launch {
            _loadingUrlParsing.emit(true)
            val client = BuzNetCommonServiceClient().withConfig()
            val request = RequestParseIMLink(link)
            val response = client.parseIMLink(request)
            _loadingUrlParsing.emit(false)
            if (!response.isSuccess) {
                response.data?.prompt?.parse()
                _parseUrlResult.emit(NormalUrlResult(link))
                return@launch
            }
            if (response.data == null){
                _parseUrlResult.emit(NormalUrlResult(link))
                return@launch
            }
            when (response.data!!.linkType) {
                1 -> {
                    logInfo(TAG, "parseIMLink:route ")
                    val router = response.data!!.action?.router
                    if (router.isNull()) return@launch
                    _parseUrlResult.emit(RouterUrlResult(router!!.getRouter()))
                }

                2 -> {
                    logInfo(TAG, "parseIMLink: browser ")
                    _parseUrlResult.emit(NormalUrlResult(link))
                }
            }
        }
    }

    fun sendTextMsg(
        textMsgParams: TextMsgParams
    ) {
        TraceUtils.reportMsgContentLink(textMsgParams.content)
        viewModelScope.launch {
            sendIMRepository.sendTextMessage(textMsgParams)
        }
    }

    fun previewFile(context: Context, preview: FilePreview) {
        viewModelScope.launch {
            openFileInternal(context, preview)
        }
    }

    private fun previewLink(preview: HyperlinkPreview) {
        if (!preview.linkUrl.isNullOrEmpty()){
            parseIMLink(preview.linkUrl!!)
            previewMsg(
                preview.baseMsgInfo.convType,
                preview.baseMsgInfo.convTargetId,
                preview.baseMsgInfo.msgId
            )
        }
    }

    private fun previewMsg(convType: IM5ConversationType,targetId: Long, msgId: Long) {
        viewModelScope.launch {
            val msg = getMsg(convType,targetId, msgId)
            msg?.let { previewMsg(msg) }
        }
    }

    private suspend fun previewMsg(msg: IMessage) {
        withContext(Dispatchers.IO) {
            msg.setPlayedMessage()
            msg.makeVoiceMsgListened()
            LastReadMsgManager.get().onMsgPlayed(msg)
        }
    }

    private fun clickPreviewVoice(preview : IVoicePreview){
        if (preview.isPlaying){
            homePreviewPlayer.stopPlayingPreview(null)
            WTMessageManager.playNextMessage(
                "clickHomePreview",
                IMMsgIdentity(
                    preview.baseMsgInfo.msgId,
                    preview.baseMsgInfo.convType
                )
            )
        }else {
            viewModelScope.launch {
                val msg = getMsg(
                    preview.baseMsgInfo.convType,
                    preview.baseMsgInfo.convTargetId,
                    preview.baseMsgInfo.msgId
                )
                msg?.let {
                    if (homePreviewPlayer.play(msg)) {
                        previewMsg(msg)
                    }
                }
            }
        }
    }

    private fun previewLocation(preview : LocationPreview){
        viewModelScope.launch {
            val msg = getMsg(
                preview.baseMsgInfo.convType,
                preview.baseMsgInfo.convTargetId,
                preview.baseMsgInfo.msgId
            )
            val msgContent = msg?.content as? BuzLocationMessage ?: return@launch
            val info = LocationDetailInfo(
                BuzLocation(msgContent.latitude, msgContent.longitude),
                BuzAddressBean(msgContent.locationName, msgContent.locationAddress),
                msg.msgId,
                msg.conversationType
            )
            _previewLocationFlow.emit(msg.getConversationId() to info)
            previewMsg(msg)
        }
    }

    private suspend fun getMsg(
        convType: IM5ConversationType,
        targetId: Long,
        msgId: Long
    ): IMessage? {
        //为了兼容IM那边会话最后一条消息没有落库的情况，后面im会发版解决，目前如果库里找不到就去拉多一次会话
        return IMAgent.getMessageSync(convType, msgId) ?: run {
            val convLastMsg =
                IMAgent.getConversation(
                    convType,
                    targetId.toString()
                )?.lastMessage
            if (convLastMsg?.msgId == msgId) {
                convLastMsg
            } else {
                null
            }
        }
    }

    private fun previewImageOrVideo(preview: IMsgPreview) {
        viewModelScope.launch {
            val msg = getMsg(
                preview.baseMsgInfo.convType,
                preview.baseMsgInfo.convTargetId,
                preview.baseMsgInfo.msgId
            )
            val mediaItem = msg?.asBuzMediaItem() ?: return@launch
            val info = ChatMediaPreviewArgs(
                msg.getConvTargetIdLong(),
                msg.msgId,
                msg.isPrivate,
                false,
                mediaItem,
                "share_img_${msg.msgId}",
                false
            )
            _previewMediaFlow.emit(info)
            previewMsg(msg)
        }
    }

    fun translateMessage(baseMsgInfo: BaseMsgInfo, force: Boolean = false) {
        if (!isNetworkAvailable) {
            toastSolidWarning(R.string.network_error)
            return
        }
        viewModelScope.launchIO {
            IMAgent.getMessageSync(
                convType = baseMsgInfo.convType,
                messageId = baseMsgInfo.msgId
            )?.let { msg ->
                msg.updateShowTranslateText(ShowTranslateTextOp.MANUAL_OPEN)
                if (msg.translateResult.state != TranslateSuccess) {
                    TranslationMessageManager.translateMessage(msg, force)
                    autoTranslationSettingRepository.updateTotalChatTranslation(baseMsgInfo.convTargetId, 1)
                }
            }
        }
    }

    fun scrollToTarget(targetId: Long) {
        scrollToTarget(ScrollEvent(targetId))
    }

    fun scrollToTarget(event: ScrollEvent) {
        scrollToTargetFlow.emitInScope(
            viewModelScope,
            event
        )
    }

    fun updateConversationTime(targetId: Long, convType: IM5ConversationType) {
        val time = NtpTime.nowForce()
        IMAgent.updateConversationTime(
            IM5ConversationUpdateTimeParam(
                convType,
                targetId.toString(),
                time
            )
        )
    }

    fun sendLocation(
        locType: String,
        targetId: String,
        sendType: IM5ConversationType,
        lat: Double,
        lon: Double,
        locationName: String,
        locationAddress: String,
        replyId: Long?=null
    ) {
        viewModelScope.launch {
            sendIMRepository.sendLocationMsg(
                LocationMsgParams(
                    targetId = targetId,
                    sendType = sendType,
                    locType = locType,
                    lat = lat,
                    lon = lon,
                    locationName = locationName,
                    locationAddress = locationAddress,
                    commonMsgParams = CommonMsgParams(
                        replyId = replyId,
                    ),
                )
            )
        }
        onSendLocation()
    }

    fun onSendLocation() {
        viewModelScope.launch {
            _onSendLocationFlow.emit(Any())
        }
    }

    fun requestAddFriend(userId: Long){
        viewModelScope.launch {
            updateBtnAddLoadingStatus(userId, true)
            val resp = userRepository.addFriend(userId, 5, null)
            PromptUtil.parse(resp.prompt)
            if (resp is Success) {
                val isFriend = resp.data.isFriend
                if (isFriend) {
                    toastRegularCorrect(R.string.friend_request_add_success)
                } else {
                    toastRegularCorrect(R.string.friend_request_send)
                }
                updateBtnAddLoadingStatus(userId, false)
                wtStrangerAddFriendStateFlow.emitInScope(
                    viewModelScope,
                    userId to resp.data.toOldUserRelationInfo()
                )
            } else {
                updateBtnAddLoadingStatus(userId, false)
            }
        }
    }

    fun requestAcceptFriend(userId: Long) {
        updateBtnAddLoadingStatus(userId, true)
        viewModelScope.launch {
            val resp = userRepository.agreeFriendApply(userId)
            PromptUtil.parse(resp.prompt)
            when (resp) {
                is Error -> {}
                is Success -> {
                    toastRegularCorrect(R.string.friend_request_add_success)
                }
            }
            updateBtnAddLoadingStatus(userId, false)
        }
    }


    fun deleteItemByTargetId(targetId: Long, convType: IM5ConversationType) {
        if (targetId <= 0) return
        viewModelScope.launch {
            val (result, errorInfo) = IMAgent.deleteConversationSync(
                convType,
                targetId.toString()
            )
            if (result != true) {
                toastNetworkErrorTips()
            }else {
                routerServices<StorageService>().value?.deleteBatchMsgCache(
                    userId = UserSessionManager.uid,
                    targetId = targetId.getLongDefault(),
                    deleteFrom = DeleteCacheType.DeleteConv,
                    convType = if (convType == PRIVATE) PrivateChat.value else GroupChat.value
                )
            }
        }
    }

    fun getConvLivePlacePreview(targetId: Long, convType: Int) {
        launch {
            ChannelStatusManager.getConvChannelPreview(targetId, convType)
        }
    }

    fun updatePortraitAnimationState(targetId: Long, isAnimated: Boolean) {
        viewModelScope.launch {
            _oneShotEventFlow.emit(OneShotEvent.SendMsgAnim(targetId))
        }
    }

    private fun updateBtnAddLoadingStatus(targetId: Long, isBtnAddLoading: Boolean) {
        val originData = addingFriendSetFlow.value
        val new = originData.toMutableSet()
        if (isBtnAddLoading){
            new.add(targetId)
        }else {
            new.remove(targetId)
        }
        addingFriendSetFlow.value = new
    }

    fun getCurrentItemTargetId() = selectedItem.value.targetIdOrNull()

    fun getCurrentItemConvType() = selectedItem.value.let {
        if (it is HomeSelectedItem.Group) {
            GROUP
        } else if (it is HomeSelectedItem.User) {
            PRIVATE
        } else {
            null
        }
    }

    /**
     * Return first WT item in home page list (filter "Add Friend" button)
     */
    fun getFirstItemInList(): HomeConversationItem? {
        logDebug(TAG, "getFirstItemInList: size ==> ${homeList.value?.size}")
        return homeList.value?.firstOrNull { it.mainItem is HomeConversationItem }?.mainItem as? HomeConversationItem
    }


    fun scrollToFirstCanOpenVEPanelTargetId() {
        val convItem = getFirstItemInList()
        logDebug(TAG, "scrollTo: $convItem")
        if (convItem == null) {
            return
        }
        val convType = if (convItem is HomeGroupItem) GROUP else PRIVATE
        val scrollEvent = ScrollEvent(
            convItem.targetId,
            canPending = true,
            expiredUptimeMillsWhenPending = SystemClock.uptimeMillis() + 500,
            afterAction = ScrollEvent.Action.OpenVEPanel(convItem.targetId, convType)
        )
        scrollToTarget(scrollEvent)
    }

    fun reportResultRB2024102106(businessType: Int){
        viewModelScope.launch {
            val friendNum = userRepository.getAllFriendsFromCache().size
            FTUEHomeTracker.onResultRB2024102106(friendNum = friendNum, businessType = businessType)
        }
    }

    fun reportRevisitOnboardingInFTUE() {
        viewModelScope.launch {
            val friendNum = userRepository.getAllFriendsFromCache().size
            CommonTracker.onClickAC2024102104(friendNum = friendNum)
        }
    }

    fun reportNotificationProblemNotComplete() {
        val allAccessOpen = NotificationUtil.isNotifyOpen() &&
                    NotificationUtil.isIgnoringBatteryOptimizations()
        if (allAccessOpen) {
            return
        }
        launchIO {
            val ret = client.reportFeatureGuideComplete(
                // TODO HWL Merge Code
                RequestReportFeatureGuideComplete(
                    type = 2,
                    source = "0",
                    extra = null
                )
            )
            logDebug(TAG, "reportNotificationProblemNotComplete==>code=${ret.code}")
        }
    }


    override fun onCleared() {
        super.onCleared()
        homePreviewPlayer.stopPlayingPreview(null)
    }

    fun updateVELatestTimestamp(timestamp: Int) {
        launch {
            VoiceEmojiRepository.updateVELatestEntryNotifyTimestamp(timestamp)
        }
    }

    fun openOrCloseMoreDialog(isOpen:Boolean) {
        _moreDialogStateFlow.value = isOpen
    }

    fun resendMessage(convType: IM5ConversationType, msgId: Long) {
        viewModelScope.launch {
            IMAgent.resendMsg(convType, msgId)
        }
    }

    fun cancelSendingMessage(convType: IM5ConversationType, msgId: Long) {
        viewModelScope.launch {
            val (message, error) = IMAgent.cancelSendingMessage(convType, msgId)
            logInfo(TAG, "cancelSendingMessage result,msgId:${msgId}, error:$error")
        }
    }

    fun onClickFileDownload(context: Context, filePreview: FilePreview) {
        viewModelScope.launch {
            val url = filePreview.fileRemoteUrl ?: return@launch
            val status = imFileDownloadRepository.getDownloadStateInfo(
                url = url,
                fileName = filePreview.fileName,
                checkDatabase = true
            ).state
            when (status) {
                SUCCESS -> {}
                STARTED -> {
                    imFileDownloadRepository.pause(url = url, fileName = filePreview.fileName)
                }
                PAUSED -> {
                    imFileDownloadRepository.resume(url = url, fileName = filePreview.fileName)
                }
                else -> { // IDLE, CANCEL, PENDING, FAILURE
                    if (!isNetworkAvailable) {
                        toastNetworkErrorTips()
                        return@launch
                    }
                    val file = imFileDownloadRepository.getDownloadFile(url, filePreview.fileName)
                    val fileDownloadedSize = file?.length() ?: 0L
                    if (fileDownloadedSize == filePreview.fileSize && filePreview.fileSize > 0L) {
                        // File already exists locally, try to open it
                        val previewWithLocalPath = filePreview.copy(fileLocalPath = file?.absolutePath)
                        openFileInternal(context, previewWithLocalPath)
                        return@launch
                    }
                    if (imFileDownloadRepository.allowDownloadWithMobileData) {
                        startDownload(filePreview)
                    } else {
                        // Show confirmation dialog
                        _showFileDownloadDialogFlow.emit(filePreview)
                    }
                }
            }
        }
    }

    fun startDownload(filePreview: FilePreview) {
        if (filePreview.fileRemoteUrl.isNullOrEmpty()) return
        viewModelScope.launch {
            imFileDownloadRepository.download(
                url = filePreview.fileRemoteUrl!!,
                fileName = filePreview.fileName,
                serverFileSize = filePreview.fileSize,
                conversationType = filePreview.baseMsgInfo.convType,
                serMsgId = filePreview.baseMsgInfo.serMsgId,
                targetId = filePreview.baseMsgInfo.convTargetId.toString(),
                fromId = filePreview.baseMsgInfo.fromUid.toString(),
                source = "home_preview_file_message"
            )
        }
    }

    private suspend fun openFileInternal(context: Context, filePreview: FilePreview) {
        ChatTracker.onClickToOpenFile(
            isPrivate = filePreview.baseMsgInfo.convType == PRIVATE,
            targetId = getCurrentItemTargetId().toString(),
            clickFromHomePage = true,
            extension = filePreview.fileExtension
        )
        val result = imFileDownloadRepository.openFile(
            context = context,
            localPath = filePreview.fileLocalPath,
            remoteUrl = filePreview.fileRemoteUrl,
            fileName = filePreview.fileName,
            serMsgId = filePreview.baseMsgInfo.serMsgId,
            conversationType = filePreview.baseMsgInfo.convType,
            targetId = filePreview.baseMsgInfo.convTargetId.toString(),
            fromId = filePreview.baseMsgInfo.fromUid.toString(),
        )
        when (result) {
            is FileOpenResult.Success -> {
                // Update unread count
                previewMsg(
                    filePreview.baseMsgInfo.convType,
                    filePreview.baseMsgInfo.convTargetId,
                    filePreview.baseMsgInfo.msgId
                )
            }
            is FileNotFound -> {
                toast(R.string.file_not_found.asString())
            }
            is NoSupportedApp -> {
                toast(R.string.not_supported_to_open_file.asString())
            }
            is IsApk -> {
                toast(R.string.not_supported_to_open_file.asString())
                logInfo(TAG, "APK file detected: ${filePreview.fileName}")
            }
            is FileOpenResult.Error -> {
                toast(R.string.error_try_again.asString())
                logError(TAG, result.exception.message, "Error opening file, ${filePreview.fileName}")
            }
        }
    }

    fun updateLivePlaceOpenStatus(
        openedList: List<ConvChannelInfo>,
        lifecycleOwner: LifecycleOwner
    ) = launchIO {
        openedList.forEach {
            val targetId = it.convTargetId
            // 当空间转为开播时，需要拿本地持久化的上次记录的channelId，和最新的channelId进行比较
            // 如果不一致，则说明空间重新开播了，需要插入居中消息。否则认为空间没有开播状态的变化，不做插入操作
            val lastChannelId =
                userDb.getLivePlaceBaseInfoDao().queryLastChannelId(targetId)?.channelId
            if (lastChannelId != it.channelId) {
                val name = UserRelationCacheManager.getUserRelationInfoByUidSync(targetId)
                    ?.getDisplayName() ?: ""
                val text = ResUtil.getString(R.string.live_place_opened, name)
                logInfo(TAG, "insertCenteredMsg userId:${it.convTargetId}, newChannelId:${it.channelId}")
                IMAgent.insertCenteredMsg(
                    lifecycleOwner = lifecycleOwner,
                    fromId = UserSessionManager.uid.toString(),
                    targetId = it.convTargetId.toString(),
                    convType = PRIVATE,
                    createTime = it.updateTimestamp,
                    text = text,
                    digest = text,
                )
                // 插入成功后，更新本地持久化的channelId
                userDb.getLivePlaceBaseInfoDao().insertLastChannelId(
                    LivePlaceLastChannelId(
                        uid = targetId,
                        channelId = it.channelId
                    )
                )
            } else {
                logInfo(TAG, "no need insertCenteredMsg bcz same channelId with before")
            }
        }
    }
}