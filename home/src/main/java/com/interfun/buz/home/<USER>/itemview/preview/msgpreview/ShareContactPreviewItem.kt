package com.interfun.buz.home.view.itemview.preview.msgpreview

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.chat.R
import com.interfun.buz.chat.wt.entity.HomeMsgPreviewModel
import com.interfun.buz.home.databinding.ChatPreviewItemShareContactMessageBinding
import kotlinx.coroutines.CoroutineScope

/**
 * Author: ChenYouSheng
 * Date: 2025/7/10
 * Email: <EMAIL>
 * Desc:
 */
class ShareContactPreviewItem @JvmOverloads constructor
    (context: Context, attrs: AttributeSet? = null) :
    BaseMsgPreviewItem<HomeMsgPreviewModel.ShareContactPreview>(context, attrs) {


    private val binding by lazy {
        ChatPreviewItemShareContactMessageBinding.inflate(LayoutInflater.from(context), this)
    }

    override fun setMessage(
        previewData: HomeMsgPreviewModel.ShareContactPreview,
        scope: CoroutineScope?,
        info: HomeMsgPreviewModel.ConvInfo
    ) {
        binding.tvTextMessage.text =
            if (previewData.isGroupContact) {
                String.format(
                    R.string.contact_card_group_contact.asString(),
                    previewData.displayName
                )
            } else {
                String.format(
                    R.string.contact_card_user_contact.asString(),
                    previewData.displayName
                )
            }
        binding.portraitImageView.setPortrait(previewData.portrait)
        super.setMessage(previewData, scope, info)
    }
}