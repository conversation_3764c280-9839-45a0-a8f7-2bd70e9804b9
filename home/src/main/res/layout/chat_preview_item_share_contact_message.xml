<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/transparent"
    android:clipChildren="false"
    tools:background="@color/home_conv_list_bg"
    tools:layout_height="@dimen/home_preview_list_item_height"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <View
        android:id="@+id/space_top"
        android:layout_width="match_parent"
        android:layout_height="10dp" />

    <TextView
        android:id="@+id/tvTextMessage"
        style="@style/text_body_large"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/home_wt_item_preview_horizontal_margin"
        android:layout_marginEnd="12dp"
        android:ellipsize="end"
        android:gravity="start"
        android:maxLines="3"
        android:textColor="@color/color_text_white_secondary"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/viewPortraitBg"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/space_top"
        tools:text="[Contact] XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX" />


    <View
        android:id="@+id/viewPortraitBg"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:background="@drawable/share_contact_group_portrait_background"
        app:layout_constraintBottom_toBottomOf="@+id/portraitImageView"
        app:layout_constraintEnd_toEndOf="@+id/portraitImageView"
        app:layout_constraintStart_toStartOf="@+id/portraitImageView"
        app:layout_constraintTop_toTopOf="@+id/portraitImageView" />


    <com.interfun.buz.common.widget.view.PortraitImageView
        android:id="@+id/portraitImageView"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginEnd="@dimen/home_wt_item_preview_horizontal_margin"
        android:background="@color/overlay_white_10"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/space_top"
        tools:src="@drawable/common_user_default_portrait_round" />


    <com.interfun.buz.base.widget.round.RoundView
        android:id="@+id/roundView"
        android:layout_width="18dp"
        android:layout_height="18dp"
        android:layout_marginEnd="-3dp"
        android:layout_marginBottom="-3dp"
        android:background="@color/home_conv_list_bg"
        app:layout_constraintBottom_toBottomOf="@+id/portraitImageView"
        app:layout_constraintEnd_toEndOf="@+id/portraitImageView"
        app:round_top_left_radius="4dp" />

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftvContact"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/ic_contacts_solid"
        android:textColor="@color/text_white_important"
        android:textSize="16dp"
        app:layout_constraintBottom_toBottomOf="@+id/roundView"
        app:layout_constraintEnd_toEndOf="@+id/roundView"
        app:layout_constraintStart_toStartOf="@+id/roundView"
        app:layout_constraintTop_toTopOf="@+id/roundView" />

</merge>