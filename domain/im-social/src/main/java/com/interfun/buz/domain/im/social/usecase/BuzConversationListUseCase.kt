package com.interfun.buz.domain.im.social.usecase

import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.base.ktx.onFirstCost
import com.interfun.buz.domain.im.social.entity.Conversation
import com.interfun.buz.domain.im.social.entity.GroupConversation
import com.interfun.buz.domain.im.social.entity.UserConversation
import com.interfun.buz.im.IMAgent
import com.interfun.buz.social.repo.GroupRepository
import com.interfun.buz.social.repo.UserRepository
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.*
import java.util.ArrayList
import java.util.HashSet
import java.util.LinkedList
import javax.inject.Inject

class BuzConversationListUseCase @Inject constructor(
    private val userRepository: UserRepository,
    private val groupRepository: GroupRepository,
    private val imRepository: IMAgent
) {

    companion object {
        const val TAG = "BuzConversationListUseCase"
    }

    operator fun invoke(forceRefresh: Boolean = false): Flow<List<Conversation>> =
        combine(
            getImConversationList().onFirstCost { logDebug(TAG,"im convList with info cost:${it}") },
            userRepository.getAllFriendsFlow(forceRefresh).onFirstCost { logDebug(TAG,"friendList cost:${it}") },
            groupRepository.getAllJoinedGroupsFlow(forceRefresh).onFirstCost { logDebug(TAG,"groupList cost:${it}") }
        ) { convList, friendList, groupList ->
            val imConvTargetSet = convList.mapTo(HashSet(convList.size)) { it.convId }
            val notInImFriends = friendList.mapNotNull { friend ->
                if (friend.user.userId in imConvTargetSet) {
                    null
                } else {
                    UserConversation(friend.user.userId, 0, 0, null, friend)
                }

            }
            val notInImGroups = groupList.mapNotNull { group ->
                if (group.buzGroup.groupId in imConvTargetSet) {
                    null
                } else {
                    GroupConversation(group.buzGroup.groupId, 0, 0, null, group)
                }
            }
            val result =
                ArrayList<Conversation>(imConvTargetSet.size + notInImFriends.size + notInImGroups.size)
            result.addAll(convList)
            result.addAll(notInImFriends)
            result.addAll(notInImGroups)
            return@combine result
        }.flowOn(Dispatchers.Default)
            .onFirstCost {
                logDebug(TAG,"whole convList cost:${it}")
            }


    private fun getImConversationList(): Flow<List<Conversation>> =
        imRepository.getConversationListFlow().onFirstCost { logDebug(TAG,"im convList cost:${it}") }.flatMapLatest { imConvList ->
            val userIds = LinkedList<Long>()
            val groupIds = LinkedList<Long>()
            imConvList.forEach {
                val targetId = it.targetId.toLongOrNull() ?: return@forEach
                if (it.convType == IM5ConversationType.PRIVATE.value) {
                    userIds.add(targetId)
                } else if (it.convType == IM5ConversationType.GROUP.value) {
                    groupIds.add(targetId)
                }
            }
            combine(
                userRepository.getUserCompositeFlow(userIds),
                groupRepository.getGroupCompositeFlow(groupIds)
            ) { userMap, groupMap ->
                imConvList.mapNotNull { imConv ->
                    val targetId = imConv.targetId.toLongOrNull() ?: return@mapNotNull null
                    when (imConv.convType) {
                        IM5ConversationType.PRIVATE.value -> {
                            val userComposite = userMap[targetId]
                            if (userComposite?.relationInfo?.isBlocked == true) {
                                null
                            } else {
                                UserConversation(
                                    targetId,
                                    imConv.notPlayedCount,
                                    imConv.mentionMeCount,
                                    imConv.lastMessage,
                                    userComposite
                                )
                            }
                        }

                        IM5ConversationType.GROUP.value -> {
                            val groupComposite = groupMap[targetId]
                            GroupConversation(
                                targetId,
                                imConv.notPlayedCount,
                                imConv.mentionMeCount,
                                imConv.lastMessage,
                                groupComposite
                            )
                        }

                        else -> {
                            null
                        }
                    }
                }
            }
        }.flowOn(Dispatchers.Default)

}