package com.interfun.buz.chat.common.interfaces

import android.content.Context
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import androidx.core.text.buildSpannedString
import androidx.fragment.app.*
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.viewbinding.ViewBinding
import com.alibaba.android.arouter.facade.Postcard
import com.alibaba.android.arouter.facade.callback.NavigationCallback
import com.drakeet.multitype.MultiTypeAdapter
import com.interfun.buz.base.ktx.*
import com.interfun.buz.base.utils.ScreenUtil
import com.interfun.buz.base.utils.VibratorUtil
import com.interfun.buz.biz.center.voicemoji.model.collection.CollectionModifyType
import com.interfun.buz.biz.center.voicemoji.model.collection.CollectionType
import com.interfun.buz.biz.center.voicemoji.model.voiceemoji.VoiceEmojiEntity
import com.interfun.buz.chat.ChatNavigation
import com.interfun.buz.chat.R
import com.interfun.buz.chat.common.entity.*
import com.interfun.buz.chat.common.entity.asr.AsrState
import com.interfun.buz.chat.common.ktx.asBuzMediaItem
import com.interfun.buz.chat.common.ktx.isMsgPlayingOrOnCalling
import com.interfun.buz.chat.common.manager.ChatQuickReactPlayManager
import com.interfun.buz.chat.common.manager.TranslationMessageManager
import com.interfun.buz.chat.common.manager.TranslationMessageManager.translateResult
import com.interfun.buz.chat.common.utils.ChatTracker
import com.interfun.buz.chat.common.view.block.ChatMsgListBlock
import com.interfun.buz.chat.common.view.block.IBottomMenuPanelAction
import com.interfun.buz.chat.common.view.block.IChatMorePanelAction
import com.interfun.buz.chat.common.view.block.IHistoryReplyItemListener
import com.interfun.buz.chat.common.view.fragment.ChatExportVoiceFragment
import com.interfun.buz.chat.common.view.widget.*
import com.interfun.buz.chat.common.viewmodel.ChatMediaPreviewViewModel
import com.interfun.buz.chat.common.viewmodel.ChatMsgViewModelNew
import com.interfun.buz.chat.common.viewmodel.MentionGroupMemberAction
import com.interfun.buz.chat.databinding.ChatFragmentMsgListBinding
import com.interfun.buz.chat.forward.fragment.ChatTargetListFragment
import com.interfun.buz.chat.forward.viewmodel.ChatTargetItem
import com.interfun.buz.chat.group.view.dialog.GroupInfoDialog
import com.interfun.buz.chat.group.view.fragment.GroupChatFragment
import com.interfun.buz.chat.group.viewmodel.GroupChatMsgViewModelNew
import com.interfun.buz.chat.group.viewmodel.GroupChatViewModel
import com.interfun.buz.chat.map.receive.view.LocationDetailActivity
import com.interfun.buz.chat.map.receive.view.model.LocationDetailInfo
import com.interfun.buz.chat.media.view.fragment.ChatMediaPreviewListFragment
import com.interfun.buz.chat.media.viewmodel.MediaDownloadViewModel
import com.interfun.buz.chat.voicemoji.manager.AbsVoicePlayListener
import com.interfun.buz.chat.voicemoji.manager.PlayType.HISTORY_CHAT_PREVIEW
import com.interfun.buz.chat.voicemoji.manager.PlayVoiceEmojiWrapper
import com.interfun.buz.chat.voicemoji.utils.VoiceMojiTracker
import com.interfun.buz.chat.voicepanel.callback.VoiceItemCollectResultCallback
import com.interfun.buz.chat.voicepanel.model.CollectVoiceItemData
import com.interfun.buz.chat.voicepanel.viewmodel.VECollectionViewModel
import com.interfun.buz.chat.wt.entity.IMPushMessage
import com.interfun.buz.chat.wt.manager.WTLeaveMsgPlayerManager
import com.interfun.buz.chat.wt.manager.WTMessageManager
import com.interfun.buz.chat.wt.manager.WTVoiceEmojiManager
import com.interfun.buz.chat.wt.utils.WTTracker
import com.interfun.buz.common.arouter.NavManager
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.bean.chat.JumpStartRealTimeCallEntrance
import com.interfun.buz.common.bean.chat.JumpVoiceCallPageFrom.Companion.GROUP_INVITE_MSG
import com.interfun.buz.common.bean.chat.OnlineChatJumpType
import com.interfun.buz.common.bean.push.extra.BuzReactionOperateType
import com.interfun.buz.common.bean.user.BuzUserRelationValue
import com.interfun.buz.common.bean.user.FriendApplySource
import com.interfun.buz.common.bean.voicecall.CallConflictState
import com.interfun.buz.common.bean.voicecall.CallConflictState.ON_CALL_SAME
import com.interfun.buz.common.bean.voicecall.CallType
import com.interfun.buz.common.constants.*
import com.interfun.buz.common.constants.RouterParamKey.Chat
import com.interfun.buz.common.database.entity.UserRelationInfo
import com.interfun.buz.common.eventbus.chat.CloseBottomPanelEvent
import com.interfun.buz.common.gns.GmsStateHelper
import com.interfun.buz.common.ktx.*
import com.interfun.buz.common.manager.*
import com.interfun.buz.common.manager.AppConfigRequestManager.asrFunctionSwitch
import com.interfun.buz.common.manager.cache.user.UserRelationCacheManager
import com.interfun.buz.common.manager.router.RouterManager
import com.interfun.buz.common.service.ContactsService
import com.interfun.buz.common.service.RealTimeCallService
import com.interfun.buz.common.utils.TranslateTracker
import com.interfun.buz.common.utils.shareTextBySystemDialog
import com.interfun.buz.common.utils.toJson
import com.interfun.buz.common.voicecall.ActionType
import com.interfun.buz.common.widget.button.CommonButton
import com.interfun.buz.common.widget.dialog.CommonAlertDialog
import com.interfun.buz.common.widget.dialog.CommonGuideTipsDialog
import com.interfun.buz.common.widget.dialog.bottomlist.CommonBottomListDialog
import com.interfun.buz.domain.im.social.entity.ContactType
import com.interfun.buz.domain.im.social.entity.UserType
import com.interfun.buz.domain.record.helper.RecordStatusHelper
import com.interfun.buz.domain.voiceemoji_im.ktx.convert
import com.interfun.buz.domain.voiceemoji_im.ktx.isBlindBox
import com.interfun.buz.im.entity.FilterSuccess
import com.interfun.buz.im.entity.HyperlinkMetadataExtra
import com.interfun.buz.im.entity.IMType
import com.interfun.buz.im.entity.ShowTranslateTextOp
import com.interfun.buz.im.entity.translation.TranslateState
import com.interfun.buz.im.ktx.*
import com.interfun.buz.im.ktx.BuzSendingState.*
import com.interfun.buz.im.message.*
import com.interfun.buz.media.bean.BuzMediaItem
import com.interfun.buz.media.bean.MediaType
import com.interfun.buz.onair.helper.LivePlaceEntranceHelper
import com.interfun.buz.onair.standard.*
import com.lizhi.component.basetool.ntp.NtpTime
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import com.lizhi.im5.sdk.message.IMessage
import com.lizhi.im5.sdk.message.MessageStatus
import com.lizhi.im5.sdk.message.model.IM5VideoMessage
import com.lizhi.im5.sdk.message.model.IM5VoiceMessage
import com.yibasan.lizhifm.sdk.platformtools.ResUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.json.JSONException
import org.json.JSONObject
import kotlin.text.isEmpty

/**
 * <AUTHOR>
 * @date 2024/3/28
 * @desc
 */
class ChatItemCallbackImpl(private val msgListBlock: ChatMsgListBlock) : ChatItemCallback,
    ChatQROperateCallback , SharedQRCallBack{
    private val TAG = "ChatItemCallbackImpl"
    override val convType: IM5ConversationType get() = msgListBlock.convType
    private val fragment: Fragment get() = msgListBlock.fragment
    private val targetId: Long get() = msgListBlock.targetId
    private val binding: ChatFragmentMsgListBinding get() = msgListBlock.binding
    private val adapter: MultiTypeAdapter get() = msgListBlock.adapter
    private val leaveMsgPlayerManager: WTLeaveMsgPlayerManager get() = msgListBlock.leaveMsgPlayerManager
    private val chatMsgViewModel: ChatMsgViewModelNew get() = msgListBlock.chatMsgViewModel
    private val mediaDownloadViewModel: MediaDownloadViewModel get() = msgListBlock.mediaDownloadViewModel
    private val chatItemList: List<ChatItem>? get() = msgListBlock.chatItemList
    private val chatMediaPreviewViewModel by fragment.activityViewModels<ChatMediaPreviewViewModel>()
    private val collectionViewModel by fragment.fragmentViewModels<VECollectionViewModel>()
    private val longPressViewDelegate = lazy {
        ChatMsgLongPressView(fragment.requireContext).apply { initLongPressView() }
    }
    private val groupChatViewModel by fragment.viewModels<GroupChatViewModel>()
    private val longPressView by longPressViewDelegate
    private val qrHistoryViewDelegate = lazy {
        ChatQRHistoryView(fragment.requireContext).apply { initQRHistoryView() }
    }
    private val qrHistoryView by qrHistoryViewDelegate
    private var interceptOnSave: ((IMessage) -> Boolean) = { false }
    private val screenWidthLazy by lazy {
        ScreenUtil.getScreenWidth(msgListBlock.fragment.requireContext)
    }
    private val isGroup: Boolean get() = convType == IM5ConversationType.GROUP
    private val isRobot:Boolean get() = isGroup.not() && targetId.isRobot()
    private val groupCallStater get() = msgListBlock.groupCallStarter
    private val privateCallStarter get() = msgListBlock.privateCallStarter

    companion object {
        val MEDIA_PREVIEW_CONTAINER = com.interfun.buz.common.R.id.common_container
        val BOTTOM_COPY_LINK_DIALOG_TAG = "RoundBottomCopyLinkDialog"
    }

    override fun getScreenWidth(): Int {
        return screenWidthLazy
    }

    fun isLongPressViewShowing(): Boolean {
        return longPressViewDelegate.isInitialized() && longPressView.parent != null && longPressView.isVisible()
    }

    fun onBackPressed(): Boolean {
        return if (longPressViewDelegate.isInitialized()) {
            longPressView.onBackPressed()
        } else if (qrHistoryViewDelegate.isInitialized()) {
            qrHistoryView.onBackPressed()
        } else {
            false
        }
    }

    fun setInterceptOnSaveEvent(decider: (IMessage) -> Boolean) {
        interceptOnSave = decider
    }

    override fun onItemClick(binding: ViewBinding, item: BaseChatMsgItemBean) {
        if (RecordStatusHelper.isPressingOrLocking) {
            toast(R.string.recording_in_progress_check_later)
            return
        }
        var hideBottomPanel = true
        var hideKeyBoard = true
        when (item) {
            is ChatMsgBaseVoiceGifItemBean -> {
                hideBottomPanel = false
                hideKeyBoard = false
                onReplayVoiceGif(item)
            }

            is ChatMsgSendVoiceItemBean,
            is ChatMsgReceivePrivateVoiceItemBean,
            is ChatMsgReceiveGroupVoiceItemBean,
            is ChatMsgSendVoiceTextItemBean,
            is ChatMsgReceiveVoiceTextItemBean -> {
                hideBottomPanel = false
                hideKeyBoard = false
                togglePlayOrPause(item)
            }

            is IVoiceEmojiItemBean -> {
                hideBottomPanel = false
                hideKeyBoard = false
                onReplay(item)
            }

            is ChatMsgLocationMsgItemBean -> openLocation(item)

            is ChatMsgSendImageItemBean,
            is ChatMsgReceiveImageItemBean -> onPreviewImage(item)

            is ChatMsgReceiveVideoItemBean,
            is ChatMsgSendVideoItemBean -> onPreviewVideo(item)

            is ChatMsgSendDecryptFailItemBean,
            is ChatMsgReceiveDecryptFailItemBean -> gotoE2EEIntrductionView()

            is ChatMsgReceiveTextItemBean,
            is ChatMsgSendTextItemBean -> {
                hideBottomPanel = false
                hideKeyBoard = false
            }

            is ChatMsgSendRealTimeCallItemBean,
            is ChatMsgReceiveRealTimeCallItemBean -> {
                joinOrStartRealTimeCall(item)
            }

            is ChatMsgSendFileItemBean,
            is ChatMsgReceiveFileItemBean -> {
                val message = item.msg
                ChatTracker.onClickToOpenFile(
                    isPrivate = message.isPrivate,
                    targetId = targetId.toString(),
                    clickFromHomePage = false,
                    extension = (message.content as? BuzFileMessage)?.extName ?: ""
                )
                chatMsgViewModel.openFile(fragment.requireContext, message)
            }

            is ChatMsgReceiveShareContactItemBean,
            is ChatMsgSendShareContactItemBean -> {
                jumpProfilePage(item.msg)
            }
        }
        if (hideBottomPanel) {
            hideChatBottomPanel()
        }
        if (hideKeyBoard) {
            hideSoftInputAndOtherView()
        }
    }

    private fun openLocation(item: ChatMsgLocationMsgItemBean) {
        if (GmsStateHelper.gmsAvailable()) {
            val intent = LocationDetailActivity.newInstance(
                fragment.requireContext,
                LocationDetailInfo(
                    item.obtainBuzLocation(),
                    item.obtainBuzAddr(),
                    item.msg.msgId,
                    item.msg.conversationType
                ), targetId.toString(), convType
            )
            fragment.requireActivity().startActivity(intent)
        } else {
            MapLocationHelper.openMap(
                fragment.requireActivity(),
                item.obtainBuzLocation().lat,
                item.obtainBuzLocation().lon,
                item.obtainBuzAddr().featureName
            )
        }
    }

    override fun onLongClick(anchorView: View, portraitView: View?, item: BaseChatMsgItemBean) {
        if (item is ISupportTranslation) {

            //如果返回的 ASR 信息证明这个这个语音消息,产品需求要求完成 ASR 才能显示翻译
            val asrShowRet = (item.obtainAsrInfo() == null || item.obtainAsrInfo()
                ?.isSuccessAndShow() == true)
            val showTranslationBtn =
                //翻译工具使用serMsgId作为 ID,因此没有产生的时候不允许去触发翻译
                if (item.msg.serMsgId != null &&
                    //shouldBeTranslation如果返回 true,表示当前 item 应该出现就要展示 UI,
                    //item.msg.translateResult.isShow 如果当前为 false 那么表示翻译成功且触发了翻译但是翻译语言和源语言一直
                    //产品需求手动触发翻译的时候不管源语言和目标是否一直都需要展示
                    (!item.msg.translateResult.shouldBeTranslation())
                ) {
                    //如果返回的 ASR 信息证明这个这个语音消息,产品需求要求完成 ASR 才能显示翻译
                    asrShowRet
                } else {
                    false
                }

            val needHideOpenTranslation =  //翻译工具使用serMsgId作为 ID,因此没有产生的时候不允许去触发翻译
                if (asrShowRet && item.msg.translateResult.shouldBeTranslation()) {
                    true
                } else {
                    false
                }
            val translationConfigBack = TranslationConfig(
                openTranslationCallBack = {
                    onTranslation(item)
                }, needShowOpenTranslation = showTranslationBtn,
                needHideOpenTranslation = needHideOpenTranslation,
                hideTranslationCallBack = {
                    hideTranslation(item)
                })
            showLongClickPopup(item, anchorView, portraitView, null, translationConfigBack)
        } else {
            showLongClickPopup(item, anchorView, portraitView, null, null)
        }
    }

    fun showLongClickPopup(
        item: BaseChatMsgItemBean,
        anchorView: View,
        portraitView: View?,
        callback: IChatMsgLongPressViewListener? = null,
        translationConfig: TranslationConfig? = null
    ) {
        fragment.context ?: return
        if (item.msg.isNull()) return
        if (longPressView.isShowing()) return
        VibratorUtil.vibrator()
        dismissLongPressView()
        ChatTracker.onClickAC2024032101(convType, targetId, item.msgType, item.msg.hasVoiceFilter)
        if (longPressViewDelegate.isInitialized()) {
            longPressView.checkQRBarIsInit(fragment.viewLifecycleScope)
        }
        if (longPressView.parent == null) {
            val baseParams = LongPressViewParams(
                fragment = fragment,
                anchorView = anchorView,
                portraitView = portraitView,
                item = item,
                convType = convType,
                targetId = targetId.toString(),
                isRead = item.extra.isRead,
                showPortrait = false,
                onViewOpCallback = callback,
                isLongClickTranslation = translationConfig?.curLongClickTranslation == true,
                actionList = emptyList() // 占位，后面再设置
            )
            // 后面如果要新增弹窗选项，直接修改[buildChatLongPressActionList]方法即可
            val actionListLambda = { item: BaseChatMsgItemBean ->
                buildChatLongPressActionList(
                    item = item,
                    translationConfig = translationConfig,
                    baseParams = baseParams
                )
            }
            val params = baseParams.copy(actionList = actionListLambda.invoke(item))
            longPressView.showLongPressView(
                params = params,
                updateActionCallback = actionListLambda
            )
            eventTrackForTranslation(translationConfig, item)
            (fragment.view?.parent as? ViewGroup)?.addView(longPressView)
        }
        hideChatBottomPanel()
        hideSoftInputAndOtherView()
    }

    /**
     * 在调用 showLongClickPopup 前，先根据条件构建 长按后展示的 option列表
     * 长按选项see[ChatLongPressAction]
     */
    private fun buildChatLongPressActionList(
        item: BaseChatMsgItemBean,
        translationConfig: TranslationConfig?,
        baseParams: LongPressViewParams
    ): List<ChatLongPressAction> {
        val actions = mutableListOf<ChatLongPressAction>()
        var hasAsrTextAndShown = item.getAsrInfo().let {
            it != null && it.showAsrText && it.asrText?.isNotEmpty() == true && asrFunctionSwitch
        }
        val isOfficial = item.msg.userId?.let { UserRelationCacheManager.getUserRelationInfoByUid(it)?.isOfficial }.getBooleanDefault()
        val isLongClickTranslation = translationConfig?.curLongClickTranslation == true

        // 文本复制
        if (translationConfig?.curLongClickTranslation != true) {
            if (item.isText || item.isVoiceTextMsg || hasAsrTextAndShown) {
                actions += ChatLongPressAction.Copy { msg -> onCopy(msg) }
            }
        }


        // 媒体保存
        if (item.isMedia && item.msg.isStatusSuccess) {
            actions += ChatLongPressAction.Save { msg -> onSave(msg) }
        }

        // 举报
        val isSendByMe = item.msg.isSend
        val isMsgSupportReport = when (item.msgType) {
            ChatMsgType.WT,
            ChatMsgType.Voice,
            ChatMsgType.Text,
            ChatMsgType.VoiceText,
            ChatMsgType.Image,
            ChatMsgType.Video,
            ChatMsgType.VoiceGif,
            ChatMsgType.File -> true
            else -> false
        }
        if (isOfficial.not() && !isSendByMe && isMsgSupportReport && translationConfig?.curLongClickTranslation != true) {
            actions += ChatLongPressAction.Report { msg, msgType -> onReport(msg, msgType) }
        }

        // 转发
        if (item.isSupportForwardType && item.msg.canForwardMsg() && isOfficial.not() && translationConfig?.curLongClickTranslation != true) {
            actions += ChatLongPressAction.Forwarded { msg, targets -> onForwardMsg(msg, targets) }
        }

        // 回复
        if (item.msg.canReplyMsg() && translationConfig?.curLongClickTranslation != true) {
            actions += ChatLongPressAction.Reply { msg, original -> onReplyMsg(msg, original) }
        }

        // 收藏
        if (item.msg.canAddToFavoritesMsg()) {
            actions += ChatLongPressAction.AddToFavorites { msg -> onAddToFavoritesMsg(msg) }
        }

        // 仅语音时展示Transcribe或者语音转文本后展示HideTranscribe
        if (item is BaseChatMsgVoiceItemBean && item.isWTMsg && item.msg.serMsgId != null
            && translationConfig?.curLongClickTranslation != true
        ) {
            val shouldShowTranscribeButton = AppConfigRequestManager.asrFunctionSwitch
            val asrInfo = item.asrInfo
            // Hide Transcription
            if (shouldShowTranscribeButton && ((asrInfo.showAsrText && asrInfo.asrText != null)
                        || (asrInfo.asrState == AsrState.LOADING))
            ) {
                actions += ChatLongPressAction.HideTranscribe { voiceItem ->
                    onTranscribe(voiceItem, false)
                }
            } else if (shouldShowTranscribeButton) {
                // Transcribe
                actions += ChatLongPressAction.ShowTranscribe { voiceItem ->
                    onTranscribe(voiceItem, true)
                }
            }

            // Report Transcription
            if (shouldShowTranscribeButton && ((asrInfo.showAsrText && asrInfo.asrText != null)
                        || (asrInfo.asrState == AsrState.LOADING))
            ) {
                actions += ChatLongPressAction.ReportTranscribe { msg -> onReportTranscribe(msg) }
            }
        }

        // 支持翻译的item，长按后展示Translate 或者 Hide Translation
        if (translationConfig != null && item is ISupportTranslation) {
            // Translate
            if (translationConfig.needShowOpenTranslation) {
                actions += ChatLongPressAction.ShowTranslate(showDivider = !isLongClickTranslation) {
                    translationConfig.openTranslationCallBack?.invoke(item)
                }
            }

            // Hide Translation
            if (translationConfig.needHideOpenTranslation) {
                actions += ChatLongPressAction.HideTranslate(showDivider = !isLongClickTranslation) {
                    translationConfig.hideTranslationCallBack?.invoke(item)
                }
            }

            // change language
            if (translationConfig.needChangeLanguage && translationConfig.changeLanguageCallBack != null) {
                actions += ChatLongPressAction.SwitchLanguage {
                    translationConfig.changeLanguageCallBack.invoke(item)
                }
            }
            // copy translation
            if (translationConfig.needCopy && translationConfig.copyCallBack != null) {
                actions += ChatLongPressAction.CopyTranslate(showDivider = false) {
                    translationConfig.copyCallBack.invoke(item)
                }

            }
        }


        // 尝试声音滤镜选项
        if (!isLongClickTranslation && item.msg.hasVoiceFilter
            && ABTestManager.showVoiceFilter && AppConfigRequestManager.openVoiceFilterFunction
        ) {
            actions += ChatLongPressAction.TryFilter {
                onTryVoiceFilter(item.msg.getVoiceFilterId)
            }
        }


        // 尝试声音滤镜选项
        if (!isLongClickTranslation && item.msg.isWTVoiceMessage
            && item.msg.isSend && item.msg.isStatusSuccess
//            && AppConfigRequestManager.voiceFilterVideoTemplateUrl.isNotEmpty()
//            && AppConfigRequestManager.voiceFilterVideoTemplateMd5.isNotEmpty()
        ) {
            actions += ChatLongPressAction.ExportVoice {
                onExportVoice(item.msg)
            }
        }

        /**
         *More的出现时机：
         *  - Delete 始终展示在菜单栏最后一项，不随【More】折叠隐藏；
         *  - 操作项折叠判断逻辑中不计入 Delete；
         *  - 当"非 Delete 的操作项数量 > 5"时启用折叠机制；
         *    - 非 Delete 操作项 ≤ 5 项，所有操作项 + Delete 全部直接展示；不出现 More
         *    - 非 Delete 操作项 > 5 项，展示前 5 项 +【More】（折叠其余项）+ Delete（始终展示在最后）
         */
        val showMoreOption = actions.size > 5
        val sortList = actions.sortedBy { it.priority }
        // 展开时展示的选项列表
        val normalOptionList = if (showMoreOption) { sortList.take(5) } else sortList
        // 点击more进入折叠后的选项列表
        val moreOptionList = (if (showMoreOption) { sortList - normalOptionList } else emptyList()).toMutableList()
        if (moreOptionList.isNotEmpty()) {
            // 将more列表最后一个item的分割线隐藏掉
            val lastOptionInMore = moreOptionList.lastOrNull()?.withoutDivider()
            if (null != lastOptionInMore) {
                moreOptionList[moreOptionList.size - 1] = lastOptionInMore
            }
        }

        // 最终展示的 option（前5项 + Delete + More）
        val finalOptionList = mutableListOf<ChatLongPressAction>()
        finalOptionList.addAll(normalOptionList)
        // 取消发送
        if (item.msg.buzSendingState is Uploading
            || item.msg.buzSendingState is Compressing
            || item.msg.buzSendingState is Sending
            || (item.msg.buzSendingState is Canceled && item.msg.isFileMessage)
        ) {
            finalOptionList += ChatLongPressAction.Cancel(
                cancelAction = { msg -> onClickCancel(msg) },
                deleteAction = { msg -> showDeleteOptionDialog(msg) }
            )
        }
        // 删除消息
        else if (!isLongClickTranslation) {
            finalOptionList += ChatLongPressAction.Delete { msg -> showDeleteOptionDialog(msg) }
        }

        // more
        if (!isLongClickTranslation && showMoreOption) {
            // 折叠后的More选项，点击返回到原先的列表
            val backToMainOption = ChatLongPressAction.MoreBack {
                longPressView.showLongPressView(baseParams.copy(actionList = finalOptionList), isSwitchMore = true)
            }
            // More选项，点击进入折叠后的列表
            val moreOption = ChatLongPressAction.More {
                longPressView.showLongPressView(baseParams.copy(actionList = moreOptionList + backToMainOption), isSwitchMore = true)
            }

            finalOptionList += moreOption
        }

        logDebug(TAG,"buildChatLongPressActionList actions=${actions}， moreOptionList=${moreOptionList}， finalOptionList=${finalOptionList}")

        return finalOptionList
    }

    private fun eventTrackForTranslation(
        translationConfig: TranslationConfig?,
        item: BaseChatMsgItemBean
    ) {
        if (item !is ISupportTranslation) {
            return
        }

        if (translationConfig?.needHideOpenTranslation == true) {
            TranslateTracker.onElementExposureEE2024092403(convType, item.typeTrackString())
        }
        if (translationConfig?.needShowOpenTranslation == true) {
            TranslateTracker.onElementExposureEE2024092402(convType, item.typeTrackString())
        }
    }

    /***
     * 用户在弹出菜单中点击了翻译
     */
    private fun onTranslation(baseChatMsgVoiceItemBean: ISupportTranslation) {
        userLifecycleScope?.launchIO {
            baseChatMsgVoiceItemBean.obtainMsg().updateShowTranslateText(ShowTranslateTextOp.MANUAL_OPEN)
            if (baseChatMsgVoiceItemBean.obtainMsg().translateResult.state == TranslateState.TranslateSuccess) {
                if (baseChatMsgVoiceItemBean is ChatItem) {
                    chatMsgViewModel.updateChatItemAsync(
                        baseChatMsgVoiceItemBean, baseChatMsgVoiceItemBean,
                        ChatMsgItemPayloadType.UpdateTranslationState
                    )
                }
            } else {
                //调用TranslateManager触发翻译
                TranslationMessageManager.translateMessage(baseChatMsgVoiceItemBean.obtainMsg(), true)
                chatMsgViewModel.updateTotalChatTranslation(baseChatMsgVoiceItemBean.obtainMsg(), 1)
            }
        }
        TranslateTracker.onClickAC2024092402(convType, baseChatMsgVoiceItemBean.typeTrackString())
        dismissLongPressView()

    }

    override fun onResendClick(item: BaseChatMsgItemBean) {
        chatMsgViewModel.resendMsg(item.msg)
        hideChatBottomPanel()
        hideSoftInputAndOtherView()
    }

    override fun onLinkClick(url: String) {
        chatMsgViewModel.parseIMLink(url, fragment.activity)
        hideChatBottomPanel()
        hideSoftInputAndOtherView()
    }

    override fun onLinkLongClick(url: String, hyperLinkMetadataExtra: HyperlinkMetadataExtra?) {
        logDebug(TAG, "onLinkLongClick, image path: ${hyperLinkMetadataExtra?.linkImagePath}\n")
        val httpRegex = "^(https?://)?".toRegex()
        val userPressLink = url.replace(httpRegex, "")
        val metadataLink = hyperLinkMetadataExtra?.linkUrl?.replace(httpRegex, "")
        val image = hyperLinkMetadataExtra?.linkImagePath?.takeIf { userPressLink == metadataLink }
        CommonBottomListDialog.build {
            addTag(BOTTOM_COPY_LINK_DIALOG_TAG)
            addCaptionOption( // URL
                imageRes = image,
                title = url,
                autoDismiss = false
            )
            addLineOption() // Line separator
            addDefaultOption( // Copy Link
                iconRes = R.string.ic_copy,
                title = R.string.chat_copy_link.asString()
            ) {
                url.copyToClipboard()
                toastRegularCorrect(R.string.common_copy_link_to_clipboard_tip)
            }
        }.showDialog(fragment.childFragmentManager)
    }

    override fun onTtsTextUpdateCallback(item: BaseChatMsgItemBean) {
        if (chatItemList.isLast(item)) {
            binding.rvMsgList.smoothScrollToPosition(chatItemList?.size.getIntDefault() - 1)
        }
    }

    override fun onQRHistoryClick(item: BaseChatMsgItemBean) {
        fragment.context ?: return
        if (item.msg.isNull()) return
        dismissQRHistoryView()
        if (qrHistoryView.parent == null) {
            qrHistoryView.show(item)
            (fragment.view?.parent as? ViewGroup)?.addView(qrHistoryView)
        }
        ChatTracker.onClickAC2024032104(item.extra?.quickReactInfoList?.size ?: 0)
        hideChatBottomPanel()
        hideSoftInputAndOtherView()
    }

    override fun onUploadMediaButtonClick(item: BaseChatMsgItemBean) {
        // chatMsgViewModel.uploadOrPlayMediaMessage(item.msg, item)
        // hideVoiceMojiPanelImmediately()
        // hideSoftInputAndOtherView()
    }

    override fun onCancelCompressing(item: BaseChatMsgItemBean) {
        chatMsgViewModel.cancelCompressing(item)
    }

    override fun onClickSendStateButton(binding: ViewBinding, item: BaseChatMsgItemBean) {
        val state = item.msg.buzSendingState
        if (state is BuzSendingState.Failed || state is BuzSendingState.Succeed || state is BuzSendingState.Sending) {
            onItemClick(binding, item)
        } else {
            chatMsgViewModel.onClickSendStateButton(item)
        }
    }

    override fun onLongClickSendStateButton(anchorView: View, item: BaseChatMsgItemBean) {
        onLongClick(anchorView = anchorView, portraitView = null, item = item)
    }

    override fun onRouteJump(scheme: String, extraData: String) {
        logInfo(TAG, "scheme: $scheme, extraData: $extraData")
        val activity = fragment.activity ?: return
        RouterManager.handle(activity, scheme, extraData)
        try {
            // 只有在 KEY_OPEN_VOICE_FILTER 为 true 的时候才不需要关闭聊天页
            val extraDataJson = if (extraData.isEmpty()) null else JSONObject(extraData)
            val openVoiceFilter = extraDataJson?.optBoolean(Chat.KEY_OPEN_VOICE_FILTER)
            if (openVoiceFilter != true) activity.finish()
        } catch (e: JSONException) {
            e.log(TAG)
        }
    }

    override fun onRequestIDLAction(serMsgId: Long, apiAction: ApiAction) {
        if (apiAction.isRamadan()) {
            // Ramadan 业务
            val isGroup = convType == IM5ConversationType.GROUP
            logInfo(TAG, "\nrequest ramadan IDL: serMsgId=$serMsgId, callbackParam=${apiAction.callbackParam}, isGroup=$isGroup, targetId=$targetId")
            reportOnClickRamadanEventAction(
                isGroup = isGroup,
                targetId = targetId,
                callbackParam = apiAction.callbackParam
            )
            handleRamadanAction(serMsgId, apiAction.callbackParam)
        }
    }

    private fun handleRamadanAction(serMsgId: Long, callbackParam: String) {
        chatMsgViewModel.onRequestRamadanAction(
            serMsgId = serMsgId,
            callbackParam = callbackParam
        )
    }

    private fun reportOnClickRamadanEventAction(isGroup: Boolean, targetId: Long, callbackParam: String) {
        try {
            val callbackParamJson = JSONObject(callbackParam)
            val callbackType = callbackParamJson.optInt("callbackType")
            ChatTracker.onClickButtonInRamadanMessage(
                isGroup = isGroup,
                convId = targetId.toString(),
                action = callbackType.toString()
            )
        } catch (e: JSONException) {
            e.log(TAG)
        }
    }

    override fun onPreviewMediaItem(item: BaseChatMsgItemBean) {
        if (item.isVideoMsg) {
            onPreviewVideo(item)
        } else if (item.isImageMsg) {
            onPreviewImage(item)
        }
    }

    fun ISupportTranslation.typeTrackString(): String {
        return if (this.obtainType() == ChatMsgType.Text) "word" else "voicemsg"
    }

    override fun onTranslationLongClick(
        anchorView: View,
        portraitView: View?,
        item: BaseChatMsgItemBean
    ) {
        if (item !is ISupportTranslation) {
            return
        }
        val translationConfig = TranslationConfig(
            hideTranslationCallBack = {
                hideTranslation(item)
            },
            changeLanguageCallBack = {
                routerServices<ContactsService>().value
                    ?.getTranslationLangSettingDialog("long_press_msg")
                    ?.show(fragment.childFragmentManager, "TranslationLangSettingFragment")
                dismissLongPressView()
            },

            needHideOpenTranslation = true,
            curLongClickTranslation = true,
            needCopy = item.msg.translateResult.isSuccess(),
            copyCallBack = { item ->
                (item.obtainMsg().translateResult.translateText ?: "").copyToClipboard()
                ChatTracker.onClickCopyMessageText(targetId.isRobot(), targetId)
                dismissLongPressView()
            },
            needChangeLanguage = item.msg.translateResult.isSuccess(),
            openTranslationCallBack = {

            }

        )
        showLongClickPopup(item, anchorView, portraitView, object : IChatMsgLongPressViewListener {
            override fun onDismiss(view: ChatMsgLongPressView) {
                anchorView.backgroundTintList = null
            }
        }, translationConfig)
    }

    private fun hideTranslation(item: ISupportTranslation) {
        if (item is BaseChatMsgItemBean) {
            TranslateTracker.onClickAC2024092403(
                conversationType = convType,
                item.typeTrackString()
            )
            //关闭翻译
            chatMsgViewModel.hideTranslationMessage(item)
            dismissLongPressView()
        }
    }

    /**
     * 进行翻译重试
     */
    override fun onRetryTranslation(item: BaseChatMsgItemBean) {
        val linearLayoutManager = binding.rvMsgList.layoutManager as LinearLayoutManager
        val firsP = linearLayoutManager.findFirstVisibleItemPosition()
        val lastP = linearLayoutManager.findLastVisibleItemPosition()
        for (index in firsP..lastP) {
            val curSelectItem = adapter.items.getOrNull(index)
            if (curSelectItem is ISupportTranslation && curSelectItem.obtainMsg().translateResult.state == TranslateState.TranslateFail) {
                TranslationMessageManager.translateMessage(curSelectItem.obtainMsg(), true)
            }
        }
    }

    override fun onTranslationExpandAni(item: BaseChatMsgItemBean) {
        val itemIndex = adapter.items.indexOf(item)
        logInfo(
            TAG,
            "onTranslationExpandAni item = [${item}] translateResult = [${item.msg.translateResult}]  itemIndex = [${itemIndex}] size = [${adapter.items.size}]"
        )
        val linearLayoutManager = binding.rvMsgList.layoutManager as LinearLayoutManager
//        val firsP = linearLayoutManager.findFirstVisibleItemPosition()
        val lastP = linearLayoutManager.findLastVisibleItemPosition()
        if (lastP == itemIndex) {
            binding.rvMsgList.smoothScrollToPosition(lastP)
        }
    }

    override fun onReplyClickItem(
        item: BaseChatMsgItemBean,
        replyItemView: ReplyItemView,
        data: ReplyPreviewData
    ) {
        fragment.obtainRegisterInterface2(IHistoryReplyItemListener::class.java)
            ?.onReplyClickItem(replyItemView, data)
    }

    override fun onReplyClickPreviewIcon(
        item: BaseChatMsgItemBean,
        replyItemView: ReplyItemView,
        data: ReplyPreviewData
    ) {
        fragment.obtainRegisterInterface2(IHistoryReplyItemListener::class.java)
            ?.onReplyClickPreviewIcon(replyItemView, data)
    }

    override fun onReplyClose(
        item: BaseChatMsgItemBean,
        replyItemView: ReplyItemView,
        data: ReplyPreviewData
    ) {
        //在聊天历史中没有关闭选项所以不需要处理
    }

    override fun onAsrClick(item: BaseChatMsgItemBean) {
        msgListBlock.onOneKeyAsrClick(item)
    }

    override fun onOneClickTranslateClick(item: BaseChatMsgItemBean) {
        msgListBlock.onOneKeyTranslateClick(item)
    }

    private fun togglePlayOrPause(item: BaseChatMsgItemBean) {
        val voiceCallService = routerServices<RealTimeCallService>().value
        val onAirService = routerServices<IGlobalOnAirController>().value
        val isCalling = voiceCallService?.isOnRealTimeCall() == true
        val isOnAir = onAirService?.isInOnAir() == true
        if (isCalling) {
            toastLeaveCurrentVoiceCall()
            return
        }

        if (isOnAir) {
            toast(R.string.exit_current_call_and_try.asString())
            return
        }

        // 根据产品需求，这里如果是声音滤镜的Voice的话，需要成功才能播放
        if (item.msg.hasVoiceFilter) {
            if (item.msg.content is BuzVoiceMsg) {
                val canPlay =
                    (item.msg.content as? BuzVoiceMsg)?.voiceFilterInfo?.filterState == FilterSuccess
                if (canPlay.not()) {
                    return
                }
            }
        }

        if (item is IVoiceEmojiItemBean) {
            ChatTracker.onClickVoiceEmojiInHistory(
                unicodeEmoji = item.obtainWTVoiceEmojiMsg().emojiIcon,
                id = "${item.obtainWTVoiceEmojiMsg().emojiId}",
                isBlindBox = item.obtainWTVoiceEmojiMsg().isBlindBox()
            )
            if (item.extra.audioMsgState == AudioMsgState.PLAYING) {
                VoiceMojiTracker.onClickAC20231120605(
                    from = "chat",
                    isBlindBox = item.obtainWTVoiceEmojiMsg().isBlindBox(),
                    voiceMojiID = "${item.obtainWTVoiceEmojiMsg().emojiId}"
                )
            }
        }
        checkAudioVolume()
        val isListened = item.msg.isVoiceListened()
        WTTracker.postLeaveMsgPlayClick(
            if (convType == IM5ConversationType.GROUP) "group" else "private",
            targetId.toString(),
            if (isListened) 1 else 0,
            when (item.msgType) {
                ChatMsgType.WT,
                ChatMsgType.Voice,
                ChatMsgType.VoiceText -> "voice_message"

                ChatMsgType.VoiceEmoji -> "VE"
                ChatMsgType.VoiceGif -> "VG"
                else -> null
            }
        )
        leaveMsgPlayerManager.savedClickedItemListeningStatus(isListened)
        leaveMsgPlayerManager.togglePlayOrPause(item)
    }

    private fun checkAudioVolume() {
        userLifecycleScope?.launchIO {
            if (isVolumeMuted) {
                userLifecycleScope?.launchMain {
                    toastIconFontMsg(
                        message = R.string.chat_volume_is_to_low_to_listen.asString(),
                        textColor = R.color.text_white_default.asColor(),
                        iconFont = R.string.ic_sound_close.asString(),
                        iconFontColor = R.color.text_white_important.asColor(),
                        style = IconToastStyle.ICON_TOP_TEXT_BOTTOM

                    )
                }
            }
        }
    }

    private fun onPreviewImage(item: BaseChatMsgItemBean) {
        item.msg.asBuzMediaItem()?.let {
            jumpToMediaPreviewList(it)
        }
        if (item is ChatMsgReceiveImageItemBean) {
            if (item.forceHD) return
            val index = adapter.items.indexOf(item)
            if (index in adapter.items.indices) {
                item.forceHD = true
                adapter.notifyItemChanged(index)
            }
        }
    }

    private fun onPreviewVideo(item: BaseChatMsgItemBean) {
        item.msg.asBuzMediaItem()?.let {
            jumpToMediaPreviewList(it)
        }
        if (item is ChatMsgReceiveVideoItemBean) {
            item.forceHD = true
            val index = adapter.items.indexOf(item)
            if (index in adapter.items.indices) {
                adapter.notifyItemChanged(index)
            }
        }
    }

    private fun jumpToMediaPreviewList(item: BuzMediaItem) {
        val msg = item.imMessage ?: return
        val isVideo = item.type == MediaType.Video
        val bundle = Bundle()
        bundle.putLong(Chat.KEY_TARGET_ID, targetId)
        bundle.putLong(Chat.KEY_MESSAGE_ID, msg.msgId)
        bundle.putBoolean(Chat.KEY_IS_PRIVATE, msg.isPrivate)
        bundle.putBoolean(Chat.KEY_IS_VIDEO, isVideo)
        bundle.putParcelable(Chat.KEY_MEDIA_ITEM, item)
        bundle.putString(Chat.KEY_TRANSITION_NAME, "share_img_${msg.msgId}")
        fragment.activity?.supportFragmentManager?.apply {
            registerFragmentLifecycleCallbacks(object :
                FragmentManager.FragmentLifecycleCallbacks() {
                override fun onFragmentDetached(fm: FragmentManager, f: Fragment) {
                    chatMediaPreviewViewModel.setPreviewPageShow(false)
                }

                override fun onFragmentPreAttached(
                    fm: FragmentManager,
                    f: Fragment,
                    context: Context
                ) {
                    chatMediaPreviewViewModel.setPreviewPageShow(true)
                }
            }, true)
            commit {
                add(
                    MEDIA_PREVIEW_CONTAINER,
                    ChatMediaPreviewListFragment.newInstance(bundle),
                    "ChatMediaPreviewListFragment"
                )
            }
        }
    }

    private fun gotoE2EEIntrductionView() {
        NavManager.startE2EEIntrductionActivity(
            fragment.activity
        )
    }

    private fun joinOrStartRealTimeCall(item: BaseChatMsgItemBean) {
        val realTimeCallInviteMsg = item.msg.content as? RealTimeCallInviteMsg ?: return
        //如果通话还在进行中，弹出二次确认加入通话
        if (realTimeCallInviteMsg.status == CallState.Start) {
            if (isGroup) {
                groupCallStater?.joinGroupVoiceCall(
                    context= <EMAIL>(),
                    groupId = targetId,
                    channelId = realTimeCallInviteMsg.channelId,
                    source = GROUP_INVITE_MSG,
                    callType = realTimeCallInviteMsg.callType)
            }
        }
        // 如果通话已结束，则弹出二次确认弹出，询问用户是否要发起通话
        else if (realTimeCallInviteMsg.status != CallState.Start) {
            if (isGroup) {
                groupCallStater?.showGroupCallSelectMemberDialog(
                    groupId = targetId,
                    callType = realTimeCallInviteMsg.callType,
                    actionType = ActionType.START,
                    entrance = JumpStartRealTimeCallEntrance.CALLING_CARD,
                ) { dialogResult ->
                    if (dialogResult.curCallConflictState == CallConflictState.NO_CONFLICT) {
                        showStartNewRTCallDialog(callType = realTimeCallInviteMsg.callType) {
                            dialogResult.showDialogHelper.startShow()
                        }
                    } else if (dialogResult.curCallConflictState == ON_CALL_SAME) {
                        // 在当前房间，点击直接加入
                        dialogResult.jumpHelper.startJump(OnlineChatJumpType.reentryFromMinimize)
                    }
                }
            } else {
                privateCallStarter?.startRealTimeCall(
                    targetId = targetId,
                    callType = realTimeCallInviteMsg.callType,
                    entrance = JumpStartRealTimeCallEntrance.CALLING_CARD
                ) { callResult ->
                    if (callResult.curCallConflictState == CallConflictState.NO_CONFLICT) {
                        showStartNewRTCallDialog(callType = realTimeCallInviteMsg.callType) {
                            val callConflict = callResult.startHelper.startCall()
                            if (callConflict == CallConflictState.NO_CONFLICT) {
                                callResult.jumpHelper.startJump(OnlineChatJumpType.callInvitation)
                            }
                        }
                    } else if (callResult.curCallConflictState == CallConflictState.ON_CALL_SAME) {
                        // 在当前房间，点击直接加入
                        callResult.jumpHelper.startJump(OnlineChatJumpType.reentryFromMinimize)
                    }
                }
            }
        }
    }

    private fun jumpProfilePage(msg: IMessage) {
        val msgContent = msg.content as BuzShareContactMessage
        val inviterId = msg.fromId
        val serMsgId = msg.serMsgId.toSafeLong()
        val traceId = msg.msgTraceId
        if (msgContent.contactType == ContactType.User.type) {
            if (msgContent.userType == UserType.Normal.type && msgContent.id.isMe()) {
                R.string.contact_card_self_card.asString().toast()
            } else {
                routerServices<ContactsService>().value!!.getProfileDialog(
                    userId = msgContent.id,
                    source = FriendApplySource.conversation, /* chat list source */
                    trackerSource = ProfileSource.ON_HOME_PAGE.source,
                    businessId = null,
                ).showDialog(fragment.activity)
            }
        } else if (msgContent.contactType == ContactType.Group.type) {
            val dialog = GroupInfoDialog.newInstance(groupId = msgContent.id, inviterId = inviterId)
            dialog.showDialog(fragment.activity)
        }
        chatMsgViewModel.reportShareContactMsgClick(
            id = msgContent.id,
            targetId = targetId,
            contactType = msgContent.contactType,
            userType = msgContent.userType,
            conversationType = convType,
            serverMsgId = serMsgId,
            traceId = traceId
        )
    }

    private fun showStartNewRTCallDialog(callType:@CallType Int, positiveCallback: () -> Unit) {
        if (fragment.isDetached) return
        val context = fragment.context ?: return
        CommonAlertDialog(
            context = context,
            title = if (isGroup) {
                if (CallType.isVideoCall(callType))
                    R.string.rtc_start_videocall_for_currentvideocall_end.asString()
                else
                    R.string.rtc_start_videocall_for_currentvoicecall_end.asString()
            } else {
                if (CallType.isVideoCall(callType))
                    R.string.rtc_whether_to_open_videocall.asString()
                else
                    R.string.rtc_whether_to_open_voicecall.asString()
            },
            positiveButtonType = CommonButton.TYPE_SECONDARY_MEDIUM,
            positiveText = buildSpannedString{
                size(20.dp) {
                    iconFontAlign {
                        typeface(FontUtil.fontIcon!!) {
                            append(
                                if (CallType.isVideoCall(callType)) R.string.ic_video.asString()
                                else R.string.ic_tel.asString()
                            )
                        }
                    }
                }
                appendSpace(8.dp)
                append(R.string.call.asString())
            },
            negativeText = R.string.cancel.asString(),
            positiveCallback = {
                positiveCallback.invoke()
                it.dismiss()
            },
            negativeCallback = {
                it.dismiss()
            }
        ).show()
    }



    fun dismissLongPressView() {
        longPressViewDelegate.takeIf { it.isInitialized() }?.value?.dismiss()
    }

    fun dismissQRHistoryView() {
        qrHistoryViewDelegate.takeIf { it.isInitialized() }?.value?.dismiss()
    }

    private fun onCopy(message: IMessage) {
        when (message.msgType) {
            IMType.TYPE_TEXT_MSG -> {
                message.content?.mentionedInfoText()?.copyToClipboard()
                ChatTracker.onClickCopyMessageText(targetId.isRobot(), targetId)
            }

            IMType.TYPE_VOICE_TEXT, IMType.TYPE_VOICE_TEXT_NEW -> {
                (message.content as? VoiceTextMsg)?.text?.copyToClipboard()
                ChatTracker.onClickCopyMessageText(targetId.isRobot(), targetId)
            }

            IMType.TYPE_VOICE_MSG -> {
                var asrText = (message.content as? BuzVoiceMsg)?.asrText
                if (asrText == null) {
                    try {
                        asrText = message.localExtraModel().asrText
                        (message.content as? BuzVoiceMsg)?.asrText = asrText
                    } catch (e: Exception) {
                        logError(TAG, " $e")
                    }
                }
                asrText?.copyToClipboard()
                ChatTracker.onClickCopyMessageText(targetId.isRobot(), targetId)
            }

            IMType.TYPE_MEDIA_TEXT_NEW,
            IMType.TYPE_MEDIA_TEXT -> {
                (message.content as? MediaTextMsg)?.title?.copyToClipboard()
                ChatTracker.onClickCopyMessageText(targetId.isRobot(), targetId)
            }
        }
        dismissLongPressView()
    }

    fun onSave(message: IMessage) {
        if (interceptOnSave.invoke(message)) {
            return
        }

        val mediaItem = if (message.isImageMessage) {
            val content = message.content as BuzImageMessage
            BuzMediaItem(
                mediaId = message.msgId,
                mediaUri = content.imageUrl().getStringDefault(),
                width = content.imageWidth,
                height = content.imageHeight,
                thumbnailUrl = content.thumbImageUrl().getStringDefault(),
                type = MediaType.Image,
                orientation = content.orientation,
                imMessage = message
            )
        } else if (message.isVideoMessage) {
            val content = message.content as IM5VideoMessage
            BuzMediaItem(
                mediaId = message.msgId,
                mediaUri = content.videoUrl().getStringDefault(),
                width = content.videoWidth,
                height = content.videoHeight,
                thumbnailUrl = content.coverThumbUrl().getStringDefault(),
                hdCoverUrl = content.coverRemoteUrl ?: "",
                type = MediaType.Video,
                imMessage = message
            )
        } else {
            return
        }
        mediaDownloadViewModel.downloadMedia(mediaItem)
        dismissLongPressView()
    }

    private fun onReplayVoiceGif(item: ChatMsgBaseVoiceGifItemBean) {
        if ((item.msg.content as? IM5VoiceMessage)?.remoteUrl.isNullOrBlank()) {
            R.string.sound_unavailable.toast()
            return
        }

        if (WTMessageManager.isPlaying) {
            if ((WTMessageManager.currentPlayingMsg as? IMPushMessage)?.message?.serMsgId == item.msg.serMsgId) {
                WTMessageManager.playNext("ChatHistory click to stop")
                return
            }
        }

        if (leaveMsgPlayerManager.isLeaveMsgPlaying()) {
            if (leaveMsgPlayerManager.getCurrentMsgId() == item.msg.serMsgId) {
                leaveMsgPlayerManager.togglePlayOrPause(item)
                return
            }
        }

        if (isMsgPlayingOrOnCalling()) {
            return
        }
        dismissLongPressView()
        togglePlayOrPause(item)
    }

    private fun onReplay(item: BaseChatMsgItemBean) {
        if (isMsgPlayingOrOnCalling()) {
            return
        }
        dismissLongPressView()
        togglePlayOrPause(item)
        val wtVoiceEmojiMsg = (item.msg.content as? WTVoiceEmojiMsg) ?: return
        val emojiEntity = wtVoiceEmojiMsg.convert()
        val playVoiceEmojiWrapper = PlayVoiceEmojiWrapper(
            emojiEntity,
            type = HISTORY_CHAT_PREVIEW
        )
        WTVoiceEmojiManager.startPlayAniEffect(playVoiceEmojiWrapper,
            object : AbsVoicePlayListener() {
                override fun onTouch() {
                    super.onTouch()
                    WTVoiceEmojiManager.stopEffect()
                    leaveMsgPlayerManager.pauseAudio()
                }
            })
    }

    private fun onReport(msg: IMessage, chatMsgType: ChatMsgType) {
        chatMsgViewModel.reportMsg(msg, chatMsgType)
        dismissLongPressView()
    }

    private fun onDelete(msg: IMessage) {
        chatMsgViewModel.deleteMessage(fragment, msg, targetId.toString())
    }

    private fun onTranscribe(bean: BaseChatMsgVoiceItemBean, show: Boolean) {
        dismissLongPressView()
        if (show) {
            chatMsgViewModel.transcribeMessage(bean)
        } else {
            chatMsgViewModel.hideTranscribeMessage(bean)
        }
    }

    private fun onReportTranscribe(msg: IMessage) {
        dismissLongPressView()
        chatMsgViewModel.reportTranscribe(msg)
    }

    private fun onTryVoiceFilter(voiceFilterId: Long?) {
        fragment.obtainRegisterInterface2(IChatMorePanelAction::class.java)
            ?.openVoiceFilterPanel(voiceFilterId = voiceFilterId)
        dismissLongPressView()
    }

    private fun onExportVoice(msg: IMessage) {
        dismissLongPressView()
        val msgContent = msg.content as IM5VoiceMessage
        val filterId = when (msgContent) {
            is BuzVoiceMsg -> msgContent.voiceFilterInfo?.filterId
            is VoiceTextMsg -> msgContent.voiceFilterInfo?.filterId
            else -> null
        }.getLongDefault()
        ChatTracker.onClickExportVoice(
            msg.isGroup.not(),
            msg.targetId.toString(),
            filterId.toString()
        )
        ChatExportVoiceFragment.newInstance(msg)
            .show(fragment.childFragmentManager, ChatExportVoiceFragment.TAG)
    }

    private fun ChatQRHistoryView.initQRHistoryView() {
        itemCallback = object : ChatQRHistoryItemCallback {
            override fun onItemClick(item: ChatQRHistoryItemBean) {
                if (item.userId == UserSessionManager.uid) return
                val activity = fragment.activity ?: appContext.activity
                if (activity !is FragmentActivity) return
                routerServices<ContactsService>().value?.getProfileDialog(
                    userId = item.userId,
                    source = FriendApplySource.AVATAR_FROM_CHAT_HISTORY,
                    businessId = null,
                    trackerSource = ProfileSource.CHAT_HISTORY_NAME.source,
                    isFromGroup = convType == IM5ConversationType.GROUP
                )?.showDialog(activity)
            }

            override fun onQRClick(item: ChatQRHistoryItemBean) {
                dismissQRHistoryView()
                val findItem = chatItemList?.firstOrNull {
                    it is BaseChatMsgItemBean && it.msg.serMsgId == item.msg.serMsgId
                }
                val msg = (findItem as? BaseChatMsgItemBean)?.msg ?: item.msg
                onPlayQuickReact(msg, item.userId, item.voicemoji)
            }

            override fun onTapToRemoveClick(item: ChatQRHistoryItemBean) {
                dismissQRHistoryView()
                val findItem = chatItemList?.firstOrNull {
                    it is BaseChatMsgItemBean && it.msg.serMsgId == item.msg.serMsgId
                }
                val msg = (findItem as? BaseChatMsgItemBean)?.msg ?: item.msg
                onRemoveQuickReact(msg, item.voicemoji)
            }
        }
    }

    private fun ChatMsgLongPressView.initLongPressView() {
        qrOperateCallback = this@ChatItemCallbackImpl
    }

    fun onPlayQuickReact(msg: IMessage, userId: Long, voicemoji: VoiceEmojiEntity) {
        chatMsgViewModel.handleReactionMsg(
            listOf(msg),
            ChatQRPayloadInfo(BuzReactionOperateType.PLAY, userId, voicemoji.id)
        )
    }

    override fun onAddQuickReact(msg: IMessage, voicemoji: VoiceEmojiEntity) {
        onQuickReactOperating(msg, BuzReactionOperateType.ADD, voicemoji)
    }

    override fun onRemoveQuickReact(msg: IMessage, voicemoji: VoiceEmojiEntity) {
        ChatTracker.onClickAC2024032105(voicemoji.id)
        onQuickReactOperating(msg, BuzReactionOperateType.REMOVE, voicemoji)
    }

    override fun onReplaceQuickReact(msg: IMessage, old: VoiceEmojiEntity, new: VoiceEmojiEntity) {
        onQuickReactOperating(msg, BuzReactionOperateType.REPLACE, new, old)
    }

    override fun needFlashReplyBackground(msg: IMessage): Boolean {
        return chatMsgViewModel.canFlashReplyViewItem(msg)
    }

    override fun onClearFlashReplyBackground() {
        chatMsgViewModel.onClearFlashReplyBackground()
    }

    override fun clickJoinLpInvite(msg: IMessage) {
        val buzLivePlaceShareMessage = msg.content as? BuzLivePlaceShareMessage ?: return
        val ownerId = buzLivePlaceShareMessage.ownerId
        if (ownerId < 0) {
            return
        }
        val channelId = buzLivePlaceShareMessage.channelId
        fragment.viewLifecycleScope.launch {
            val userRelation =
                UserRelationCacheManager.getUserRelationInfoByUidSync(ownerId) ?: return@launch
            if (userRelation.serverRelation == BuzUserRelationValue.FRIEND.value) {
                val isReopen = LivePlaceEntranceHelper.reopenLivePlace(
                    activity = fragment.requireActivity(),
                    targetId = ownerId,
                    livePlaceType = LivePlaceType.PRIVATE
                )
                if (isReopen) {
                    return@launch
                }
                CommonAlertDialog(
                    fragment.requireContext(),
                    R.string.live_place_share_sure_to_join.asString(),
                    positiveText = R.string.join.asString(),
                    negativeText = R.string.cancel.asString(),
                    positiveCallback = { dlg ->
                        val roomParam = RoomParam(
                            LivePlaceType.PRIVATE,
                            ActivityReason.JOIN_ROOM,
                            ownerId,
                            true,
                            joinROOMParam = JoinRoomParam(channelId, 1, 3),
                            source = LivePlaceSource.SOURCE_SHARE_CARD_MSG

                        )
                        routerServices<IGlobalOnAirController>().value?.enterOnAir(
                            roomParam,
                            fragment.requireActivity()
                        )
                        dlg.dismiss()
                    },
                    negativeCallback = { dlg ->
                        dlg.dismiss()
                    }).show()
                return@launch
            } else if (userRelation.serverRelation == BuzUserRelationValue.MYSELF.value) {
                val roomParam = RoomParam(
                    LivePlaceType.PRIVATE,
                    ActivityReason.JOIN_ROOM,
                    ownerId,
                    true,
                    JoinRoomParam(channelId, 1, 3),
                            source = LivePlaceSource.SOURCE_SHARE_CARD_MSG

                )
                routerServices<IGlobalOnAirController>().value?.enterOnAir(
                    roomParam,
                    fragment.requireActivity()
                )
            } else {
                CommonAlertDialog(
                    fragment.requireContext(),
                    R.string.live_place_share_no_friend_tips.asString(),
                    positiveText = R.string.add.asString(),
                    negativeText = R.string.cancel.asString(),
                    positiveCallback = { dlg ->
                        NavManager.showPersonalProfile(
                            fragment.requireActivity(),
                            ownerId,
                            FriendApplySource.LP_INVITE_CARD,
                            ProfileSource.ON_INVITE_LP_CARD.source,
                            shouldAutoAddFriend = true
                        )
                        dlg.dismiss()
                    },
                    negativeCallback = { dlg ->
                        dlg.dismiss()
                    }).show()
            }
        }
    }

    private fun onQuickReactOperating(
        msg: IMessage,
        type: BuzReactionOperateType,
        voicemoji: VoiceEmojiEntity,
        oldVe: VoiceEmojiEntity? = null
    ) = fragment.viewLifecycleScope.launch(Dispatchers.IO) {
        if (!isNetworkAvailable) {
            withMainContext {
                toast(R.string.network_error)
            }
            return@launch
        }
        if (voicemoji.isBlindBox()) return@launch
        val userId = msg.fromId.toSafeLong()
        if (msg.isPrivate && msg.isReceive && UserRelationCacheManager.isUserBlocked(userId)) {
            withMainContext {
                toast(R.string.block_success)
            }
            return@launch
        }
        if (msg.isPrivate && msg.isReceive && UserRelationCacheManager.isUserMyFriend(userId)
                .not()
        ) {
            withMainContext {
                toast(R.string.chat_add_friend_first)
            }
            return@launch
        }
        val pushExtra = msgListBlock.buildPushExtra(msg.msgType)
        msg.operateQuickReact(
            userLifecycleOwner,
            type,
            pushExtra,
            voicemoji,
            oldVe
        ) { isResult, message ->
            val payload = if (isResult) {
                null
            } else {
                ChatQRPayloadInfo(type, UserSessionManager.uid, voicemoji.id)
            }
            chatMsgViewModel.handleReactionMsg(listOf(message ?: msg), payload)
            logInfo(
                ChatQuickReactPlayManager.TAG,
                "handleReactionMsg from onQuickReactOperating " +
                        "msg.serMsgId: ${msg.serMsgId}, isResult: $isResult payload: $payload, " +
                        "realQRSize: ${(message ?: msg).reactionInfos?.size ?: 0}"
            )
            if (isResult) {
                when (type) {
                    BuzReactionOperateType.ADD, BuzReactionOperateType.REPLACE -> {
                        ChatTracker.onResultRB2024032102(
                            convType,
                            targetId.toString(),
                            msg.serMsgId,
                            voicemoji.id
                        )
                    }

                    BuzReactionOperateType.REMOVE -> {
                        ChatTracker.onResultRB2024032101(
                            convType,
                            targetId.toString(),
                            msg.serMsgId,
                            voicemoji.id
                        )
                    }

                    else -> {}
                }
            }
        }
    }

    private fun hideChatBottomPanel() {
        CloseBottomPanelEvent.post()
    }

    private fun hideSoftInputAndOtherView() {
        msgListBlock.fragment.obtainRegisterInterface2(IBottomMenuPanelAction::class.java)
            ?.hideSoftInputAndOtherView()
    }

    private fun onClickCancel(msg: IMessage) {
        chatMsgViewModel.cancelAndDeleteLocalMessage(msg)
        dismissLongPressView()
    }

    private fun showDeleteOptionDialog(msg: IMessage) {
        dismissLongPressView()
        CommonBottomListDialog.build {
            addDeleteForMeOption(this, msg)
            addDeleteForEveryoneOption(this, msg)
        }.showDialog(fragment.activity)
    }

    private fun addDeleteForMeOption(build: CommonBottomListDialog.Builder, msg: IMessage) {
        build.addWarningOption(titleRes = R.string.delete_for_me) {
            if (CommonMMKV.showDeleteFroMeTipsDialog) {
                CommonMMKV.showDeleteFroMeTipsDialog = false
                fragment.context?.let {
                    CommonGuideTipsDialog(it)
                        .setTopAnimationUrl("lottie/deleteMsgLottie/chat_delete_for_me.json")
                        .setLottieImagesFolder("lottie/deleteMsgLottie")
                        .setDialogTitle(R.string.what_is_deleted_for_me.asString())
                        .setDialogContent(R.string.what_is_deleted_for_me_detail.asString())
                        .setPositiveText(
                            positiveText = R.string.delete,
                            btnBackgroundRes = R.color.secondary_button_secondary,
                            btnTextColorRes = R.color.secondary_error
                        )
                        .setCancelText(cancelText = R.string.cancel)
                        .setPositiveCallBack {
                            onDelete(msg)
                        }
                        .show()
                }
            } else {
                onDelete(msg)
            }

            ChatTracker.onClickAC2024062504(msg.targetId, msg.conversationType, "delete_for_me")
        }
    }

    private fun addDeleteForEveryoneOption(build: CommonBottomListDialog.Builder, msg: IMessage) {
        if (canShowRecallOptionItem(msg)) {
            val contentStr =
                ResUtil.getString(R.string.what_is_deleted_for_everyone_detail, "1.43.0")
            build.addWarningOption(titleRes = R.string.delete_for_everyone) {
                if (CommonMMKV.showDeleteFroEveryOneTipsDialog) {
                    CommonMMKV.showDeleteFroEveryOneTipsDialog = false
                    fragment.context?.let {
                        CommonGuideTipsDialog(it)
                            .setTopAnimationUrl("lottie/deleteMsgLottie/chat_delete_for_everyone.json")
                            .setLottieImagesFolder("lottie/deleteMsgLottie")
                            .setDialogTitle(R.string.what_is_deleted_for_everyone.asString())
                            .setDialogContent(contentStr)
                            .setPositiveText(
                                positiveText = R.string.delete,
                                btnBackgroundRes = R.color.secondary_button_secondary,
                                btnTextColorRes = R.color.secondary_error
                            )
                            .setCancelText(cancelText = R.string.cancel)
                            .setPositiveCallBack {
                                chatMsgViewModel.recallMessageSync(fragment, msg)
                            }
                            .show()
                    }
                } else {
                    chatMsgViewModel.recallMessageSync(fragment, msg)
                }
                ChatTracker.onClickAC2024062504(
                    msg.targetId,
                    msg.conversationType,
                    "delete_for_everyone"
                )
            }
        }
    }

    private fun canShowRecallOptionItem(msg: IMessage): Boolean {
        return (msg.isSend
                && AppConfigRequestManager.enableMsgRecall == true
                && msg.status == MessageStatus.SUCCESS
                && msgTimeAllowRecall(msg))
    }

    private fun msgTimeAllowRecall(msg: IMessage): Boolean {
        // 如果没有配置，默认24小时
        val msgRecallMaxMillis =
            AppConfigRequestManager.msgRecallMaxMinutes.getLongDefault(24 * 60) * 60 * 1000
        return (NtpTime.nowForce() - msg.createTime) < msgRecallMaxMillis
    }


    private fun onForwardMsg(msg: IMessage, targetList: List<ChatTargetItem>) {
        chatMsgViewModel.forwardMessage(fragment, msg, targetList)
    }

    private fun onReplyMsg(msg: IMessage, item: BaseChatMsgItemBean) {
        chatMsgViewModel.replyMsg(msg = msg)
        ChatTracker.onClickACAC2024112901(
            conversationType = convType,
            businessId = targetId.toString(),
            source = "long_press",
            msgType = item.msgType
        )
    }

    private fun onAddToFavoritesMsg(msg: IMessage) {
        val collectionVeType : CollectionType
        var elementType = "voicemoji_message"
        val objectId =
            when (msg.content) {
            is BuzVoiceGifMsg -> {
                elementType = "VG_message"
                collectionVeType = CollectionType.VoiceGif
                (msg.content as BuzVoiceGifMsg).id
            }

                is WTVoiceEmojiMsg -> {
                    collectionVeType = CollectionType.VoiceEmoji
                    (msg.content as WTVoiceEmojiMsg).emojiId.toString()
                }

                else -> {
                    return
                }
            }

        VoiceMojiTracker.onChatAddToFavorite(
            pageType = if (isGroup) "group" else if (isRobot) "robot" else "private",
            businessId = targetId.toString(),
            elementType = elementType,
            elementId = objectId
        )

        val collectionData =  CollectVoiceItemData(
            collectId = -1,
            objectId = objectId,
            collectionVeType = collectionVeType,
            modifyType = CollectionModifyType.AddToFirst
        )
        collectionViewModel.modifyCollectionData(
            collectionData,
            callback = object : VoiceItemCollectResultCallback {
                override fun onResult(code: Int, collectionData: CollectVoiceItemData) {
                    when (code) {
                        CODE_SUCCESS -> {
                            val hasBeenAddedToFavorite = collectionViewModel.hasBeenAddedToFavoriteStateFlow.value
                            val toastText = if (hasBeenAddedToFavorite) {
                                R.string.ve_has_been_collected.asString()
                            } else {
                                R.string.ve_added_to_fav.asString()
                            }
                            toastIconFontMsg(
                                message = toastText,
                                style = IconToastStyle.ICON_TOP_TEXT_BOTTOM,
                                iconFont = R.string.ic_correct_solid.asString(),
                                textColor = R.color.text_white_secondary.asColor(),
                                iconFontColor = R.color.text_white_important.asColor()
                            )
                        }
                        101 -> {
                            toastSolidWarning(R.string.ve_fav_reach_max_limit) // reach 100 limit
                        }
                        102 -> {
                            toastSolidWarning(R.string.ve_has_been_removed) // ve is removed
                        }
                        else -> {
                            toastSolidWarning(com.interfun.buz.common.R.string.tips_network_error)
                        }
                    }
                }
            })
    }

    /**
     * 转发目标列表dialog被销毁恢复后，重新设置回调
     */
    fun onRestoreForwardTargetListCallBack() {
        ChatTargetListFragment.onRestoreForwardTargetListCallBack(fragment) { msg, list ->
            onForwardMsg(msg, list)
        }
    }

    /**
     * 长按的消息被撤回时，隐藏长按消息view
     */
    fun hideViewByRecallMsg(msg: IMessage): Boolean {
        var result = false
        if (longPressView.isAnchorMsg(msg)) {
            dismissLongPressView()
            result = true
        }
        if (qrHistoryView.isAnchorMsg(msg)) {
            dismissQRHistoryView()
            result = true
        }
        return result
    }

    override fun onLongClickPortraitToMention(info: UserRelationInfo?) {
        if (fragment !is GroupChatFragment) return
        val fragment = fragment as GroupChatFragment
        info?.let {
            fragment.handleMention(
                info = it,
                mentionType = MentionGroupMemberAction.LongClickPortrait
            )
        }
    }



    override fun onRename() {
        ChatTracker.clickAC2025041704("rename", groupId = targetId.toString())
        if (!isGroup) {
            return
        }
        fragment.lifecycleScope.launch {
            //这里暂时只会有群的情况调用
            val groupInfoBean =groupChatViewModel.groupInfoFlow.value?.toOldGroupInfoBean()
//                GroupInfoCacheManager.getGroupInfoBeanByIdSync(targetId)

            if (groupInfoBean==null) {
                return@launch
            }
            RouterManager.handle(
                fragment.requireActivity(),
                RouterSchemes.Group.GROUP_EDIT_INFO,
                JSONObject(
                    mapOf(
                        RouterParamKey.Common.KEY_SOURCE to 1,
                        RouterParamKey.Group.KEY_TRANSITION_NAIM to RouterParamValues.Common.TRANSITION_ANIM_SLIDE_BOTTOM,
                        RouterParamKey.Group.KEY_GROUP_INFO to groupInfoBean.toJson()
                    )
                ).toString(), null, object : NavigationCallback {
                    override fun onFound(postcard: Postcard?) {
                    }

                    override fun onLost(postcard: Postcard?) {
                    }

                    override fun onArrival(postcard: Postcard?) {
                        postcard?.context?.activity?.overridePendingTransition(
                            R.anim.anim_slide_in_bottom,
                            0
                        )
                    }

                    override fun onInterrupt(postcard: Postcard?) {
                    }
                }
            )
        }
    }

    override fun onAddFriend() {
        ChatTracker.clickAC2025041704("add", groupId = targetId.toString())
        ChatNavigation.toAddGroupMembersActivity(
            fragment.requireActivity(),
            targetId,
            false,
            false,
            transitionAnim = RouterParamValues.Common.TRANSITION_ANIM_SLIDE_BOTTOM
        )

    }
    override fun onSharedQR(url: String) {
        ChatTracker.clickAC2025041704("share", groupId = targetId.toString())
        shareTextBySystemDialog( fragment.requireActivity(),url)
    }
    override fun onRefresh() {
        ChatTracker.clickAC2025041704("retry", groupId = targetId.toString())
        (chatMsgViewModel as? GroupChatMsgViewModelNew)?.refreshQr()
    }

    override fun onAutoTranslateTipsClick(item: ChatMsgCenteredTipsMsgItemBean) {
        fragment.activity?.let { activity ->
            val actionJson = item.action?.let { JSONObject(it) } ?: return
            RouterManager.handle(activity, actionJson)
            TranslateTracker.onClickAutoTranslateNotifyTips(
                item.convType,
                item.targetId.toString()
            )
        }
    }

    override fun showConfirmDownloadDialog(onConfirm: DefaultCallback) {
        CommonAlertDialog(
            context = fragment.requireContext,
            tips = R.string.confirm_download_dialog_title.asString(),
            positiveText = R.string.download_anyway.asString(),
            negativeText = R.string.cancel.asString(),
            positiveButtonType = CommonButton.TYPE_SECONDARY_MEDIUM,
            multiOptions = listOf(
                {
                    setText(R.string.download_anyway.asString())
                    setType(CommonButton.TYPE_SECONDARY_MEDIUM)
                    click {
                        it.dismiss()
                        onConfirm.invoke()
                    }
                },
                {
                    setText(R.string.cancel.asString())
                    setBackgroundColor(R.color.overlay_grey_10.asColor())
                    click {
                        it.dismiss()
                    }
                },
            )
        ).show()
    }
}