package com.interfun.buz.chat.wt.block

import android.animation.Animator
import android.annotation.SuppressLint
import android.app.Activity
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.widget.FrameLayout
import androidx.core.text.buildSpannedString
import androidx.core.text.color
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.lifecycleScope
import coil.load
import com.interfun.buz.base.ktx.*
import com.interfun.buz.base.utils.VibratorUtil
import com.interfun.buz.biz.center.voicefilter.compat.VoiceFilterHelper
import com.interfun.buz.biz.center.voicemoji.model.voiceemoji.VoiceEmojiType
import com.interfun.buz.biz.center.voicemoji.repository.voiceemoji.VoiceEmojiRepository
import com.interfun.buz.chat.R
import com.interfun.buz.chat.common.interfaces.ChatItemCallbackImpl
import com.interfun.buz.chat.common.manager.ChatGlobalInfoRecorder
import com.interfun.buz.chat.common.manager.SmartTransManager
import com.interfun.buz.chat.common.utils.ChatTracker
import com.interfun.buz.chat.common.view.activity.ChatHomeActivity
import com.interfun.buz.chat.common.view.activity.TakePhotoSendActivity
import com.interfun.buz.chat.databinding.*
import com.interfun.buz.chat.group.view.activity.GroupChatActivity
import com.interfun.buz.chat.group.view.fragment.GroupChatFragment
import com.interfun.buz.chat.media.view.fragment.ChatMediaPreviewListFragment
import com.interfun.buz.chat.privy.view.activity.PrivateChatActivity
import com.interfun.buz.chat.privy.view.fragment.PrivateChatFragment
import com.interfun.buz.chat.wt.entity.*
import com.interfun.buz.chat.wt.event.GroupInfoDialogHideEvent
import com.interfun.buz.chat.wt.event.UserInfoDialogHideEvent
import com.interfun.buz.chat.wt.event.WTMoreFunctionHidePanelEvent
import com.interfun.buz.chat.wt.event.WTVoiceMojiHidePanelEvent
import com.interfun.buz.chat.wt.manager.*
import com.interfun.buz.common.arouter.NavManager
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.base.BaseActivity
import com.interfun.buz.common.base.BaseManualBlock
import com.interfun.buz.common.bean.chat.ChatJumpType.IM
import com.interfun.buz.common.bean.chat.GroupChatJumpInfo
import com.interfun.buz.common.bean.chat.PrivateChatJumpInfo
import com.interfun.buz.common.bean.common.MentionDisplaySpanOption
import com.interfun.buz.common.bean.push.extra.BuzReactionOperateType
import com.interfun.buz.common.bean.push.extra.IMReactionType
import com.interfun.buz.common.constants.CommonMMKV
import com.interfun.buz.common.database.entity.UserRelationInfo
import com.interfun.buz.common.database.entity.chat.GroupInfoBean
import com.interfun.buz.common.eventbus.HomePageEnum
import com.interfun.buz.common.eventbus.chat.ChatListScrollToEndEvent
import com.interfun.buz.common.eventbus.chat.CloseBottomPanelEvent
import com.interfun.buz.common.eventbus.chat.CloseChatListEvent
import com.interfun.buz.common.eventbus.wt.NotificationInterceptEvent
import com.interfun.buz.common.eventbus.wt.WTListScrollEvent
import com.interfun.buz.common.ktx.*
import com.interfun.buz.common.manager.MuteInfoManager
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.manager.cache.group.GroupInfoCacheManager
import com.interfun.buz.common.manager.cache.user.UserRelationCacheManager
import com.interfun.buz.common.manager.login.LoginMainABTestManager
import com.interfun.buz.common.manager.update.UpdateVersionManager
import com.interfun.buz.common.service.ChatService
import com.interfun.buz.common.service.HomeService
import com.interfun.buz.common.service.RealTimeCallService
import com.interfun.buz.common.utils.AnimatorListener
import com.interfun.buz.common.utils.HyperlinkUtil
import com.interfun.buz.common.utils.RingtonePlayer
import com.interfun.buz.common.widget.dialog.CommonAlertDialog
import com.interfun.buz.domain.im.social.entity.ContactType
import com.interfun.buz.domain.record.helper.RecordStatusHelper
import com.interfun.buz.im.IMAgent
import com.interfun.buz.im.entity.BuzNotifyType
import com.interfun.buz.im.ktx.*
import com.interfun.buz.im.message.*
import com.interfun.buz.onair.standard.IGlobalOnAirController
import com.interfun.buz.voicecall.common.view.activity.BaseRealTimeCallActivity
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import com.lizhi.im5.sdk.message.IMessage
import com.lizhi.im5.sdk.message.MsgDirection
import com.lizhi.im5.sdk.message.model.IM5ImageMessage
import com.lizhi.im5.sdk.message.model.IM5RecallMessage
import com.lizhi.im5.sdk.message.model.IM5TextMessage
import com.lizhi.im5.sdk.message.model.IM5VideoMessage
import com.lizhi.im5.sdk.profile.UserInfo
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import java.util.LinkedList

/**
 * 首页消息弹窗提示，具体效果描述请看：https://vocalbeats.sg.larksuite.com/wiki/BGg6wcKAViXOurkRe1zlgCyRgSf
 * <note>
 *     当前的弹窗没有进行view的复用，主要是复用的时候遇到两个问题：
 *     1.view进行属性动画之后，translationY发生改变，使得复用后的view位置不对。尝试过手动改变translationY，
 *      但是无效，当时没有找到解决方案。
 *     2.如果复用view，popup语音消息时，如果复用view，PAG动画就会变得不可控制，当时出现比较奇葩的问题
 *     考虑到以上两个问题，以及排期紧张，没有多余时间进行排查，所以没有进行view的复用。
 * <note/>
 *
 * @property commonMsgQueue:目前包含：文本、图标、异步语音（未开启auto play）、好友上线消息
 *
 * 2024/10/24
 * 优化方案：https://vocalbeats.sg.larksuite.com/wiki/FHSZwwezsisIyAkAq9AllOTXg9g#share-On3GdGcneoaMVHx0zs0l3vPlgih
 * 1.自动播放开起：
 * 1.1.对准用户：声音类无通知，一条一条顺序播放到，然后在首页预览区域显示头像。其他通知，只有声音播放中、录音中才会出现，否则只有提示声音跟震动，无弹窗；
 * 1.2.非对准用户：声音类通知一条一条顺序播放到才出现。其他通知，都会出现弹窗；
 *
 * 2.自动播放关闭：
 * 2.1.对准用户：所有通知，只有声音播放中、录音中才会出现弹窗；否则只有提示声音跟震动，无弹窗
 * 2.2.非对准用户：所有通知，都会出现弹窗；
 *
 * 3.emoji QR比较特殊，不管对不对准，直接弹出来
 *
 * 4.首页预览消息语音点击重播，只要非对准好友（不区分点击的是自己还是好友），都弹, 对准时不弹
 *
 * 5.首页选中target时，如果VE弹窗或者more功能弹窗展示的时候，非speaking语音的消息都需要展示，包括静音模式下的语音消息
 *
 * 6. 去掉了好友上线通知
 *
 * 7. 布局划分
 * 根布局：[ChatPopupMsgBinding]
 * 实时播放语音+emoji语音+重播语音，都会看到头像的动画，带关闭按钮，共用一个布局：[ChatPopupMsgVoicemojiItemAutoPlayBinding]
 * 表情回复消息：[ChatPopupMsgQuickReactItemBinding]
 * 普通文本、地址、居中命令消息、解密失败消息、实时通话开启/结束卡片要请消息、文件消息、分享联系人卡片消息：[ChatPopupMsgTextItemBinding]
 * 非自动播放时的emoji语音消息：[ChatPopupMsgVoicemojiItemBinding]
 * 非自动播放时的gif语音消息：[ChatPopupMsgVoicegifItemBinding]
 * 图片、视频消息：[ChatPopupMsgImageItemBinding]
 * 非自动播放语音消息，支持ASR：[ChatPopupMsgVoiceItemBinding]
 * 合并消息：[ChatPopupMsgNotifyItemBinding]
 * Live Place 分享消息：[ChatPopupMsgLivePlaceInviteItemBinding]
 *
 * 8. 首页AB的影响：旧首页（A组）对准当前用户，不论quit模式还是avaliable模式，收到非speaking消息后，都弹，因为旧首页没有预览区域
 * 9. 处于画中画模式下的语音/视频通话，不能展示 [BaseRealTimeCallActivity]
 */
class WTPopMessageBlock(val activity:BaseActivity): BaseManualBlock() {
    private val TAG = "WTPopMessageBlock"

    companion object {
        // 记录用户划走或者点击的普通消息，当关闭当前页面，新的页面不再展示
        val removeByUserMessageList = LinkedList<Any>()
        val removeByUserMessageStateFlow = MutableStateFlow<Any?>(null)
    }
    private val commonMsgQueue = LinkedList<Any>()
    private val voiceMsgQueue = LinkedList<Any>()
    private val usingMessageViewQueue = LinkedList<ChatPopupMsgBinding>()
    private val usingMessageMap = LinkedHashMap<ChatPopupMsgBinding, Any>()
    private val contentView = activity.window.findViewById<View>(android.R.id.content) as? FrameLayout
    private val paddingBetween2Msg = R.dimen.wt_pop_layout_margin_top.asDimensionInt()
    /**text/photo/video/localtion/asr语音的item高度是80dp，其它speaking 和 voice emoji item高度是60dp*/
    private val itemMinHeight = R.dimen.wt_pop_layout_height.asDimensionInt()
    private val itemMaxHeight = R.dimen.wt_pop_layout_height_max.asDimensionInt()
    private val itemMarginTop = 10.dp
    private val firstPosition = activity.statusBarHeight.toFloat() + itemMarginTop
    private val secondPositionMax = itemMaxHeight.toFloat()+ paddingBetween2Msg + firstPosition
    private val secondPositionMin = itemMinHeight.toFloat()+ paddingBetween2Msg + firstPosition
    private val notVoiceMsgAliveTime = 5000L
    private val animationDuration = 200L
    private val FIRST_POSITION_TAG = "firstPosition"
    private val SECOND_POSITION_TAG = "secondPosition"
    private val MAX_SHOWING_VIEW_SIZE = 2
    private val wtPopWindowManager by lazy { WTPopWindowManager(activity) }
    private var lastHomePageEnum: HomePageEnum? = null
    private var lastTargetId: Long? = null
    private val shouldShowSpeakingViewFlow = combine(
        WTStatusManager.homeCurrentSelectedConversation,
        WTMessageManager.messageFlow,
        WTLeaveMsgPlayerManager.playStateFlow,
        ChatHomeActivity.currentSelectPageFlow
    ) { selectItemChange, (msg, msgState), lmPlayStatePair, selectPageChange ->
        val replayingMessage = homePreviewPlayingFlow.value
        val conversationId = msg?.getConversationId()?.toLongOrNull()
            ?: replayingMessage?.getConversationId()?.toLongOrNull()?:lmPlayStatePair.second?.getConversationId()?.toLongOrNull()
        // 当前是否定位首页目标
        val isTarget = conversationId?.let { isTarget(it) } ?: false
        // 首页对准收到speaking，然后打开当前的聊天页，不需要展示speaking
        val isInTargetChatList = isInThisConversationChatList(conversationId)
        logInfo(
            TAG, "shouldShowSpeakingViewFlow [combine] selectItemChange=${selectItemChange}" +
                    " (msgState = $msgState, msg = $msg)," +
                    " lmPlayStatePair=${lmPlayStatePair}," +
                    " selectPageChange=${selectPageChange},"+
                    " isTarget=${isTarget},"+
                    " isInTargetChatList=${isInTargetChatList}"+
                    " activity:${activity.simpleName}"
        )
        if (isTarget || isInTargetChatList) {
            logInfo(TAG, "shouldShowSpeakingViewFlow [combine] condition 1 return null")
            return@combine null
        } else if (lmPlayStatePair.first == LMPlayerState.PLAY_WT_MSG) {
            return@combine if (lmPlayStatePair.second == null) {
                logInfo(TAG, "shouldShowSpeakingViewFlow [combine] condition 2 return null")
                null
            } else {
                logInfo(TAG, "shouldShowSpeakingViewFlow [combine] condition 2 return ${IMPushMessage(lmPlayStatePair.second!!)}")
                IMPushMessage(lmPlayStatePair.second!!)
            }
        } else if (lmPlayStatePair.first != LMPlayerState.IDLE) {
            logInfo(TAG, "shouldShowSpeakingViewFlow [combine] condition 3 return null")
            return@combine null
        } else if (isVoiceMojiInChatList(msg, conversationId)) {
            logInfo(TAG, "shouldShowSpeakingViewFlow [combine] condition isVoiceMojiInChatList return null")
            return@combine null
        } else if (replayingMessage != null) {
            return@combine ReplayingMessage(replayingMessage)
        } else {
            logInfo(TAG, "shouldShowSpeakingViewFlow [combine] condition 5 return ${msg}")
            return@combine msg
        }
    }

    private var lastAnimTime = 0L

    private var lastEnqueueMessage: Long? = null
    private val homeService by lazy { routerServices<HomeService>().value }
    private val realTimeCallService by lazy { routerServices<RealTimeCallService>().value }
    private val homePreviewPlayingFlow by lazy {
        homeService?.getHomePlayingPreviewFlow() ?: MutableStateFlow(null)
    }

    // 全局语音播放+列表语音播放+首页重播
    private val playingFlow = combine(
        WTStatusManager.isPlayingFlow,
        WTLeaveMsgPlayerManager.playStateFlow,
        homePreviewPlayingFlow
    ) { globalPlayState, chatListPlayState, homePreviewPlayState ->
        return@combine globalPlayState || chatListPlayState.first != LMPlayerState.IDLE || homePreviewPlayState != null
    }.stateIn(activity.lifecycleScope, SharingStarted.Eagerly, false)

    private val isPlaying get() = playingFlow.value

    private val onWindowFocusChangeListener by lazy {
        ViewTreeObserver.OnWindowFocusChangeListener { hasFocus -> handleHomeActivityLoseFocus(!hasFocus) }
    }

    override fun initData() {
        super.initData()
        // 处理非实时播放语音消息
        handleNoneSpeakingMessage()
        // 语音非实时播放模式下asr处理
        handleVoiceAsrTextMessage()
        // 处理实时语音消息展示和隐藏，以及非speaking消息隐藏
        handleSpeakingMessage()
        // 首页预览语音播放监听
        handleReplayVoiceMessage()
        // 预览页语音播放监听
        handleVoicePreview()
        // quite模式下消息处理
        handleQuiteModeMessage()
        // 非quite模式下无音频的VoiceGif消息处理
        handleNoVoiceMessage()
        // 消息被mute的处理
        handleMuteMessage()
        // 录视频模式下消息处理
        handleMessageWhileTakingVideo()
        // 当移除顶级页面的pop msg时，其它页面也要同步移除
        handleTopMessageRemoveSync()
        // 添加首页window焦点变化监听
        addHomeActivityWindowFocusChangeListener()
    }

    /**录视频模式下消息处理*/
    private fun handleMessageWhileTakingVideo() {
        WTMessageManager.takingVideoModeReceiveMsg.collectIn(activity, context = Dispatchers.Main) {
            if (it.isNull()) return@collectIn
            val mute = when (it) {
                is IMessage -> MuteInfoManager.isMessageNotificationMuted(it)
                is RealTimeMessage -> it.isMessageNotificationMuted()
                else -> true
            }
            if (!mute) {
                enqueueMessage(it!!)
            }
        }
    }

    /**当移除顶级页面的pop msg时，其它页面也要同步移除*/
    private fun handleTopMessageRemoveSync() {
        removeByUserMessageStateFlow.collectIn(
            activity,
            context = Dispatchers.Main
        ) { removeMsg ->
            val (showingBinding, showingMessage) = usingMessageMap.entries
                .firstOrNull { it.value == removeMsg }
                ?.let { it.key to it.value }
                ?: return@collectIn
            if (activity != topActivity && showingBinding.isVoiceAutoPlayBindingShow.not()) {
                if (removeByUserMessageList.contains(showingMessage)) {
                    logInfo(
                        TAG,
                        "removeByUserMessageListStateFlow,activity=${activity::class.java.simpleName}"
                    )
                    usingMessageMap.remove(showingBinding)
                    dismissPopWindow(showingBinding)
                    if (usingMessageViewQueue.size == 1) {
                        notifyShowNextMessage(NotifyShowNextMsgSource.ANIMATION_END)
                    }
                }
            }
        }
    }

    /**消息被mute的处理*/
    private fun handleMuteMessage() {
        WTMessageManager.muteMessageReceiveMsg.collectIn(activity, context = Dispatchers.Main) {
            logInfo(TAG, "muteMessageReceiveMsg")
            enqueueMessageIfNeed(it)
        }
    }

    /**quite模式下消息处理*/
    private fun handleQuiteModeMessage() {
        WTMessageManager.quietModeReceiveMsg.collectIn(activity, context = Dispatchers.Main) {
            logInfo(TAG, "quietModeReceiveMsg")
            enqueueMessageIfNeed(it)
        }
    }

    /**非quite模式下无音频的VoiceGif消息处理*/
    private fun handleNoVoiceMessage() {
        WTMessageManager.receiveNoVoiceMsg.collectIn(activity, context = Dispatchers.Main) {
            logInfo(TAG, "receiveNoVoiceMsg")
            enqueueMessageIfNeed(it)
        }
    }

    private fun enqueueMessageIfNeed(it: Pair<BuzNotifyType,IMessage>) {
        val type = it.first
        if (type == BuzNotifyType.AsrEditMSG) {
            return
        }
        val msg = it.second
        if (msg.isNull()) return
        val conversationId = getMessageConversationId(msg)
        val isInChatList = isInThisConversationChatList(conversationId)
        val mute = when (msg) {
            is IMessage -> MuteInfoManager.isMessageNotificationMuted(msg)
            is RealTimeMessage -> msg.isMessageNotificationMuted()
            else -> true
        }
        if (!isInChatList && !mute && !hasShowUpdateDialog()) {
            val isTarget = conversationId?.let { id -> isTarget(id) } ?: false
            val isHomeActivityLoseFocus = isTargetOnTopAndLoseFocus(conversationId)
            // 静音模式下，非选中item可以直接展示，选中item仅在录音中或者播放音频时才展示，另外首页被其它弹窗覆盖也可以展示
            val canEnqueue = isTarget.not() || (isTarget && (isPlaying || RecordStatusHelper.isRecording))
                    || isHomeActivityLoseFocus || !LoginMainABTestManager.isShowNewHomePagePlanB
            if (canEnqueue && !isInPipMode()) {
                enqueueMessage(msg)
                logInfo(TAG, "enqueueMessageIfNeed $it")
            }
        }
    }

    /**首页预览语音播放监听*/
    private fun handleReplayVoiceMessage() {
        routerServices<HomeService>().value?.getHomePlayingPreviewFlow()
            ?.collectIn(activity) { playingMessage ->
                logInfo(TAG, "getHomePlayingPreviewFlow:${playingMessage} activity:${activity}")
                if (playingMessage != null) {
                    val conversationId = playingMessage.getConvTargetIdLong()
                    val isTarget = isTarget(conversationId)
                            || isInThisConversationChatList(conversationId)
                    val canEnqueue = !isTarget && activity == topActivity
                            && activity is ChatHomeActivity
                    if (canEnqueue) {
                        enqueueMessage(ReplayingMessage(playingMessage))
                    }
                } else {
                    removeVoiceMessage()
                }
            }
    }

    /**首页预览语音播放监听*/
    private fun handleVoicePreview() {
        if (activity is ChatHomeActivity && activity == topActivity) {
            routerServices<HomeService>().value?.getIsPlayingPreviewVoiceMsg()
                ?.collectIn(activity) {
                    if (!it) return@collectIn
                    removeVoiceMessage()
                }
        }
    }


    /**添加首页window焦点变化监听，间接触发[handleHomeActivityLoseFocus]*/
    private fun addHomeActivityWindowFocusChangeListener() {
        if (activity is ChatHomeActivity && activity == topActivity) {
            activity.doOnLifecycle(onCreate = {
                activity.window.decorView.viewTreeObserver.addOnWindowFocusChangeListener(onWindowFocusChangeListener)
            }, onDestroy = {
                val viewTree = activity.window.decorView.viewTreeObserver
                if (viewTree.isAlive){
                    viewTree.removeOnWindowFocusChangeListener(onWindowFocusChangeListener)
                }
            })
        }
    }

    /**当在重播或者收到语音且首页选中item的语音时，点击VE面板或者更多面板,或者其它弹窗展示时，需要展示弹窗*/
    private fun handleHomeActivityLoseFocus(loseFocus: Boolean) {
        if (activity is ChatHomeActivity && activity == topActivity) {
            val replayingMessage = homePreviewPlayingFlow.value
            if (replayingMessage != null) {
                val conversationId = replayingMessage.getConvTargetIdLong()
                val isTarget = WTStatusManager.wtTargetId == conversationId
                if (isTarget) {
                    logInfo(TAG,"handleHomeActivityLoseFocus==>replayingMessage loseFocus:$loseFocus")
                    if (loseFocus) {
                        if (replayingMessage.fromId == UserSessionManager.getSessionUid().toString()) {
                            // the msg was sent by myself, don't need to show
                            return
                        }
                        enqueueMessage(ReplayingMessage(replayingMessage))
                    } else {
                        removeVoiceMessage()
                    }
                }
            } else if (WTMessageManager.isPlaying) {
                val (imPushMessage, _) = WTMessageManager.messageFlow.value
                val conversationId =
                    imPushMessage?.getConversationId()?.toLongOrNull() ?: return
                val isTarget = WTStatusManager.wtTargetId == conversationId
                if (isTarget) {
                    logInfo(TAG,"handleHomeActivityLoseFocus==>speakingMessage loseFocus:$loseFocus")
                    if (loseFocus) {
                        if ((imPushMessage.msg as? IMessage)?.fromId == UserSessionManager.getSessionUid().toString()) {
                            // the msg was sent by myself, don't need to show
                            return
                        }
                        enqueueMessage(imPushMessage)
                    } else {
                        removeVoiceMessage()
                    }
                }
            }
        }
    }

    /**处理实时语音消息展示和隐藏，以及非speaking消息隐藏*/
    private fun handleSpeakingMessage() {
        shouldShowSpeakingViewFlow.collectIn(activity, context = Dispatchers.Main) { msg ->
            val currentHomePageEnum = ChatHomeActivity.currentSelectPageFlow.value
            val conversationId = msg?.let { getMessageConversationId(it) }
            val currentTargetId = WTStatusManager.wtTargetId
            val currentMsgId = msg?.let { getMessageId(it) }
            val isTheSameVoiceMsg = lastEnqueueMessage == currentMsgId
            val isCurrentAutoPlayVisible = isAutoPlayBlindVisible()
            logInfo(
                TAG, "shouldShowSpeakingViewFlow [collectIn] msg=${msg}" +
                        ",isCurrentAutoPlayVisible:${isCurrentAutoPlayVisible}" +
                        ",currentHomePageEnum:${currentHomePageEnum}" +
                        ",currentTargetId:${currentTargetId}" +
                        ",currentMsgId:${currentMsgId}" +
                        ",isTheSameVoiceMsg=${isTheSameVoiceMsg}" +
                        ",activity=${activity.simpleName}"
            )

            if (msg == null || !isTheSameVoiceMsg) {
                logInfo(TAG, "shouldShowSpeakingViewFlow [collectIn] 命中删除条件 result 1")
                // 当对准用户时，需要隐藏正在speaking的通知
                removeVoiceMessage()
                // 当对准用户时，需要隐藏非speaking的通知
                removeNoneSpeakingMessageWhenSelectTarget()
            }

            if (msg != null) {
                // 语音重播不需要区分方向
                val directionCondition = if (msg is ReplayingMessage) true else !isDirectionSend(msg)
                if (directionCondition && !isMessageNotificationMuted(msg)) {
                    if (lastHomePageEnum != currentHomePageEnum && currentHomePageEnum != HomePageEnum.PageHome) {
                        // 从首页切换到设置页或者通讯录页，无论首页是否定位目标，都需要再次展示
                        logInfo(TAG, "shouldShowSpeakingViewFlow [collectIn] 首页fragment切换，需要展示speaking result 3")
                    } else if (isTheSameVoiceMsg) {
                        // 如果是相同消息，在首页会话列表来回切换，当滑动到不是当前的target时需要再次展示，如果已经展示了则不处理
                        if (isCurrentAutoPlayVisible.not()
                            && currentHomePageEnum == HomePageEnum.PageHome
                            && topActivity is ChatHomeActivity
                            && currentTargetId != lastTargetId
                        ) {
                            logInfo(TAG, "shouldShowSpeakingViewFlow [collectIn] 首页target切换，需要展示speaking result 4")
                        } else {
                            logInfo(TAG, "shouldShowSpeakingViewFlow [collectIn] 当前speaking正在展示，不做处理 result 5")
                            return@collectIn
                        }
                    }
                    if (msg is ReplayingMessage) {
                        logInfo(TAG, "shouldShowSpeakingViewFlow [collectIn] enqueueMessage ReplayingMessage result 6")
                    } else {
                        logInfo(TAG, "shouldShowSpeakingViewFlow [collectIn] enqueueMessage IMPushMessage result 7")
                    }
                    lastEnqueueMessage = currentMsgId
                    lastTargetId = conversationId
                    enqueueMessage(msg)
                }
            }

            lastHomePageEnum = currentHomePageEnum
        }
    }

    /**
     * 静音模式下隐藏 从通讯录或者设置页返回时展示的非实时播放的语音消息,
     * 或者会话列表target切换到对准的时候需要隐藏非speaking消息
     * */
    private fun removeNoneSpeakingMessageWhenSelectTarget(): Boolean {
        val showingBlinding = usingMessageViewQueue.peek()
        if (null != showingBlinding) {
            val showingMessage = usingMessageMap[showingBlinding]
            if (null != showingMessage) {
                val playingConversationId = getMessageConversationId(showingMessage) ?: return true
                val isTarget = isTarget(playingConversationId)
                // 首页失去焦点的时候，不需要隐藏，比如VE和More弹窗需要用到
                val isHomeActivityLoseFocus = activity.hasWindowFocus().not()
                val shouldRemove = !isHomeActivityLoseFocus
                        && isTarget
                        && (isPlaying || RecordStatusHelper.isRecording).not()
                        && LoginMainABTestManager.isShowNewHomePagePlanB
                if (shouldRemove) {
                    logInfo(TAG, "shouldShowSpeakingViewFlow [collectIn] 命中删除条件 result 2")
                    animateGone(showingBlinding, 0L)
                    usingMessageMap.remove(showingMessage)
                }
            }
        }
        return false
    }

    /**语音非实时播放模式下asr处理*/
    private fun handleVoiceAsrTextMessage() {
        IMAgent.msgReceivedFlow.collect(activity) { (type, msgList) ->
            if (type == BuzNotifyType.AsrEditMSG) {
                logInfo(TAG, "handleVoiceAsrTextMessage msgReceivedFlow AsrEditMSG list = ${msgList.toListString()}")
                val asrMsgMap = msgList.filter { it.isVoiceMsg }.associateBy { it.msgId }
                usingMessageViewQueue.forEach { binding ->
                    if (binding.isVoiceBindingShow) {
                        val msgId = binding.asrMsgId
                        if (null != msgId) {
                            val msg = asrMsgMap[msgId]
                            logInfo(TAG, "handleVoiceAsrTextMessage msgReceivedFlow AsrEditMSG asrMsgId=${msgId}")
                            updateVoiceAsrText(msg, binding)
                        }
                    }
                }
            }
        }
    }

    /**处理非实时播放语音消息*/
    private fun handleNoneSpeakingMessage() {
        IMAgent.msgReceivedFlow.collect(activity) { (type, msgList) ->
            logInfo(TAG, "addMessageNotifyObserver [handleNoneSpeakingMessage] type=${type}, msgList=${msgList.toListString()}")
            if (type == BuzNotifyType.RecallMsg) {
                removeMessageInQueueFromRecall(msgList)
                return@collect
            }

            if ((type != BuzNotifyType.NewMsg && type != BuzNotifyType.ReactionMsg) || msgList.isEmpty()) {
                return@collect
            }
            logInfo(TAG, "addMessageNotifyObserver [handleNoneSpeakingMessage] filter msg start")
            activity.lifecycleScope.launch {
                // 过滤非单聊解密失败的情况
                msgList.filter {
                    if (type == BuzNotifyType.ReactionMsg) {
                        it.isSend
                    } else if (it.isGroupOfflineMessage()) {
                        false
                    } else {
                        val isNotifyShowCommandMsg = it.isNotifyShowCommandMsg()
                        //voiceCall 命令消息特殊的过滤规则
                        val voiceCallCommandMsgFilter = it.isReceive && !it.pushPayLoad.isNullOrEmpty()

                        val shouldShowCommandMsg = isNotifyShowCommandMsg && voiceCallCommandMsgFilter

                        if (it.isCommandMessage) {
                            logInfo(TAG, "addMessageNotifyObserver [handleNoneSpeakingMessage] " +
                                    "shouldShowCommandMsg:${shouldShowCommandMsg},msgType:${it.msgType}")
                        }

                        (it.isDecryptFail
                                || it.isTextMessage
                                || it.isFileMessage
                                || it.isShareContactMessage
                                || it.isLocationMsg
                                || it.isImageMessage
                                || it.isVideoMessage
                                || it.isVoiceMojiMessage
                                || it.isVoiceGifMessage
                                || it.isVoiceMsg
                                || it.isMediaTextMsg
                                || (it.isRealTimeCallMsg && it.pushPayLoad.isNullOrEmpty().not()) // 在群通话被邀请中的人，pushPayLoad是空的，不展示消息通知
                                || it.isLivePlaceShareMsg
                                || (it.isCommandMessage && shouldShowCommandMsg))
                                && !(it.isDecryptFail && !it.isPrivate) && it.isReceive
                    }
                }.forEach {
                    val isMuteMessage = MuteInfoManager.isMessageNotificationMuted(it)
                    val conversationId = getMessageConversationId(it)
                    val isInChatList = isInThisConversationChatList(conversationId)
                    val isTarget = conversationId?.let { id -> isTarget(id) } ?: false
                    val isThisActivityInPipMode = isInPipMode()
                    if (it.isVoiceMojiMessage || it.isVoiceMsg || it.isVoiceGifMessage) {
                        if (isAppInForeground && CommonMMKV.vibration) {
                            VibratorUtil.vibrateOnReceiveMsg(appContext)
                        }
                        if (type != BuzNotifyType.ReactionMsg) {
                            logInfo(TAG, "addMessageNotifyObserver [handleNoneSpeakingMessage] return cos msg is voice message")
                            return@launch
                        }
                    }
                    val isHomeActivityLoseFocus = isTargetOnTopAndLoseFocus(conversationId)

                    val hasShowUpdateDialog = hasShowUpdateDialog()
                    logInfo(
                        TAG, "addMessageNotifyObserver [handleNoneSpeakingMessage] forEach isMuteMessage=${isMuteMessage}," +
                                " isInChatList=${isInChatList}, " +
                                "isTarget=${isTarget}, isPlaying=${isPlaying}, " +
                                "isRecording=${RecordStatusHelper.isRecording}," +
                                "isHomeActivityLoseFocus=${isHomeActivityLoseFocus}," +
                                "isAppInForeground=${isAppInForeground}, " +
                                "hasShowUpdateDialog=$hasShowUpdateDialog, "+
                                "isThisActivityInPipMode=$isThisActivityInPipMode"
                    )

                    // 非实时播放语音消息：当对准用户时，只有声音播放中和录音中才需要展示普通通知或者emoji qr;非对准时都展示
                    val canEnqueue = !isTarget || isPlaying || RecordStatusHelper.isRecording
                            || type == BuzNotifyType.ReactionMsg || isHomeActivityLoseFocus
                            || !LoginMainABTestManager.isShowNewHomePagePlanB // 旧首页，需要弹
                    if (canEnqueue && !isThisActivityInPipMode && !isMuteMessage && !isInChatList && !hasShowUpdateDialog) {
                        if (type == BuzNotifyType.ReactionMsg) {
                            handleQRMsg(it)
                            logInfo(TAG, "addMessageNotifyObserver [handleNoneSpeakingMessage] handleQRMsg")
                        } else {
                            enqueueMessage(it)
                            logInfo(TAG, "addMessageNotifyObserver [handleNoneSpeakingMessage] enqueueMessage")
                        }
                    } else if (isTarget && !(isPlaying || RecordStatusHelper.isRecording)) {
                        // 对准时，需要震动和提示音
                        vibratorAndPlayRingtone()
                        logInfo(TAG, "addMessageNotifyObserver [handleNoneSpeakingMessage] vibratorAndPlayRingtone")
                    }
                    if (isInChatList && isAppInForeground && CommonMMKV.vibration) {
                        if (type == BuzNotifyType.ReactionMsg) {
                            val op = it.reactionOperation
                            if ((it.fromId.toSafeLong() == UserSessionManager.uid
                                        || op.operator.toSafeLong() == UserSessionManager.uid)
                                && op.reactionType == IMReactionType.QUICK_REACT.value
                                && (op.type == BuzReactionOperateType.ADD.value
                                        || op.type == BuzReactionOperateType.REPLACE.value)
                            ) {
                                VibratorUtil.vibrateOnReceiveMsg(appContext)
                                logInfo(TAG, "addMessageNotifyObserver [handleNoneSpeakingMessage] isInChatList vibrateOnReceiveMsg ReactionMsg")
                            }
                        } else {
                            VibratorUtil.vibrateOnReceiveMsg(appContext)
                            logInfo(TAG, "addMessageNotifyObserver [handleNoneSpeakingMessage] isInChatList vibrateOnReceiveMsg for other msg")
                        }
                    }
                }
            }
        }
    }

    /**首页是否选中目标，且处于焦点（没有弹窗在上面）*/
    private fun isTarget(conversationId: Long): Boolean {
        return WTStatusManager.wtTargetId == conversationId
                && (topActivity is ChatHomeActivity || activity is ChatHomeActivity)
                && ChatHomeActivity.isShowPageHome()
                && activity.hasWindowFocus()
    }

    /**当前Activity是否处于画中画模式*/
    private fun isInPipMode(): Boolean {
        return activity.isInPipMode
    }

    /**首页是否处于top并且target选中，且失去焦点（有其它弹窗在上面）*/
    private fun isTargetOnTopAndLoseFocus(conversationId: Long?): Boolean {
        return activity == topActivity
                && WTStatusManager.wtTargetId == conversationId
                && topActivity is ChatHomeActivity
                && ChatHomeActivity.isShowPageHome()
                && activity.hasWindowFocus().not()
    }

    private fun updateVoiceAsrText(
        msg: IMessage?,
        binding: ChatPopupMsgBinding
    ) {
        msg?.apply {
            if (isPrivate) {
                binding.clVoiceRoot?.let { updatePrivateChatAsrText(this, it) }
            } else {
                activity.lifecycleScope.launch {
                    val userName = getUserInfo(msg)?.getContactFirstName()
                    if (!isActive) {
                        return@launch
                    }
                    if (userName != null) {
                        binding.clVoiceRoot?.let { updateGroupChatAsrText(msg, userName, it) }
                    }
                }
            }
        }
    }

    private fun getMessageId(msg: Any?): Long? {
        return when (msg) {
            is IMPushMessage -> {
                msg.message.msgId
            }

            is ReplayingMessage -> {
                msg.message.msgId
            }

            is QuickReactNotifyMessage -> {
                msg.message.msgId
            }

            is IMessage -> {
                msg.msgId
            }

            else -> null
        }
    }

    private suspend fun handleQRMsg(message: IMessage) {
        val fromId = message.fromId.toLongOrNull() ?: return
        if (fromId != UserSessionManager.uid) return
        val operation = message.reactionOperation
        val operator = operation.operator
        if (operator == UserSessionManager.uid.toString()) return
        val voicemojiId = operation.reactionId.toLongOrNull()
        if (voicemojiId != null &&
            operation.reactionType == IMReactionType.QUICK_REACT.value &&
            (message.reactionOperation.type == BuzReactionOperateType.ADD.value
                || message.reactionOperation.type == BuzReactionOperateType.REPLACE.value)
        ) {
            VoiceEmojiRepository.getVoiceEmojiById(voicemojiId).firstOrNull()?.let {
                enqueueMessage(QuickReactNotifyMessage(message, operator, it.emojiIcon))
            }
        }
    }

    private fun enqueueMessage(message: Any) {
        logInfo(TAG,"enqueueMessage, message=${message},,activity=${activity}")
        doInMainThread {
            if (message is QuickReactNotifyMessage) {
                commonMsgQueue.offer(message)
                notifyShowNextMessage(NotifyShowNextMsgSource.ENQUEUE_MESSAGE)
                return@doInMainThread
            }
            val msgType = getMessageType(message)
            if (msgType.isNull()) return@doInMainThread
            when(msgType){
                MsgType.IMAGE,
                MsgType.File,
                MsgType.TEXT,
                MsgType.ASYNC,
                MsgType.VIDEO,
                MsgType.LOCATION,
                MsgType.LP_SHARE,
                MsgType.RealTimeCall,
                MsgType.ShareContact,
                MsgType.VOICECALL_COMMAND -> {
                    commonMsgQueue.offer(message)
                }
                MsgType.VOICEGIF ->{
                    if (message is IMessage) {
                        commonMsgQueue.offer(message)
                    }else if (message is RealTimeMessage) {
                        ((message.msg as? IMessage)?.content as? BuzVoiceGifMsg)?.let {
                            logDebug("TESTTTTTTT","remoteUrl = ${it.remoteUrl}")
                            if (it.remoteUrl == null) {
                                commonMsgQueue.offer(message)
                            } else {
                                voiceMsgQueue.offer(message)
                            }
                        } ?: let {
                            voiceMsgQueue.offer(message)
                        }
                    }
                }
                MsgType.VOICEMOJI->{
                    if (message is IMessage) {
                        commonMsgQueue.offer(message)
                    }else if (message is RealTimeMessage) {
                        voiceMsgQueue.offer(message)
                    }
                }
                MsgType.VOICE, MsgType.REPLAY -> {
                    voiceMsgQueue.offer(message)
                }

                else -> return@doInMainThread
            }
            notifyShowNextMessage(NotifyShowNextMsgSource.ENQUEUE_MESSAGE)
        }
    }



    private fun removeVoiceMessage(){
        usingMessageViewQueue.filter { it.isVoiceAutoPlayBindingShow }.forEach {
            animateGone(it,0L)
            logInfo(TAG,"shouldShowSpeakingViewFlow removeVoiceMessage")
        }
    }

    private fun isAutoPlayBlindVisible(): Boolean {
        return usingMessageViewQueue.any { it.isVoiceAutoPlayBindingShow }
    }

    private fun getFirstMessage():Any?{
        val lowPriorityMsg = commonMsgQueue.peek()
        val highPriorityMsg = voiceMsgQueue.peek()
        if (lowPriorityMsg.isNull() && highPriorityMsg.isNull()) return null

        if (lowPriorityMsg.isNull()){
            return highPriorityMsg
        }

        if (highPriorityMsg.isNull()){
            return lowPriorityMsg
        }

        var highPriorityMsgUpdateTime = getMsgUpdateTime(highPriorityMsg)

        //0代表没法处理的类型，直接抛弃该消息
        if (highPriorityMsgUpdateTime == 0L) {
            highPriorityMsgUpdateTime = Long.MAX_VALUE
            voiceMsgQueue.poll()
        }

        val lowPriorityMsgUpdateTime = getMsgUpdateTime(lowPriorityMsg)

        return if (lowPriorityMsgUpdateTime < highPriorityMsgUpdateTime) {
            lowPriorityMsg
        } else {
            highPriorityMsg
        }
    }

    private fun getMsgUpdateTime(msg: Any):Long{
        return when(msg){
            is RealTimeMessage -> msg.updateTime
            is IMessage -> msg.updateTime
            else -> 0L
        }
    }

    private fun getHomeAddressUserInfo(groupId: Long): UserRelationInfo? {
        return WTStatusManager.getHomeAddressUserInfo(groupId)
    }

    private fun getMessageBinding(msg:Any): ChatPopupMsgBinding? {
        val queue = when{
            voiceMsgQueue.contains(msg) -> voiceMsgQueue
            commonMsgQueue.contains(msg) -> commonMsgQueue
            else -> null
        }
        if (queue.isNull()) {
            logInfo(TAG, "getMessageBinding queue.isNull,,activity=${activity}")
            return null
        }

        val binding = ChatPopupMsgBinding.inflate(activity.layoutInflater,contentView,false)
        if (queue == commonMsgQueue){
            var firstMessage = queue.peek()
            val pupMsgList = ArrayList<Any>()
            while (firstMessage.isNotNull()){
                pupMsgList.add(firstMessage)
                queue.poll()
                firstMessage = queue.peek()
            }
            if(pupMsgList.size > 1){
                initMergeMessageView(pupMsgList.size,binding)
            }else{
                initCommonMessageView(msg,binding)
                if (pupMsgList.size == 0){
                    queue.poll()
                }
            }
        }else if (queue == voiceMsgQueue){
            initCommonMessageView(msg,binding)
            queue.poll()
        }
        // 第一个pop/第二个pop消失动画结束后触发删除
        binding.root.animate().setListener(object : AnimatorListener(){
            override fun onAnimationStart(animation: Animator) {
                lastAnimTime = System.currentTimeMillis()
                logInfo(TAG,"onAnimationStart")
            }

            override fun onAnimationEnd(animation: Animator) {
                logInfo(TAG,"onAnimationEnd")
                if (binding.root.alpha == 0f){
                     // 隐藏弹窗
                    dismissPopWindow(binding)
                }
                notifyShowNextMessage(NotifyShowNextMsgSource.ANIMATION_END)
            }
        })

        val businessType = getPopMessageTypeForTrack(binding,msg)
        binding.root.click {
            logInfo(TAG,"getMessageBinding: isShowingPreview = ${isShowingVoicePreview()}")
            if (isShowingVoicePreview()){
                toast(R.string.pls_exit_previewing)
                return@click
            }
            popMessageClickEvent(binding, msg)
            animateGone(binding,0L,10L)
            addShowingMessageToRemoveList(binding)
            CloseChatListEvent.post(getMessageConversationId(msg))
            val pageBusinessType = when{
                isGroupMessage(msg) -> "group"
                isPrivateMessage(msg) ->"private"
                else -> ""
            }
            ChatTracker.onChatPopupMessageClick(
                pageBusinessType,
                getMessageConversationId(msg)?.toString() ?: "",
                businessType
            )
        }

        ChatTracker.onPopupMessageExposed(businessType)

        return binding
    }

    private fun dismissPopWindow(binding: ChatPopupMsgBinding) {
        usingMessageViewQueue.remove(binding)
        wtPopWindowManager.removePopup(binding.root)
        if (binding.isVoiceAutoPlayBindingShow) {
            binding.clVoiceAutoPlay?.lottieVoicePlaying?.cancelAnimation()
            binding.clVoiceAutoPlay?.lottieVoicePlaying?.gone()
        }
        logInfo(TAG, "dismissPopWindow")
    }

    private fun showPopWindow(binding: ChatPopupMsgBinding) {
        wtPopWindowManager.showPopup(binding.root)
        logInfo(TAG,"showPopWindow")
    }

    private fun popMessageClickEvent(binding: ChatPopupMsgBinding, msg: Any) {
        val chatService = routerServices<ChatService>().value
        val isTakingVideo = chatService?.isTakingVideo() ?: false
        val isAlbumPreviewing = chatService?.isAlbumPreviewing() ?: false

        if (isTakingVideo) {
            chatService?.stopRecording()
            NotificationInterceptEvent.post(
                positiveCallback = {
                    directActivityInRightState(binding,msg)
                }
            )
            return
        }

        if (isAlbumPreviewing) {
            showConfirmationDialogToChatHistory(
                activity = activity,
                title = com.interfun.buz.common.R.string.leave_media_dialog_title.asString(),
                positiveCallback = {
                    CloseBottomPanelEvent.post()
                    val inChatActivity = activityList.find { it is PrivateChatActivity || it is GroupChatActivity }
                    val conversationId = getMessageConversationId(msg)
                    val isInChatList = conversationId.isNotNull()
                            && conversationId == ChatGlobalInfoRecorder.nowChatListTargetId.value
                            && inChatActivity.isNotNull()
                    if (!isInChatList) { directActivity(binding, msg) }
                    activity.finish()
                }
            )
            return
        }

        if (shouldFinishCurrentActivity()){
            directActivityInRightState(binding, msg)
            return
        }

        if (isInThisConversationChatReadMedia(getMessageConversationId(msg))){
            activity.onBackPressed()
            ChatListScrollToEndEvent.post(false)

            return
        }

        directActivity(binding, msg)
    }

    private fun isShowingVoicePreview():Boolean{
        return routerServices<ChatService>().value?.chatPreviewVF()?.value==true
    }

    private fun directActivityInRightState(binding: ChatPopupMsgBinding,msg: Any){
        val inChatActivity = activityList.find { it is PrivateChatActivity || it is GroupChatActivity }
        val conversationId = getMessageConversationId(msg)
        val isInChatList = conversationId.isNotNull()
            && conversationId == ChatGlobalInfoRecorder.nowChatListTargetId.value
            && inChatActivity.isNotNull()
        if (!isInChatList) { directActivity(binding, msg) }
        activity.finish()
    }

    private fun directActivity(binding: ChatPopupMsgBinding, msg: Any) {
        val forceInHomePage = activity !is ChatHomeActivity
        logInfo(TAG,"directActivity==>isVoiceAutoPlayBindingShow:${binding.isVoiceAutoPlayBindingShow}," +
                "msg is IMPushMessage:${msg is IMPushMessage}," +
                "WTQuietModeManager.isQuietModeEnable:${WTQuietModeManager.isQuietModeEnable}," +
                "forceInHomePage=${forceInHomePage}")

        if (binding.isVoiceAutoPlayBindingShow|| (binding.isVoiceAutoPlayBindingShow && msg is IMPushMessage && !WTQuietModeManager.isQuietModeEnable)){
            WTListScrollEvent.post(getMessageConversationId(msg), forceInHomePage)
            logInfo(TAG,"directActivity result 1")
        }else{
            if (msg is IMessage && (msg.isVoiceMsg || msg.isVoiceMojiMessage)) {
                // 这里判断当前是否在视频通话页，如果是的话，不能直接调用下面 Event 里的startChatHomeActivity
                // 会出现任务栈和PIP模式交互导致异常的问题：https://project.larksuite.com/yewutest/issue/detail/6477197
                if (activity !is BaseRealTimeCallActivity) {
                    WTListScrollEvent.post(getMessageConversationId(msg), forceInHomePage)
                } else {
                    WTListScrollEvent.post(getMessageConversationId(msg))
                }
            } else {
                // 每次点击popup 都要通知首页更新当前选中
                WTListScrollEvent.post(getMessageConversationId(msg))
            }
            val reactionOpUserId = if (msg is QuickReactNotifyMessage) msg.reactionOpUserId else null
            val serMsgId = if (msg is QuickReactNotifyMessage) msg.message.serMsgId else null
            val jump2ChatActivity = {
                if (isGroupMessage(msg)){
                    val groupId = getMessageConversationId(msg).getLongDefault()
                    val jumpInfo = GroupChatJumpInfo(groupId,
                        IM,
                        addressUserInfo = getHomeAddressUserInfo(groupId),
                        serMsgId = serMsgId,
                        reactionOpUserId = reactionOpUserId)
                    NavManager.startGroupChatActivity(activity,jumpInfo)
                    logInfo(TAG,"directActivity result 2")
                }else if (isPrivateMessage(msg)){
                    val jumpInfo = PrivateChatJumpInfo(getMessageConversationId(msg),
                        IM,
                        serMsgId = serMsgId,
                        reactionOpUserId = reactionOpUserId)
                    NavManager.startPrivateChatActivity(activity,jumpInfo)
                    logInfo(TAG,"directActivity result 3")
                }
            }
            if (forceInHomePage) {
                // fix：https://project.larksuite.com/yewutest/issue/detail/4898469?parentUrl=%2Fyewutest%2Fissue%2Fhomepage&tabKey=detail#detail
                // 如果需要先跳去首页，这里先延迟一下再跳去聊天页，避免顺序问题，导致聊天页先打开，首页后打开，导致首页覆盖了聊天页
                delayInScope(activity.lifecycleScope, 50) {
                    jump2ChatActivity.invoke()
                }
            } else {
                jump2ChatActivity.invoke()
            }
        }
        hideHomeVEPanelIfNeed()
        hideHomeMoreFunctionPanelIfNeed()
        hideHomeUserOrGroupProfileDialogIfNeed()
    }

    private fun hideHomeVEPanelIfNeed() {
        if (WTVoiceEmojiManager.isHomeVEPanelShowing) {
            WTVoiceEmojiManager.isHomeVEPanelShowing = false
            WTVoiceMojiHidePanelEvent.post()
        }
    }

    private fun hideHomeMoreFunctionPanelIfNeed() {
        WTMoreFunctionHidePanelEvent.post()
    }

    private fun hideHomeUserOrGroupProfileDialogIfNeed() {
        GroupInfoDialogHideEvent.post()
        UserInfoDialogHideEvent.post()
    }

    private fun showConfirmationDialogToChatHistory(
        activity: Activity,
        title: String,
        tips: String? = null,
        positiveCallback: () -> Unit,
        negativeCallback: (() -> Unit)? = null
    ) {
        CommonAlertDialog(
            context = activity,
            title = title,
            tips = tips,
            positiveText = com.interfun.buz.common.R.string.leave_media.asString(),
            negativeText = com.interfun.buz.common.R.string.stay_media.asString(),
            positiveCallback = {
                it.dismiss()
                activity.finish()
                positiveCallback.invoke()
            },
            negativeCallback = {
                it.dismiss()
                negativeCallback?.invoke()
            }
        ).show()
    }

    private fun notifyShowNextMessage(from:NotifyShowNextMsgSource) {
        val firstMessage = getFirstMessage()
        logInfo(TAG,"notifyShowNextMessage firstMessage=${firstMessage},,activity=${activity}")
        if (usingMessageViewQueue.size == MAX_SHOWING_VIEW_SIZE
            && firstMessage.isNotNull()
            && commonMsgQueue.contains(firstMessage)){
            val commonMsgBinding = usingMessageViewQueue.find { !it.isVoiceAutoPlayBindingShow }
            if (commonMsgBinding.isNotNull()){
                animateGone(commonMsgBinding,0L)
            }
            return
        }

        log(TAG,"notifyShowNextMessage: before judge")

        if (((from == NotifyShowNextMsgSource.ENQUEUE_MESSAGE && lastAnimTime + animationDuration < System.currentTimeMillis())
            || from == NotifyShowNextMsgSource.ANIMATION_END) && usingMessageViewQueue.size < MAX_SHOWING_VIEW_SIZE) {
            log(TAG, "notifyShowNextMessage: after judge")
            if (firstMessage.isNull()) {
                if (usingMessageViewQueue.size == 1 && usingMessageViewQueue[0].root.tag == SECOND_POSITION_TAG) {
                    // 第一个消失了，将第二个回到第一个的位置
                    slideToFirstPosition(usingMessageViewQueue[0], fromInit = false)
                }
                return
            }

            if (usingMessageViewQueue.isEmpty()){
                showMessage(firstMessage)
                return
            }

            val showingBinding = usingMessageViewQueue.peek()!!
            if (showingBinding.isVoiceAutoPlayBindingShow && voiceMsgQueue.contains(firstMessage)) return
            if (!showingBinding.isVoiceAutoPlayBindingShow && commonMsgQueue.contains(firstMessage)){
                animateGone(showingBinding,0L)
                return
            }
            showMessage(firstMessage)
        }
    }

    private fun initCommonMessageView(msg: Any, binding: ChatPopupMsgBinding) {
        val msgType = getMessageType(msg) ?: return
        when {
            // 表情回复
            msgType == MsgType.QUICK_REACT -> {
                val clQuickReactRoot = ChatPopupMsgQuickReactItemBinding.inflate(
                    activity.layoutInflater,binding.root,true
                )
                initQRMessageView(msg as QuickReactNotifyMessage, clQuickReactRoot)
            }

            // 解密失败
            msg is IMessage && msg.isDecryptFail -> {
                val clTextMsgRoot = ChatPopupMsgTextItemBinding.inflate(
                    activity.layoutInflater,binding.root,true
                )
                initDecryptFailMessageView(msg, clTextMsgRoot)
            }

            // emoji语音自动播放 + 普通语音自动播放 +语音重播
            (msgType== MsgType.VOICE
                    || ( (msgType == MsgType.VOICEMOJI || msgType == MsgType.VOICEGIF) && msg is IMPushMessage)
                    || msgType == MsgType.REPLAY) -> {
                binding.root.forbiddenDrag = false
                val message = if (msg is IMPushMessage) msg.message else if (msg is ReplayingMessage) msg.message else return
                binding.root.setOnHideListener {
                    removeVoiceMessageInQueueByConversationId(
                        message.getConversationId(),
                        "slide",
                        msgType
                    )
                }
                val clVoiceAutoPlay = ChatPopupMsgVoicemojiItemAutoPlayBinding.inflate(
                    activity.layoutInflater,binding.root,true
                )
                initVoiceMessageView(message, msgType, clVoiceAutoPlay)
            }

            // emoji语音 非自动播放,没有头像的pag动画
            msgType == MsgType.VOICEMOJI -> {
                getIMessage(msg)?.let { initVoiceMojiMessageView(it, binding) }
            }

            msgType == MsgType.VOICEGIF -> {
                getIMessage(msg)?.let { initVoiceGifMessageView(it,binding) }
            }

            // 普通文本、地址、文件消息、分享联系人消息
            msgType == MsgType.TEXT
                    || msgType == MsgType.LOCATION
                    || msgType == MsgType.RealTimeCall
                    || msgType == MsgType.ShareContact
                    || msgType == MsgType.File-> {
                val clTextMsgRoot = ChatPopupMsgTextItemBinding.inflate(
                    activity.layoutInflater,binding.root,true
                )
                if (isGroupMessage(msg)) {
                    initGroupTxtOrLocationMessageView(msg, clTextMsgRoot)
                }
                if (isPrivateMessage(msg)) {
                    initPrivateTxtOrLocationMessageView(msg, clTextMsgRoot)
                }
            }

            // 图片、视频
            msgType == MsgType.IMAGE || msgType == MsgType.VIDEO -> {
                val clImageMsgRoot = ChatPopupMsgImageItemBinding.inflate(
                    activity.layoutInflater, binding.root, true
                )
                val iMessage = getIMessage(msg) ?: return
                if (iMessage.isPrivate) {
                    initPrivateImageOrVideoMessageView(iMessage, clImageMsgRoot)
                }
                if (iMessage.isGroup) {
                    initGroupImageOrVideoMessageView(iMessage, clImageMsgRoot)
                }
            }

            // 居中命令消息
            msgType == MsgType.VOICECALL_COMMAND -> {
                val clTextMsgRoot = ChatPopupMsgTextItemBinding.inflate(
                    activity.layoutInflater, binding.root, true
                )
                getIMessage(msg)?.let { initVoiceCallCommandMsgView(it, clTextMsgRoot) }
            }
            // liveplace邀请
            msgType == MsgType.LP_SHARE -> {

//                chat_popup_msg_live_place_invite_item.xml
                val clTextMsgRoot = ChatPopupMsgLivePlaceInviteItemBinding.inflate(
                    activity.layoutInflater, binding.root, true
                )
                if (isGroupMessage(msg)) {
                    initLivePlaceGroupMessageView(msg, clTextMsgRoot)
                }
                if (isPrivateMessage(msg)) {
                    initLivePlacePrivateMessageView(msg, clTextMsgRoot)
                }
//                getIMessage(msg)?.let { initLivePlaceMessageView(it, clTextMsgRoot) }
            }

            // 普通语音 非自动播放，支持音转文
            msgType == MsgType.ASYNC -> {
                val clVoiceRoot = ChatPopupMsgVoiceItemBinding.inflate(
                    activity.layoutInflater,binding.root,true
                )
                initVoiceAsrMessageView(msg, clVoiceRoot)
            }
        }
    }

    private fun initLivePlacePrivateMessageView(
        msg: Any,
        binding: ChatPopupMsgLivePlaceInviteItemBinding
    ) {
        activity.lifecycleScope.launch {
            val iMessage = getIMessage(msg) ?: return@launch
            val contactUserInfo = getUserInfo(msg)?.let {
                if (!isActive) {
                    return@let
                }
                binding.ivTxtPortrait.setPortrait(it.portrait)
                binding.tvTxtName.text = it.getContactFirstName()
            }

            if (contactUserInfo == null && (msg is IMessage) && msg.userInfo != null){
                binding.ivTxtPortrait.setPortrait(msg.userInfo.portraitURL)
                binding.tvTxtName.text = msg.userInfo.nickName
            }
            logInfo(TAG,"initPrivateTxtOrLocationMessageView")
            val content = when {
                iMessage.isLivePlaceShareMsg -> {
                    val text = (iMessage.content as? BuzLivePlaceShareMessage)?.topic
                    activity.getString(R.string.live_place_invite_card_share, text)
                }

                else -> ""
            }
            binding.ivLocationLogo.visibleIf(isLocationMessage(msg))
            binding.tvTxtTexContent.text = content
        }
    }

    // 语音asr文本展示
    private fun initVoiceAsrMessageView(msg: Any, binding: ChatPopupMsgVoiceItemBinding) {
        if (isGroupMessage(msg)) {
            initGroupVoiceAsrMessageView(msg, binding)
        }
        if (isPrivateMessage(msg)) {
            initPrivateVoiceAsrMessageView(msg, binding)
        }
    }

    // 私聊语音 asr
    private fun initPrivateVoiceAsrMessageView(msg: Any, binding: ChatPopupMsgVoiceItemBinding) {
        activity.lifecycleScope.launch {
            val contactUserInfo = getUserInfo(msg)?.let {
                if (!isActive) {
                    return@let
                }
                binding.ivVoicePortrait.setPortrait(it.portrait)
                binding.tvVoiceTitle.text = it.getContactFirstName()
            }
            if (contactUserInfo == null && (msg is IMessage) && msg.userInfo != null) {
                binding.ivVoicePortrait.setPortrait(msg.userInfo.portraitURL)
                binding.tvVoiceTitle.text = msg.userInfo.nickName
            }
            logInfo(TAG, "initPrivateVoiceAsrMessageView")
            updatePrivateChatAsrText(msg, binding)
        }
    }

    private fun updatePrivateChatAsrText(
        msg: Any,
        binding: ChatPopupMsgVoiceItemBinding
    ) {
        val smartTrans = isSmartTransMsg(msg)
        val msgId = getMsgId(msg)
        val asrText = if (smartTrans && null != msgId) {
            val result = getAsrString(msg)
            if (result.isNullOrEmpty()) {
                // If there is no asr result, then save the msgId to the tag of the view.
                binding.root.setTag(R.id.wt_pop_voice_msg_id, msgId)
            }
            result
        } else null
        logInfo(TAG, "updatePrivateChatAsrText: smartTrans = $smartTrans,msgId=${msgId}")
        val duration = getVoiceDuration(msg)
        val filterName: String? = msg.getVoiceFilterName()
        binding.tvVoiceAsr.text = buildSpannedString {
            if (hasVoiceFilter(msg)) {
                append("[")
                append(filterName)
                append("]")
            } else {
                append(R.string.chat_pop_msg_voice_tag.asString())
            }
            if (null != asrText) {
                append(" $asrText")
            } else {
                if (null != duration) {
                    append(" $duration")
                }
            }
        }
    }

    // 群聊语音 asr
    private fun initGroupVoiceAsrMessageView(msg: Any, binding: ChatPopupMsgVoiceItemBinding) {
        activity.lifecycleScope.launch {
            val groupId = getMessageConversationId(msg)
            if (groupId.isNull()) return@launch
            val groupInfoBean = GroupInfoCacheManager.getGroupInfoBeanByIdSync(groupId!!)
            if (groupInfoBean.isNull()) return@launch

            if (!isActive) {
                return@launch
            }
            binding.ivVoicePortrait.setGroupInfoBean(groupInfoBean!!)
            binding.tvVoiceTitle.text = buildSpannedString {
                append(groupInfoBean.groupName)
                val addressedText = getAddressedString(msg)
                if (addressedText.isEmpty().not()) {
                    color(R.color.basic_primary.asColor()) {
                        append(addressedText)
                    }
                }
            }
            val userName = getUserInfo(msg)?.getContactFirstName()
            if (!isActive) {
                return@launch
            }
            logInfo(TAG, "initGroupVoiceAsrMessageView")
            if (userName != null) {
                updateGroupChatAsrText(msg, userName, binding)
            }
        }
    }

    private fun updateGroupChatAsrText(
        msg: Any,
        userName: String,
        binding: ChatPopupMsgVoiceItemBinding
    ) {
        val smartTrans = isSmartTransMsg(msg)
        val msgId = getMsgId(msg)
        val asrText = if (smartTrans && null != msgId) {
            val result = getAsrString(msg)
            if (result.isNullOrEmpty()) {
                // If there is no asr result, then save the msgId to the tag of the view.
                binding.root.setTag(R.id.wt_pop_voice_msg_id, msgId)
            }
            result
        } else null
        logInfo(TAG, "updateGroupChatAsrText: smartTrans = $smartTrans = $smartTrans,msgId=${msgId}")
        val duration = getVoiceDuration(msg)
        val filterName: String? = msg.getVoiceFilterName()
        binding.tvVoiceAsr.text = buildSpannedString {
            append("${userName}: ")
            if (hasVoiceFilter(msg)) {
                append("[")
                append(filterName)
                append("]")
            } else {
                append(R.string.chat_pop_msg_voice_tag.asString())
            }
            if (null != asrText) {
                append(" $asrText")
            } else {
                if (null != duration) {
                    append(" $duration")
                }
            }
        }
    }

    private fun initQRMessageView(msg: QuickReactNotifyMessage, binding: ChatPopupMsgQuickReactItemBinding){
        activity.lifecycleScope.launch {
            UserRelationCacheManager.getUserRelationInfoByUidSync(msg.reactionOpUserId.toSafeLong())?.let {
                if (!isActive) {
                    return@let
                }
                var groupInfoBean: GroupInfoBean? = null
                if (msg.message.isGroup) {
                    val groupId = msg.message.getConversationId().toLongOrNull()
                    if (groupId != null) {
                        groupInfoBean = GroupInfoCacheManager.getGroupInfoBeanByIdSync(groupId)
                    }
                }
                if (groupInfoBean != null) {
                    binding.ivTxtPortrait.setGroupInfoBean(groupInfoBean)
                } else {
                    binding.ivTxtPortrait.setPortrait(it.portrait)
                }
                binding.tvTxtName.text = it.getContactFirstName()
            }
            binding.tvTxtTexContent.text = appStringContext.getString(R.string.qr_notify_tip, msg.voicemoji)
        }
    }

    private fun initDecryptFailMessageView(
        msg: IMessage,
        binding: ChatPopupMsgTextItemBinding
    ) {
        activity.lifecycleScope.launch {
            val uid = msg.userInfo?.userId?.toLongOrNull() ?:return@launch
            val contactUserInfo = UserRelationCacheManager.getUserRelationInfoByUidSync(uid)?.let {
                if (!isActive) {
                    return@let
                }
                binding.ivTxtPortrait.setPortrait(it.portrait)
                binding.tvTxtName.text = it.getContactFirstName()
            }

            if (contactUserInfo == null  && msg.userInfo != null){
                binding.ivTxtPortrait.setPortrait(msg.userInfo.portraitURL)
                binding.tvTxtName.text = msg.userInfo.nickName
            }
            binding.tvTxtTexContent.text = buildSpannedString {
                iconFontAlign(color = R.color.text_white_secondary.asColor()) {
                    size(14.dp){
                        typeface(FontUtil.fontIcon!!) {
                            append(R.string.ic_time.asString())
                        }
                    }
                }
                appendSpace(2.dp)
                append( R.string.notify_decrypt_failed.asString())
            }
        }
    }

    // 私聊普通文本或者位置
    private fun initPrivateTxtOrLocationMessageView(
        msg: Any,
        binding: ChatPopupMsgTextItemBinding
    ) {
        activity.lifecycleScope.launch {
            val iMessage = getIMessage(msg) ?: return@launch
            val contactUserInfo = getUserInfo(msg)?.let {
                if (!isActive) {
                    return@let
                }
                binding.ivTxtPortrait.setPortrait(it.portrait)
                binding.tvTxtName.text = it.getContactFirstName()
            }

            if (contactUserInfo == null && (msg is IMessage) && msg.userInfo != null){
                binding.ivTxtPortrait.setPortrait(msg.userInfo.portraitURL)
                binding.tvTxtName.text = msg.userInfo.nickName
            }
            logInfo(TAG,"initPrivateTxtOrLocationMessageView")
            val content = when {
                iMessage.isTextMessage -> {
                    val text = (iMessage.content as? IM5TextMessage)?.text.getStringDefault()
                    val hasLink = HyperlinkUtil.hasLink(text)
                    if (hasLink) {
                        activity.getString(R.string.chat_pop_msg_link_tag, text)
                    } else text
                }

                iMessage.isMediaTextMsg -> {
                    (iMessage.content as? MediaTextMsg)?.title.getStringDefault()
                }

                iMessage.isLocationMsg -> R.string.location.asString()
                iMessage.isRealTimeCallMsg -> (iMessage.content as? RealTimeCallInviteMsg)?.title.getStringDefault()
                iMessage.isFileMessage -> {
                    val fileName = (iMessage.content as? BuzFileMessage)?.name
                    activity.getString(R.string.chat_pop_msg_file_tag, fileName)
                }
                iMessage.isShareContactMessage ->{
                    val msgContent = iMessage.content as? BuzShareContactMessage
                    val contactType = msgContent?.contactType?: ContactType.User.type
                    if (contactType == ContactType.User.type) {
                        String.format(
                            R.string.contact_card_user_contact.asString(),
                            msgContent?.name ?: ""
                        )
                    } else {
                        String.format(
                            R.string.contact_card_group_contact.asString(),
                            msgContent?.name ?: ""
                        )
                    }
                }
                else -> ""
            }
            binding.ivLocationLogo.visibleIf(iMessage.isLocationMsg || iMessage.isFileMessage)
            if (iMessage.isFileMessage) {
                binding.ivLocationLogo.setImageDrawable(R.drawable.file_preview_image_drawable.asDrawable())
                binding.ivLocationLogo.layoutWidth(40.dp)
                val fileName = (iMessage.content as? BuzFileMessage)?.extName
                binding.tvLogo.text = fileName?.uppercase()
                binding.tvLogo.visibleIf(fileName.isNullOrEmpty().not())
            } else if (iMessage.isLocationMsg) {
                binding.ivLocationLogo.layoutWidth(48.dp)
                binding.ivLocationLogo.setImageDrawable(R.drawable.ic_location_logo.asDrawable())
            }
            binding.tvTxtTexContent.text = content
        }
    }

    private fun getVideoDurationText(msgContent: IM5VideoMessage): String {
        var durationSecond = msgContent.duration / 1000
        if (durationSecond * 1000 < msgContent.duration) {
            durationSecond += 1
        }
        //at least 1 second
        val useDuration = durationSecond.coerceAtLeast(1L)
        val durationText = String.format("%02d:%02d", useDuration / 60, useDuration % 60)
        return durationText
    }

    // 群聊普通文本或者位置
    private fun initGroupTxtOrLocationMessageView(msg:Any, binding: ChatPopupMsgTextItemBinding){
        activity.lifecycleScope.launch {
            val iMessage = getIMessage(msg) ?: return@launch
            val groupId = getMessageConversationId(msg)
            if (groupId.isNull()) return@launch
            val groupInfoBean = GroupInfoCacheManager.getGroupInfoBeanByIdSync(groupId!!)
            if (groupInfoBean.isNull()) return@launch

            if (!isActive) {
                return@launch
            }
            binding.ivTxtPortrait.setGroupInfoBean(groupInfoBean!!)
            binding.tvTxtName.text = groupInfoBean.groupName

            val addressedText = getAddressedString(msg)
            binding.tvMentionedYou.goneIf(addressedText.isEmpty())
            if (addressedText.isEmpty().not()) {
                binding.tvMentionedYou.text = addressedText
            }

            val userId = iMessage.userInfo?.userId?.toLongOrNull()
            val contactUserInfo = UserRelationCacheManager.getUserRelationInfoByUidSync(userId ?: 0L) ?: return@launch
            if (!isActive) {
                return@launch
            }
            logInfo(TAG,"initGroupTxtOrLocationMessageView")
            val content = when {
                iMessage.isTextMessage -> {
                    val mentionDisplaySpanOption = MentionDisplaySpanOption(
                        displayAsYou = false,
                        mentionTextColor = R.color.color_text_white_secondary.asColor(),
                    )
                    iMessage.mentionedInfoText(mentionDisplaySpanOption)
                }
                iMessage.isMediaTextMsg -> (iMessage.content as? MediaTextMsg)?.title
                iMessage.isLocationMsg -> R.string.location.asString()
                iMessage.isRealTimeCallMsg -> (iMessage.content as? RealTimeCallInviteMsg)?.title
                iMessage.isFileMessage -> {
                    val fileName = (iMessage.content as? BuzFileMessage)?.name
                    activity.getString(R.string.chat_pop_msg_file_tag, fileName)
                }
                iMessage.isShareContactMessage ->{
                    val msgContent = iMessage.content as? BuzShareContactMessage
                    val contactType = msgContent?.contactType?: ContactType.User.type
                    if (contactType == ContactType.User.type) {
                        String.format(
                            R.string.contact_card_user_contact.asString(),
                            msgContent?.name ?: ""
                        )
                    } else {
                        String.format(
                            R.string.contact_card_group_contact.asString(),
                            msgContent?.name ?: ""
                        )
                    }
                }
                else -> ""
            }
            binding.ivLocationLogo.visibleIf(iMessage.isLocationMsg || iMessage.isFileMessage)
            if (iMessage.isFileMessage) {
                binding.ivLocationLogo.setImageDrawable(R.drawable.file_preview_image_drawable.asDrawable())
                binding.ivLocationLogo.layoutWidth(40.dp)
                val fileName = (iMessage.content as? BuzFileMessage)?.extName
                binding.tvLogo.text = fileName?.uppercase()
                binding.tvLogo.visibleIf(fileName.isNullOrEmpty().not())
            } else if (iMessage.isLocationMsg) {
                binding.ivLocationLogo.layoutWidth(48.dp)
                binding.ivLocationLogo.setImageDrawable(R.drawable.ic_location_logo.asDrawable())
            }
            binding.tvTxtTexContent.text = activity.getString(
                R.string.chat_colon_split_two_string,
                contactUserInfo.getContactFirstName(),
                content
            )
        }
    }

    private fun initLivePlaceGroupMessageView(msg:Any, binding: ChatPopupMsgLivePlaceInviteItemBinding){
        activity.lifecycleScope.launch {
            val iMessage = getIMessage(msg) ?: return@launch
            val groupId = getMessageConversationId(msg)
            if (groupId.isNull()) return@launch
            val groupInfoBean = GroupInfoCacheManager.getGroupInfoBeanByIdSync(groupId!!)
            if (groupInfoBean.isNull()) return@launch

            if (!isActive) {
                return@launch
            }
            binding.ivTxtPortrait.setGroupInfoBean(groupInfoBean!!)
            binding.tvTxtName.text = groupInfoBean.groupName

            val addressedText = getAddressedString(msg)
            binding.tvMentionedYou.goneIf(addressedText.isEmpty())
            if (addressedText.isEmpty().not()) {
                binding.tvMentionedYou.text = addressedText
            }

            val userId = iMessage.userInfo?.userId?.toLongOrNull()
            val contactUserInfo = UserRelationCacheManager.getUserRelationInfoByUidSync(userId ?: 0L) ?: return@launch
            if (!isActive) {
                return@launch
            }
            logInfo(TAG,"initGroupTxtOrLocationMessageView")
            val content = when {
                iMessage.isLivePlaceShareMsg -> {
                    activity.getString(R.string.live_place_invite_card_share, (iMessage.content as BuzLivePlaceShareMessage).topic)
                }
                else -> ""
            }
            binding.ivLocationLogo.visibleIf(isLocationMessage(msg))
            binding.tvTxtTexContent.text = activity.getString(
                R.string.chat_colon_split_two_string,
                contactUserInfo.getContactFirstName(),
                content
            )
        }
    }

    private fun initPrivateImageOrVideoMessageView(msg:IMessage, binding: ChatPopupMsgImageItemBinding) {
        activity.lifecycleScope.launch {
            val uid = msg.userInfo.userId.toLongOrNull() ?: return@launch
            val contactUserInfo = UserRelationCacheManager.getUserRelationInfoByUidSync(uid)?.let {
                if (!isActive) {
                    return@let
                }
                binding.ivImgPortrait.setPortrait(it.portrait)
                binding.tvImgName.text = it.getContactFirstName()
            }
            if (contactUserInfo == null && msg.userInfo != null) {
                binding.ivImgPortrait.setPortrait(msg.userInfo.portraitURL)
                binding.tvImgName.text = msg.userInfo.nickName
            }

            binding.flBottom.gone()
            val msgContent = msg.content
            if (msgContent is IM5ImageMessage) {
                logInfo(TAG,"initPrivatePicMessageView: msg is IM5ImageMessage")
                binding.tvPhotoTag.text = R.string.chat_pop_msg_photo_tag.asString()
                binding.ivImgImage.loadWithThumbnailNew(
                    thumbnailUrl = msgContent.thumbUrl,
                    finalUrl = msgContent.imageUrl(),
                    aesComponent = msg.aesComponent
                )
            } else if (msgContent is IM5VideoMessage) {
                binding.flBottom.visible()
                val durationText = getVideoDurationText(msgContent)
                binding.tvPhotoTag.text = "${R.string.chat_pop_msg_video_tag.asString()} $durationText"
                binding.ivImgImage.loadWithThumbnail(url = msgContent.coverThumbPath, aesComponent = msg.aesComponent)
            }
        }
    }


    // emoji语音 非自动播放
    private fun initVoiceMojiMessageView(msg:IMessage,binding:ChatPopupMsgBinding) {
        binding.root.forbiddenDrag = false
        binding.root.setOnHideListener {
            removeVoiceMessageInQueueByConversationId(
                msg.getConversationId(),
                "slide",
                MsgType.VOICEMOJI
            )
        }
        val clVoiceMojiMsgRoot = ChatPopupMsgVoicemojiItemBinding.inflate(
            activity.layoutInflater, binding.root, true
        )
        if (msg.isGroup) {
            initVoiceMojiGroupMessageView(msg, clVoiceMojiMsgRoot)
        } else {
            initVoiceMojiPrivateMessageView(msg, clVoiceMojiMsgRoot)
        }
    }

    // emoji语音，非自动播放
    private fun initVoiceMojiPrivateMessageView(msg: IMessage, binding: ChatPopupMsgVoicemojiItemBinding) {
        activity.lifecycleScope.launch {
            val uid = msg.userInfo.userId.toLongOrNull() ?: return@launch
            val contactUserInfo = UserRelationCacheManager.getUserRelationInfoByUidSync(uid)?.let {
                if (!isActive) {
                    return@let
                }
                binding.ivVoiceMojiPortrait.setPortrait(it.portrait)
                binding.tvVoiceMojiName.text = it.getContactFirstName()
            }
            if (contactUserInfo == null && msg.userInfo != null) {
                binding.ivVoiceMojiPortrait.setPortrait(msg.userInfo.portraitURL)
                binding.tvVoiceMojiName.text = msg.userInfo.nickName
            }
            binding.tvVoiceMojiTag.text = R.string.ve_voiceemoji_tip_updated.asString()

            val msgContent = msg.content
            if (msgContent is WTVoiceEmojiMsg) {
                logInfo(TAG, "initPrivatePicMessageView: msg is IM5ImageMessage")
                binding.voiceMojiText.setVoiceEmojiUrl(
                    msgContent.emojiIcon,
                    if (msgContent.isImageEmoji()) {
                        VoiceEmojiType.Image
                    } else {
                        VoiceEmojiType.Text
                    }
                )
            }
        }

    }

    // 群聊emoji语音 非自动播放
    @SuppressLint("SetTextI18n")
    private fun initVoiceMojiGroupMessageView(msg: IMessage, binding: ChatPopupMsgVoicemojiItemBinding) {
        activity.lifecycleScope.launch {
            val groupId = msg.getConversationId().toLongOrNull() ?: return@launch

            val contactUserInfo = GroupInfoCacheManager.getGroupInfoBeanByIdSync(groupId)?.let {
                if (!isActive) {
                    return@let
                }
                binding.ivVoiceMojiPortrait.setGroupInfoBean(it)
                binding.tvVoiceMojiName.text = it.groupName
            }
            if (contactUserInfo == null && msg.userInfo != null) {
                binding.ivVoiceMojiPortrait.setPortrait(msg.userInfo.portraitURL)
                binding.tvVoiceMojiName.text = msg.userInfo.nickName
            }
            binding.tvVoiceMojiTag.text = "${msg.userInfo.nickName}: ${R.string.ve_voiceemoji_tip_updated.asString()}"

            val msgContent = msg.content
            if (msgContent is WTVoiceEmojiMsg) {
                logInfo(TAG, "initVoiceMojiGroupMessageView: msg is IM5ImageMessage")
                binding.voiceMojiText.setVoiceEmojiUrl(
                    msgContent.emojiIcon,
                    if (msgContent.isImageEmoji()) {
                        VoiceEmojiType.Image
                    } else {
                        VoiceEmojiType.Text
                    }
                )
            }
        }

    }

    // gif语音 非自动播放
    private fun initVoiceGifMessageView(msg:IMessage,binding:ChatPopupMsgBinding) {
        binding.root.forbiddenDrag = false
        binding.root.setOnHideListener {
            removeVoiceMessageInQueueByConversationId(
                msg.getConversationId(),
                "slide",
                MsgType.VOICEMOJI
            )
        }
        val clVoiceGifMsgRoot = ChatPopupMsgVoicegifItemBinding.inflate(
            activity.layoutInflater, binding.root, true
        )
        if (msg.isGroup) {
            initVoiceGifGroupMessageView(msg, clVoiceGifMsgRoot)
        } else {
            initVoiceGifPrivateMessageView(msg, clVoiceGifMsgRoot)
        }
    }

    // 私聊gif语音，非自动播放
    private fun initVoiceGifPrivateMessageView(msg: IMessage, binding: ChatPopupMsgVoicegifItemBinding) {
        activity.lifecycleScope.launch {
            val uid = msg.userInfo.userId.toLongOrNull() ?: return@launch
            val contactUserInfo = UserRelationCacheManager.getUserRelationInfoByUidSync(uid)?.let {
                if (!isActive) {
                    return@let
                }
                binding.ivVoiceGifPortrait.setPortrait(it.portrait)
                binding.tvVoiceGifName.text = it.getContactFirstName()
            }
            if (contactUserInfo == null && msg.userInfo != null) {
                binding.ivVoiceGifPortrait.setPortrait(msg.userInfo.portraitURL)
                binding.tvVoiceGifName.text = msg.userInfo.nickName
            }
            binding.tvVoiceGifTag.text = R.string.voice_gif_tag.asString()

            val msgContent = msg.content
            if (msgContent is BuzVoiceGifMsg) {
                logInfo(TAG, "initPrivatePicMessageView: msg is IM5ImageMessage")
                val voiceGifUrl =
                    msgContent.smallThumbnailUrl.ifBlank { msgContent.thumbnailUrl }
                binding.voiceGif.load(voiceGifUrl) {
                    crossfade(true)
                    transformations(coil.transform.RoundedCornersTransformation(12.dp.toFloat()))
                }
            }
        }
    }

    // 群聊gif语音 非自动播放
    @SuppressLint("SetTextI18n")
    private fun initVoiceGifGroupMessageView(msg: IMessage, binding: ChatPopupMsgVoicegifItemBinding) {
        activity.lifecycleScope.launch {
            val groupId = msg.getConversationId().toLongOrNull() ?: return@launch

            val contactUserInfo = GroupInfoCacheManager.getGroupInfoBeanByIdSync(groupId)?.let {
                if (!isActive) {
                    return@let
                }
                binding.ivVoiceGifPortrait.setGroupInfoBean(it)
                binding.tvVoiceGifName.text = it.groupName
            }
            if (contactUserInfo == null && msg.userInfo != null) {
                binding.ivVoiceGifPortrait.setPortrait(msg.userInfo.portraitURL)
                binding.tvVoiceGifName.text = msg.userInfo.nickName
            }
            binding.tvVoiceGifTag.text = "${msg.userInfo.nickName}: ${R.string.voice_gif_tag.asString()}"

            val msgContent = msg.content
            if (msgContent is BuzVoiceGifMsg) {
                logInfo(TAG, "initVoiceMojiGroupMessageView: msg is IM5ImageMessage")
                val voiceGifUrl =
                    msgContent.smallThumbnailUrl.ifBlank { msgContent.thumbnailUrl }
                binding.voiceGif.load(voiceGifUrl) {
                    crossfade(true)
                    transformations(coil.transform.RoundedCornersTransformation(12.dp.toFloat()))
                }
            }
        }

    }

    @SuppressLint("SetTextI18n")
    private fun initGroupImageOrVideoMessageView(msg:IMessage, binding:ChatPopupMsgImageItemBinding){
        activity.lifecycleScope.launch {
            val groupInfoBean =
                GroupInfoCacheManager.getGroupInfoBeanByIdSync(msg.getConversationId().toLong())
                    ?: return@launch
            if (!isActive) {
                return@launch
            }
            binding.ivImgPortrait.setGroupInfoBean(groupInfoBean)
            binding.tvImgName.text = buildSpannedString {
                append(groupInfoBean.groupName)
                val addressedText = getAddressedString(msg)
                if (addressedText.isEmpty().not()){
                    color(R.color.basic_primary.asColor()){
                        append(addressedText)
                    }
                }
            }

            val contactUserInfo = UserRelationCacheManager.getUserRelationInfoByUidSync(
                msg.userInfo?.userId?.toLongOrNull() ?: 0L
            ) ?: return@launch
            if (!isActive) {
                return@launch
            }
            var content = R.string.chat_pop_msg_photo_tag.asString()
            binding.flBottom.gone()
            val msgContent = msg.content
            if (msgContent is IM5ImageMessage) {
                binding.ivImgImage.loadWithThumbnailNew(
                    thumbnailUrl = msgContent.thumbUrl,
                    finalUrl = msgContent.imageUrl(),
                    aesComponent = msg.aesComponent
                )
            } else if (msgContent is IM5VideoMessage) {
                binding.flBottom.visible()
                val durationText = getVideoDurationText(msgContent)
                content = "${R.string.chat_pop_msg_video_tag.asString()} $durationText"
                binding.ivImgImage.loadWithThumbnail(url = msgContent.coverThumbPath)
            }
            binding.tvPhotoTag.text = activity.getString(
                R.string.chat_colon_split_two_string,
                contactUserInfo.getContactFirstName(),
                content
            )
        }
    }

    /**
     * 有以下三类voicecall的命令消息需要做展示
     * 1. "xx start a voice call"，只有在群语音中未被呼叫邀请的群成员才展示
     * 2. "Missed a vocie call", 在被呼叫人超时未接听的1v1和群语音中都会展示
     * 3. "Call ended", 只有在群语音未加入通话的成员会展示
     */
    private fun initVoiceCallCommandMsgView(msg:IMessage, binding:ChatPopupMsgTextItemBinding){
        activity.lifecycleScope.launch{
            // 只有群聊语音未受邀请的用户才需要展示
            logInfo(TAG, "initVoiceCallCommandMsgView isPrivate = ${msg.isPrivate}, isSend = ${msg.isSend}, pushPayLoad = ${msg.pushPayLoad}")
            // 在 enqueueMessage 消息到队列之前就需要先过滤掉
            //if (msg.isSend || msg.pushPayLoad.isNullOrEmpty()) return@launch
            if (msg.isGroup){
                val groupInfoBean = GroupInfoCacheManager.getGroupInfoBeanByIdSync(msg.getConversationId().toLong())?: return@launch
                binding.ivTxtPortrait.setPortrait(groupInfoBean.serverPortraitUrl)
                binding.tvTxtName.text = groupInfoBean.groupName
            }else{
                val userRelationInfo =
                    UserRelationCacheManager.getUserRelationInfoByUidSync(
                        msg.getConversationId().toLong()
                    )
                userRelationInfo?.let {
                    binding.ivTxtPortrait.setPortrait(it.portrait)
                    binding.tvTxtName.text = it.getDisplayName()
                }
            }
            val commandMsgNotifyText = msg.getCommandMsgNotifyText()
            if (!isActive) {
                return@launch
            }
            binding.tvTxtTexContent.text = commandMsgNotifyText
        }
    }


    // emoji语音 + 普通语音 自动播放 + 语音重播
    private fun initVoiceMessageView(
        msg: IMessage,
        msgType: MsgType,
        binding: ChatPopupMsgVoicemojiItemAutoPlayBinding
    ) {
        binding.roundCloseVoice.click {
            removeVoiceMessageInQueueByConversationId(msg.getConversationId(), "button", msgType)
        }
        when (msg.conversationType) {
            IM5ConversationType.GROUP -> {
                val groupId = msg.targetId.toLongOrNull() ?: return
                val filterName: String? = msg.getVoiceFilterName()
                initGroupVoiceMessageView(
                    msgType,
                    msg,
                    binding,
                    groupId,
                    msg.userInfo?.userId?.toLongOrNull() ?: 0L,
                    msg.userInfo,
                    filterName
                )
            }
            IM5ConversationType.PRIVATE -> {
                val uid = msg.userInfo.userId.toLongOrNull() ?: return
                val filterName: String? = msg.getVoiceFilterName()
                initPrivateVoiceMessageView(msgType,binding, uid, msg.userInfo, filterName)
            }

            else -> {}
        }
        // speaking 动画
        binding.lottieVoicePlaying.visible()
        binding.lottieVoicePlaying.playAnimation()
        // 重播和speaking的颜色是不同的
        val isRePlaying = msgType == MsgType.REPLAY
        binding.tvVoiceMemberSpeaking.setTextColor(
            if (isRePlaying) R.color.text_white_secondary.asColor()
            else R.color.color_text_highlight_default.asColor()
        )

        val msgContent = msg.content
        if (msgContent is WTVoiceEmojiMsg) {
            logInfo(TAG, "initVoiceMojiMessageView: msg is WTVoiceEmojiMsg")
            binding.voiceMojiText.visible()
            binding.voiceGif.gone()
            binding.voiceMojiText.setVoiceEmojiUrl(
                msgContent.emojiIcon,
                if (msgContent.isImageEmoji()) {
                    VoiceEmojiType.Image
                } else {
                    VoiceEmojiType.Text
                }
            )
        }
        if (msgContent is BuzVoiceGifMsg) {
            logInfo(TAG, "initVoiceGifMessageView: msg is BuzVoiceGifMsg")
            activity.lifecycleScope.launchMain {
                binding.voiceGif.visible()
                binding.voiceMojiText.gone()
                val voiceGifUrl =
                    msgContent.smallThumbnailUrl.ifBlank { msgContent.thumbnailUrl }
                binding.voiceGif.load(voiceGifUrl) {
                    crossfade(true)
                    transformations(coil.transform.RoundedCornersTransformation(12.dp.toFloat()))
                }
            }
        }
    }

    // 私聊普通语音 emoji 语音自动播放
    private fun initPrivateVoiceMessageView(
        msgType: MsgType,
        binding: ChatPopupMsgVoicemojiItemAutoPlayBinding,
        uid: Long,
        userInfo: UserInfo? = null,
        filterName: String? = null
    ) {
        val isEmojiVoice = msgType == MsgType.VOICEMOJI
        val isGifVoice = msgType == MsgType.VOICEGIF
        val isRePlaying = msgType == MsgType.REPLAY
        activity.lifecycleScope.launch {
            val contactUserInfo = UserRelationCacheManager.getUserRelationInfoByUidSync(uid)?.let {
                if (!isActive) {
                    return@let
                }
                binding.ivVoiceMojiPortrait.setPortrait(it.portrait)
                binding.tvVoiceMojiName.text = it.getContactFirstName()
            }
            if (contactUserInfo == null && userInfo != null) {
                binding.ivVoiceMojiPortrait.setPortrait(userInfo.portraitURL)
                binding.tvVoiceMojiName.text = userInfo.nickName
            }
            val speakingText = if (isEmojiVoice) R.string.ve_voiceemoji_tip_updated.asString()
            else if (isGifVoice) R.string.voice_gif_tag.asString()
            else if (isRePlaying) {
                if (filterName.isNullOrEmpty()) {
                    R.string.home_pop_message_playing.asString()
                } else {
                    "[$filterName] ${R.string.home_pop_message_playing.asString()}"
                }
            }
            else {
                if (filterName.isNullOrEmpty()) {
                    R.string.chat_rtp_speaking.asString()
                } else {
                    "[$filterName] ${R.string.chat_rtp_speaking.asString()}"
                }
            }
            binding.tvVoiceMemberSpeaking.text = speakingText
        }
    }

    // 群聊普通语音 emoji 语音自动播放
    @SuppressLint("SetTextI18n")
    private fun initGroupVoiceMessageView(
        msgType: MsgType,
        msg: Any?,
        binding: ChatPopupMsgVoicemojiItemAutoPlayBinding,
        groupId: Long,
        uid: Long,
        userInfo: UserInfo? = null,
        filterName: String? = null
    ) {
        val isEmojiVoice = msgType == MsgType.VOICEMOJI
        val isGifVoice = msgType == MsgType.VOICEGIF
        val isRePlaying = msgType == MsgType.REPLAY
        activity.lifecycleScope.launch {
            GroupInfoCacheManager.getGroupInfoBeanByIdSync(groupId)?.let {
                if (!isActive) {
                    return@let
                }
                binding.ivVoiceMojiPortrait.setGroupInfoBean(it)
                binding.tvVoiceMojiName.text = buildSpannedString {  // 群名 @you
                    append(it.groupName)
                    val addressedText = getAddressedString(msg)
                    if (addressedText.isEmpty().not()){
                        color(R.color.basic_primary.asColor()){
                            append(" $addressedText")
                        }
                    }
                }
            }
            val speakerName =
                UserRelationCacheManager.getUserRelationInfoByUidSync(uid)?.getContactFirstName()
                    ?: userInfo?.nickName

            val speakingTextWithSpeaker =
                if (isEmojiVoice) "$speakerName: ${R.string.ve_voiceemoji_tip_updated.asString()}"
                else if (isGifVoice) "$speakerName: ${R.string.voice_gif_tag.asString()}"
                else if (isRePlaying) {
                    if (filterName.isNullOrEmpty()) {
                        String.format(
                            R.string.home_pop_xxx_message_playing.asString(),
                            speakerName
                        )
                    } else {
                        "[$filterName] ${String.format(
                            R.string.home_pop_xxx_message_playing.asString(),
                            speakerName
                        )}"
                    }
                }
                else {
                    if (filterName.isNullOrEmpty()) {
                        "$speakerName ${R.string.chat_rtp_speaking.asString()}"
                    } else {
                        "$speakerName: [$filterName] ${R.string.chat_rtp_speaking.asString()}"
                    }
                }

            val speakingTextNoneSpeaker = if (isEmojiVoice) R.string.ve_voiceemoji_tip_updated.asString()
            else if (isGifVoice)  R.string.voice_gif_tag.asString()
            else if (isRePlaying) {
                if (filterName.isNullOrEmpty()) {
                    R.string.home_pop_message_playing.asString()
                } else {
                    "[$filterName] ${R.string.home_pop_message_playing.asString()}"
                }
            }
            else {
                if (filterName.isNullOrEmpty()) {
                    R.string.chat_rtp_speaking.asString()
                } else {
                    "[$filterName] ${R.string.chat_rtp_speaking.asString()}"
                }
            }

            binding.tvVoiceMemberSpeaking.text =
                if (!speakerName.isNullOrEmpty()) speakingTextWithSpeaker else speakingTextNoneSpeaker
        }
    }

    /**
     * 消息合并提示
     */
    private fun initMergeMessageView(count: Int, binding: ChatPopupMsgBinding) {
        val clNotifyRoot = ChatPopupMsgNotifyItemBinding.inflate(
            activity.layoutInflater, binding.root, true
        )
        clNotifyRoot.tvMergeMsg.text = activity.getString(R.string.chat_merge_msg_tip, count)
        clNotifyRoot.tvMergeMsg.visible()
    }

    private fun isInThisConversationChatList(conversationId: Long?):Boolean{
        val isThisChatOnTop = isThisChatOnTop(conversationId)
        val isActivityResumed = resumedActivity is PrivateChatActivity || resumedActivity is GroupChatActivity
        val convIsSame = conversationId.isNotNull() && conversationId == ChatGlobalInfoRecorder.nowChatListTargetId.value
        logInfo(TAG, "isThisChatOnTop: $isThisChatOnTop, isActivityResumed: $isActivityResumed")
        if (!isThisChatOnTop) {
            return convIsSame && isActivityResumed
        }

        val currentFragment =
            (topActivity as? FragmentActivity)?.supportFragmentManager?.findFragmentById(
                ChatItemCallbackImpl.MEDIA_PREVIEW_CONTAINER
            )

        logInfo(TAG,"convIsSame: $convIsSame, isInChatList: $currentFragment")
        return convIsSame && (currentFragment is GroupChatFragment || currentFragment is PrivateChatFragment)
    }

    private fun isInThisConversationChatReadMedia(conversationId: Long?):Boolean{
        val isThisChatOnTop = isThisChatOnTop(conversationId)
        if (!isThisChatOnTop) return false
        val currentFragment = activity.supportFragmentManager.findFragmentById(ChatItemCallbackImpl.MEDIA_PREVIEW_CONTAINER)
        return currentFragment is ChatMediaPreviewListFragment
    }

    private fun isThisChatOnTop(conversationId: Long?): Boolean {
        logInfo(TAG, "isThisChatOnTop topActivity: $topActivity, activity: $activity")
        return conversationId.isNotNull()
            && conversationId == ChatGlobalInfoRecorder.nowChatListTargetId.value
            && (topActivity is PrivateChatActivity || topActivity is GroupChatActivity)
    }

    val globalOnAirController=routerServices<IGlobalOnAirController>()
    private fun shouldFinishCurrentActivity():Boolean{

        return activity is TakePhotoSendActivity || activity::class.java == globalOnAirController.value?.livePlaceClass()
    }

    private fun isAsyncVoiceMessage(msg:Any):Boolean{
        return getMessageType(msg) == MsgType.ASYNC
    }

    private fun isLocationMessage(msg:Any):Boolean{
        return getMessageType(msg) == MsgType.LOCATION
    }


    /**
     * popup消息，同时播放消息提示音和震动
     */
    private fun showMessage(firstMessage: Any?) {
        logInfo(TAG,"showMessage, firstMessage=${firstMessage},isPlaying=${isPlaying},activity=${activity}")
        val binding = getMessageBinding(firstMessage!!)
        if (binding.isNull()) return
        if (binding?.isVoiceAutoPlayBindingShow == false) {
            vibratorAndPlayRingtone()
        }
        // 展示消息弹窗
        showPopWindow(binding!!)
        usingMessageMap[binding] = firstMessage
        slideToCorrectPosition(binding)
    }

    /**震动和播放提示音*/
    private fun vibratorAndPlayRingtone() {
        val isTakingVideo = routerServices<ChatService>().value?.isTakingVideo() ?: false
        val isAlbumPreviewing = routerServices<ChatService>().value?.isAlbumPreviewing() ?: false
        if (!WTQuietModeManager.isQuietModeEnable && !isPlaying && isAppInForeground
            && CommonMMKV.settingONSoundsOpen && !isTakingVideo && !isAlbumPreviewing
        ) {
            RingtonePlayer.getInstance().play(RingtonePlayer.TYPE_CHAT_MESSAGE_POPUP)
        }
        if (CommonMMKV.vibration && isAppInForeground) {
            VibratorUtil.vibrateOnReceiveMsg(appContext)
            logInfo(TAG,"vibratorAndPlayRingtone")
        }
    }


    private fun slideToCorrectPosition(viewBinding: ChatPopupMsgBinding){
        //如果当前展示队列没有pop消息或者只有一个pop消息，那么就直接新消息展示到第一个位置
        if (usingMessageViewQueue.isEmpty()) {
            usingMessageViewQueue.offer(viewBinding)
            slideToFirstPosition(viewBinding)
            return
        }

        if (usingMessageViewQueue.size == 1){
            val frontView = usingMessageViewQueue.peek()
            //两个消息类型不一致，那么就把第一个消息下推，然后把新消息放到第一个位置。两个消息类型一致，则旧消息消失
            if (frontView.isVoiceAutoPlayBindingShow != viewBinding.isVoiceAutoPlayBindingShow) {
                slideToSecondPosition(frontView)
            }else{
                logError(TAG,"slideToCorrectPosition: type error = ${frontView.isVoiceAutoPlayBindingShow}")
            }
            usingMessageViewQueue.offer(viewBinding)
            slideToFirstPosition(viewBinding)
            return
        }

        logError(TAG,"slideToCorrectPosition: size error = ${usingMessageViewQueue.size}")
    }

    private fun slideToFirstPosition(viewBinding:ChatPopupMsgBinding, fromInit:Boolean = true){
        logInfo(TAG,"slideToFirstPosition: enqueue view $viewBinding,,activity=${activity}")
        val view = viewBinding.root
        viewBinding.root.tag = FIRST_POSITION_TAG
        if (viewBinding.isVoiceAutoPlayBindingShow  && view.isDragging){
            view.setOriginTranslationY(0f)
            view.setOnDragReleaseListener { shouldHide, duration ->
                if (!shouldHide) view.layoutMarginTop(firstPosition.toInt(), animationDuration)
            }
            return
        }
        if (fromInit) {
            // 首次出来，是从顶部出来
            view.layoutMarginTop(0, 0)
        }
        view.layoutMarginTop(firstPosition.toInt(), animationDuration)
        if (!viewBinding.isVoiceAutoPlayBindingShow) {
            animateGone(viewBinding, notVoiceMsgAliveTime + animationDuration)
            // 非自动播放的消息也支持滑动消失
            viewBinding.root.forbiddenDrag = false
            viewBinding.root.setOnHideListener {
                animateGone(viewBinding, 0L)
                addShowingMessageToRemoveList(viewBinding)
            }
            activity.doOnLifecycle(onDestroy = {
                if (activityList.size <= 1 || topActivity is ChatHomeActivity) {
                    removeByUserMessageList.clear()
                    logInfo(TAG, "onDestroy 清空 removeByUserMessageList：${removeByUserMessageList}")
                }
            })
        }
    }

    private fun animateGone(
        viewBinding: ChatPopupMsgBinding?,
        delayTime: Long,
        duration: Long = animationDuration
    ) {
        if (viewBinding.isNull()) return
        val view = viewBinding!!.root
        view.postDelayed({
            val parentHeight = (view.parent as? ViewGroup)?.height ?: 0
            val translationY = -(firstPosition + parentHeight)
            view.animate().alpha(0f).translationY(translationY).duration = duration
            logInfo(TAG, "animateGone   $delayTime")
        }, delayTime)
    }


    private fun slideToSecondPosition(frontViewBinding:ChatPopupMsgBinding){
        val view = frontViewBinding.root
        // 根据第一次添加的View是否是语音speaking，设置滑动的顶部距离，当下滑的是语音speaking，需要滑动的距离是max
        val slideMarginTop = if (frontViewBinding.isVoiceAutoPlayBindingShow) secondPositionMax
        else secondPositionMin
        frontViewBinding.root.tag = SECOND_POSITION_TAG
        if (frontViewBinding.isVoiceAutoPlayBindingShow && view.isDragging){
            // 设置松手监听，将当前window回到第1个位置（不存在第一个），或者第二个位置（存在第一个的时候），或者消失（向上滑动）
            view.setOriginTranslationY(slideMarginTop)
            view.setOnDragReleaseListener { shouldHide, duration ->
                if(!shouldHide) view.layoutMarginTop(slideMarginTop.toInt(),animationDuration)
            }
            return
        }
        view.layoutMarginTop(slideMarginTop.toInt(), animationDuration)
    }

    /**将待删除的消息添加到列表中*/
    private fun addShowingMessageToRemoveList(binding: ChatPopupMsgBinding) {
        val message = usingMessageMap[binding] ?: return
        if (!removeByUserMessageList.contains(message)) {
            removeByUserMessageList.add(message)
        }
        // 通知behind的activity处理弹窗消失
        removeByUserMessageStateFlow.value = message
    }

    private fun removeVoiceMessageInQueueByConversationId(
        conversationId: String?,
        from: String,
        msgType: MsgType
    ) {
        if (conversationId.isNullOrEmpty()) return

        voiceMsgQueue.removeIf {
            getMessageConversationId(it)?.toString() == conversationId
        }

        logInfo(TAG, "removeVoiceMessageInQueueByConversationId: conversationId=$conversationId")
        if (msgType == MsgType.REPLAY) {
            val playingMessage = homeService?.getHomePlayingPreviewFlow()?.value
            homeService?.stopPlayingPreview(playingMessage)
        } else {
            WTMessageManager.removeByConvIdAndPlayNext(conversationId)
            ChatTracker.onRemoveConversationVoiceMsgClick(from)
        }
    }

    private val ChatPopupMsgBinding.isVoiceAutoPlayBindingShow: Boolean
        get() = root.findViewById<View>(R.id.clVoiceAutoPlay) != null

    private val ChatPopupMsgBinding.isMergeBindingShow: Boolean
        get() = root.findViewById<View>(R.id.clNotifyRoot) != null

    private val ChatPopupMsgBinding.isVoiceBindingShow: Boolean
        get() = root.findViewById<View>(R.id.clVoiceRoot) != null

    private val ChatPopupMsgBinding.clVoiceAutoPlay: ChatPopupMsgVoicemojiItemAutoPlayBinding?
        get() = root.findViewById<View>(R.id.clVoiceAutoPlay)?.let {
            ChatPopupMsgVoicemojiItemAutoPlayBinding.bind(it)
        }

    private val ChatPopupMsgBinding.clVoiceRoot: ChatPopupMsgVoiceItemBinding?
        get() = root.findViewById<View>(R.id.clVoiceRoot)
            ?.let { ChatPopupMsgVoiceItemBinding.bind(it) }

    // get the msg id which is bound to the [ChatPopupMsgVoiceItemBinding]
    private val ChatPopupMsgBinding.asrMsgId: Long?
        get() = clVoiceRoot?.root?.getTag(R.id.wt_pop_voice_msg_id) as? Long


    private val RealTimeMessage.updateTime:Long
        get() = when (val message = this.msg) {
            is IMPushMessage -> message.message.updateTime
            else -> 0
        }

    private fun isGroupMessage(msg: Any):Boolean{
        return when (msg) {
            is IMessage -> msg.isGroup
            is RealTimeMessage -> msg.isGroupMsg()
            is QuickReactNotifyMessage -> msg.message.isGroup
            is ReplayingMessage -> msg.message.isGroup
            else -> false
        }
    }

    private fun isPrivateMessage(msg:Any):Boolean {
        return when (msg) {
            is IMessage -> msg.isPrivate
            is RealTimeMessage -> msg.isPrivateMsg()
            is QuickReactNotifyMessage -> msg.message.isPrivate
            is ReplayingMessage -> msg.message.isPrivate
            else -> false
        }
    }

    private fun getMessageConversationId(msg:Any):Long?{
        return when(msg) {
            is IMessage -> msg.getConversationId().toLongOrNull()
            is RealTimeMessage -> msg.getConversationId()?.toLongOrNull()
            is QuickReactNotifyMessage -> msg.message.getConversationId().toLongOrNull()
            is ReplayingMessage -> msg.message.getConversationId().toLongOrNull()
            else -> null
        }
    }

    private fun isDirectionSend(msg: Any): Boolean {
        return when (msg) {
            is IMessage -> msg.messageDirection == MsgDirection.SEND
            is RealTimeMessage -> msg.isDirectionSend()
            is QuickReactNotifyMessage -> msg.message.messageDirection == MsgDirection.SEND
            is ReplayingMessage -> msg.message.messageDirection == MsgDirection.SEND
            else -> false
        }
    }

    private fun isMessageNotificationMuted(msg: Any): Boolean {
        return when (msg) {
            is IMessage -> MuteInfoManager.isMessageNotificationMuted(msg)
            is RealTimeMessage -> msg.isMessageNotificationMuted()
            is QuickReactNotifyMessage -> MuteInfoManager.isMessageNotificationMuted(msg.message)
            is ReplayingMessage -> MuteInfoManager.isMessageNotificationMuted(msg.message)
            else -> false
        }
    }

    private fun hasShowUpdateDialog(): Boolean {
        return UpdateVersionManager.hasShowUpdateVersion() && isAppInForeground
    }

    /**获取@you*/
    private fun getAddressedString(msg: Any?): String {
        val hasMentionedMe = when (msg) {
            is IMessage -> msg.hasMentionedMe
            is IMPushMessage -> msg.message.hasMentionedMe
            is QuickReactNotifyMessage -> msg.message.hasMentionedMe
            is ReplayingMessage -> msg.message.hasMentionedMe
            else -> false
        }
        return if (hasMentionedMe) R.string.mention_at_you.asString() else ""
    }

    /**获取asr音转文*/
    private fun getAsrString(msg: Any?): String? {
        return when (msg) {
            is IMessage -> msg.obtainAsrText()
            is IMPushMessage -> msg.message.obtainAsrText()
            is QuickReactNotifyMessage -> msg.message.obtainAsrText()
            is ReplayingMessage -> msg.message.obtainAsrText()
            else -> null
        }
    }

    /**获取是否有滤镜信息*/
    private fun hasVoiceFilter(msg: Any?): Boolean {
        return when (msg) {
            is IMessage -> msg.hasVoiceFilter
            is IMPushMessage -> msg.message.hasVoiceFilter
            is QuickReactNotifyMessage -> msg.message.hasVoiceFilter
            is ReplayingMessage -> msg.message.hasVoiceFilter
            else -> false
        }
    }

    /**获取滤镜名称*/
    private fun Any?.getVoiceFilterName(): String? {
        val message:IMessage? =  when (this) {
            is IMessage -> this
            is IMPushMessage -> this.message
            is QuickReactNotifyMessage -> this.message
            is ReplayingMessage -> this.message
            else -> null
        }
        return message?.getVoiceFilterId?.let {
            VoiceFilterHelper.getCachedVoiceFilterNameById(it)
        }
    }

    /**获取消息id*/
    private fun getMsgId(msg: Any?): Long? {
        return when (msg) {
            is IMessage -> msg.msgId
            is IMPushMessage -> msg.message.msgId
            is QuickReactNotifyMessage -> msg.message.msgId
            is ReplayingMessage -> msg.message.msgId
            else -> null
        }
    }

    /**是否开启了自动翻译*/
    private fun isSmartTransMsg(msg: Any?): Boolean {
        return when (msg) {
            is IMessage -> SmartTransManager.isSmartTransMsg(msg)
            is IMPushMessage -> SmartTransManager.isSmartTransMsg(msg.message)
            is QuickReactNotifyMessage -> SmartTransManager.isSmartTransMsg(msg.message)
            is ReplayingMessage -> SmartTransManager.isSmartTransMsg(msg.message)
            else -> false
        }
    }

    /**获取用户昵称*/
    private suspend fun getUserInfo(msg: Any): UserRelationInfo? {
        val userId = when (msg) {
            is IMessage -> msg.userInfo?.userId?.toLongOrNull()
            is IMPushMessage -> msg.message.userInfo?.userId?.toLongOrNull()
            is QuickReactNotifyMessage -> msg.message.userInfo?.userId?.toLongOrNull()
            is ReplayingMessage -> msg.message.userInfo?.userId?.toLongOrNull()
            else -> return null
        }
        return UserRelationCacheManager.getUserRelationInfoByUidSync(userId ?: 0L)
    }

    /**获取语音时长*/
    private fun getVoiceDuration(msg: Any?): String? {
        return when (msg) {
            is IMessage -> msg.getVoiceDurationFormatString()
            is IMPushMessage -> msg.message.getVoiceDurationFormatString()
            is QuickReactNotifyMessage -> msg.message.getVoiceDurationFormatString()
            is ReplayingMessage -> msg.message.getVoiceDurationFormatString()
            else -> return null
        }
    }

    /**获取IM消息*/
    private fun getIMessage(any: Any): IMessage? {
        return when (any) {
            is IMessage -> any
            is IMPushMessage -> any.message
            is QuickReactNotifyMessage -> any.message
            is ReplayingMessage -> any.message
            else -> null
        }
    }

    /**获取消息type*/
    private fun getMessageType(msg: Any): MsgType? {
        if (msg is IMessage){
            return when{
                msg.isTextMessage || msg.isMediaTextMsg -> MsgType.TEXT
                msg.isImageMessage -> MsgType.IMAGE
                msg.isVoiceMojiMessage -> MsgType.VOICEMOJI
                msg.isWTVoiceMessage -> MsgType.ASYNC
                msg.isCommandMessage -> {
                    MsgType.VOICECALL_COMMAND
                }
                msg.isVideoMessage -> MsgType.VIDEO
                msg.isLocationMsg->MsgType.LOCATION
                msg.isVoiceGifMessage -> MsgType.VOICEGIF
                msg.isLivePlaceShareMsg->MsgType.LP_SHARE
                msg.isRealTimeCallMsg->MsgType.RealTimeCall
                msg.isFileMessage -> MsgType.File
                msg.isShareContactMessage -> MsgType.ShareContact
                else ->  null
            }
        }

        if (msg is RealTimeMessage) {
            if (WTQuietModeManager.isQuietModeEnable) {
                return MsgType.ASYNC
            }
            if (msg.msg is IMessage) {
                return when {
                    (msg.msg as IMessage).isVoiceMojiMessage -> MsgType.VOICEMOJI
                    (msg.msg as IMessage).isVoiceGifMessage -> MsgType.VOICEGIF
                    else -> MsgType.VOICE
                }
            }
            return MsgType.VOICE
        }

        if (msg is ReplayingMessage) {
            return MsgType.REPLAY
        }

        if (msg is QuickReactNotifyMessage) {
            return MsgType.QUICK_REACT
        }
        return null
    }

    /**获取打点type*/
    private fun getPopMessageTypeForTrack(binding: ChatPopupMsgBinding,msg: Any):String{
        val msgType = getMessageType(msg)
        return when{
            binding.isMergeBindingShow ->"0"
            msgType == MsgType.TEXT -> "1"
            msgType == MsgType.IMAGE -> "2"
            msgType == MsgType.ASYNC -> "3"
            msgType == MsgType.VOICE -> "4"
            msgType == MsgType.VOICEMOJI -> "7"
            msgType == MsgType.QUICK_REACT -> "8"
            msgType == MsgType.VIDEO -> "9"
            msgType == MsgType.LOCATION -> "10"
            msgType == MsgType.VOICEGIF -> "12"
            msgType == MsgType.RealTimeCall -> "13"
            msgType == MsgType.File -> "14"
            msgType == MsgType.ShareContact -> "15"
            else -> "0"
        }
    }

    private fun isVoiceMojiInChatList(
        realTimeMessage: RealTimeMessage?,
        conversationId: Long?
    ): Boolean {
        (realTimeMessage?.msg as? IMessage)?.let {
            if (it.isVoiceMojiMessage
                && conversationId.isNotNull()
                && conversationId == ChatGlobalInfoRecorder.nowChatListTargetId.value
                && isInThisConversationChatList(conversationId)
            ) {
                return true
            }
        }
        return false
    }

    /**
     * 撤回消息后，从队列移除撤回的消息
     */
    private fun removeMessageInQueueFromRecall(recallMsgList: List<IMessage>) {
        recallMsgList.forEach { recallMsg ->
            val content = recallMsg.content
            if (content is IM5RecallMessage && recallMsg.isRecallWTAutoPlayMessage(content.orgType.toInt())) {
                voiceMsgQueue.removeIf {
                    it is IMPushMessage && it.message.serMsgId == recallMsg.serMsgId
                            || it is ReplayingMessage && it.message.serMsgId == recallMsg.serMsgId
                }
            } else {
                commonMsgQueue.removeIf {
                    it is IMessage && it.serMsgId == recallMsg.serMsgId
                }
            }
        }
    }

    enum class MsgType{
        VOICE,
        ASYNC, //被mute掉（不会实时播放）的语音消息
        TEXT,
        LOCATION,
        IMAGE,
        VOICEMOJI,
        VOICEGIF,
        VOICECALL_COMMAND, //特殊的voicecall命令消息，需要通知栏展示内容的命令消息
        VIDEO, // 视频消息
        REPLAY, // 回放消息
        QUICK_REACT, // 表情快速回复
        LP_SHARE,
        RealTimeCall, // 实时语音/视频通话
        File,// 文件消息
        ShareContact // 分享联系人卡片消息
    }

    enum class NotifyShowNextMsgSource{
        ENQUEUE_MESSAGE,
        ANIMATION_END
    }

}

/***
 * 定义QuickReact提醒消息（非IM消息）
 */
class QuickReactNotifyMessage(
    val message: IMessage,
    val reactionOpUserId: String,
    val voicemoji: String,
)

/**首页重播消息*/
class ReplayingMessage(val message: IMessage)