package com.interfun.buz.chat.common.view.item.delegate

import android.view.LayoutInflater
import android.view.ViewGroup
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.base.ktx.gone
import com.interfun.buz.base.ktx.visible
import com.interfun.buz.chat.databinding.ChatItemShareContactMsgBinding
import com.interfun.buz.common.R
import com.interfun.buz.common.ktx.setPortrait
import com.interfun.buz.domain.im.social.entity.ContactType
import com.interfun.buz.domain.im.social.entity.UserType
import com.interfun.buz.im.message.BuzShareContactMessage
import com.lizhi.im5.sdk.conversation.IM5ConversationType

/**
 * Author: ChenYouSheng
 * Date: 2025/7/7
 * Email: <EMAIL>
 * Desc: 分享联系人卡片
 */
class ChatMsgShareContactItemViewDelegate :
    ChatItemViewDelegate<BuzShareContactMessage, ChatItemShareContactMsgBinding> {

    override fun onBindView(
        binding: ChatItemShareContactMsgBinding,
        content: BuzShareContactMessage,
        isReceive: Boolean,
        convType: IM5ConversationType
    ) {
        val desc = when (content.contactType) {
            ContactType.Group.type ->{
                if (content.groupMemberCount == 1) {
                    R.string.contact_card_one_memeber.asString()
                } else if (content.groupMemberCount > 1) {
                    String.format(
                        R.string.contact_card_memebers.asString(),
                        content.groupMemberCount
                    )
                } else ""
            }

            ContactType.User.type ->
                when (content.userType) {
                    UserType.UserResearch.type,
                    UserType.Official.type -> {
                        R.string.contact_card_oa.asString()
                    }

                    UserType.AIBot.type -> {
                        R.string.contact_card_ai.asString()
                    }

                    else -> {
                        if (content.buzId.isNotEmpty()) {
                            R.string.common_symbol_at.asString() + content.buzId
                        } else ""
                    }
                }

            else -> ""
        }

        when (content.userType) {
            UserType.UserResearch.type,
            UserType.Official.type -> {
                binding.ivAiIcon.gone()
                binding.iftvOfficialTag.visible()
            }

            UserType.AIBot.type -> {
                binding.ivAiIcon.visible()
                binding.iftvOfficialTag.gone()
                binding.ivAiIcon.setImageResource(R.drawable.common_icon_ai_flag)
            }

            else -> {
                binding.ivAiIcon.gone()
                binding.iftvOfficialTag.gone()
            }
        }

        binding.tvDesc.text = desc
        binding.ivContactPortrait.setPortrait(content.portrait)
    }

    fun updateContactName(binding: ChatItemShareContactMsgBinding, contactName: String) {
        binding.tvTitle.text = contactName
    }

    override fun onCreateViewBinding(container: ViewGroup): ChatItemShareContactMsgBinding {
        return ChatItemShareContactMsgBinding.inflate(
            LayoutInflater.from(container.context), container, true
        )
    }
}