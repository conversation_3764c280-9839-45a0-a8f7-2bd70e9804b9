package com.interfun.buz.chat.common.view.widget

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.drawable.ColorDrawable
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.ImageView.ScaleType.FIT_CENTER
import android.view.View
import androidx.annotation.MainThread
import androidx.core.view.children
import coil.Coil
import coil.ImageLoader
import coil.load
import com.interfun.buz.assertutil.buzAssert
import com.interfun.buz.assertutil.buzAssertMain
import com.interfun.buz.base.ktx.*
import com.interfun.buz.base.utils.safe
import com.interfun.buz.biz.center.voicemoji.model.voiceemoji.VoiceEmojiType
import com.interfun.buz.chat.R
import com.interfun.buz.chat.databinding.ChatPreviewReplyBinding
import com.interfun.buz.common.ktx.getDisplayName
import com.interfun.buz.common.ktx.isMe
import com.interfun.buz.common.manager.cache.user.UserRelationCacheManager
import com.interfun.buz.domain.record.helper.RecordStatusHelper
import com.interfun.buz.im.entity.IMMessageContentExtra
import com.interfun.buz.im.entity.IMMessageLocalExtra
import com.interfun.buz.im.entity.IMType
import com.interfun.buz.im.entity.isAllMetadataNullExceptIcon
import com.interfun.buz.im.ktx.*
import com.interfun.buz.im.message.*
import com.lizhi.im5.sdk.message.IMessage
import com.lizhi.im5.sdk.message.MsgReferenceStatus
import com.lizhi.im5.sdk.message.model.IM5MessageType
import com.yibasan.lizhifm.sdk.platformtools.ResUtil
import kotlin.math.roundToInt

data class CommonConfig(val showClose: Boolean)
interface ReplyItemListener {
    fun onReplyClickItem(view: ReplyItemView, data: ReplyPreviewData)
    fun onReplyClickPreviewIcon(view: ReplyItemView, data: ReplyPreviewData)
    fun onReplyClose(replyItemView: ReplyItemView, data: ReplyPreviewData)
}

interface NotNormalPreviewFlag
sealed class ReplyPreviewData private constructor(
    val commonConfig: CommonConfig,
    val msg: IMessage?
) {
    companion object {
        val commonConfigAtEditor = CommonConfig(true)
        val commonConfigAtList = CommonConfig(false)
    }

    class TextReplyPreviewData constructor(
        val title: String,
        val subTitle: String,
        msg: IMessage,
        commonConfig: CommonConfig
    ) : ReplyPreviewData(commonConfig, msg)

    class LinkReplyPreviewData constructor(
        val title: String,
        val subTitle: String?,
        val imgUrl: String?,
        msg: IMessage,
        commonConfig: CommonConfig
    ) : ReplyPreviewData(commonConfig, msg)

    class VoiceReplyPreviewData constructor(
        val title: String,
        val asr: String? = null,
        commonConfig: CommonConfig,
        msg: IMessage,
    ) : ReplyPreviewData(commonConfig, msg)

    class VEReplyPreviewData constructor(
        val title: String,
        val emoji: String,
        commonConfig: CommonConfig,
        msg: IMessage,
    ) : ReplyPreviewData(commonConfig, msg)

    class VGReplyPreviewData constructor(
        val title: String,
        val width:Int,
        val height:Int,
        val animationUrl: String?, // 动图gif
        val thumbnailUrl: String?, // 缩略图gif
        commonConfig: CommonConfig,
        msg: IMessage,
    ) : ReplyPreviewData(commonConfig, msg)

    class BlingBoxReplyPreviewData constructor(
        val title: String,
        val coverUri: String,
        commonConfig: CommonConfig,
        msg: IMessage,

        ) : ReplyPreviewData(commonConfig, msg)

    class LocationReplyPreviewData constructor(
        val title: String,
        val subTitle: String,
        commonConfig: CommonConfig,
        msg: IMessage
    ) : ReplyPreviewData(commonConfig, msg)

    class VideoReplyPreviewData constructor(
        val title: String,
        val coverImgUrl: String,
        commonConfig: CommonConfig,
        msg: IMessage,
    ) : ReplyPreviewData(commonConfig, msg)

    class PicReplyPreviewData constructor(
        val title: String,
        val imgUrl: String,
        commonConfig: CommonConfig,
        msg: IMessage,
    ) : ReplyPreviewData(commonConfig, msg)

    class FileReplyPreviewData constructor(
        val title: String,
        val fileName: String,
        val fileExtension: String,
        commonConfig: CommonConfig,
        msg: IMessage,
    ) : ReplyPreviewData(commonConfig, msg)

    class ShareContactReplyPreviewData(
        val title: String,
        val subTitle: String,
        val imgUrl: String?,
        commonConfig: CommonConfig,
        msg: IMessage,
    ): ReplyPreviewData(commonConfig, msg)

    class UnsupportedReplyPreviewData constructor(
        commonConfig: CommonConfig,
        msg: IMessage,
    ) : ReplyPreviewData(commonConfig, msg), NotNormalPreviewFlag

    class ExceptionReplyPreviewData constructor(
        commonConfig: CommonConfig,
        msg: IMessage,
    ) : ReplyPreviewData(commonConfig, msg), NotNormalPreviewFlag

    class ExceptionNotLocalReplyPreviewData constructor(
        commonConfig: CommonConfig,
    ) : ReplyPreviewData(commonConfig, null), NotNormalPreviewFlag

    class DeletedReplyPreviewData constructor(
        commonConfig: CommonConfig,
        msg: IMessage,
    ) : ReplyPreviewData(commonConfig, msg), NotNormalPreviewFlag

    data object EmptyReplyPreviewData : ReplyPreviewData(commonConfigAtEditor, null)
}

class ReplyItemView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : com.interfun.buz.base.widget.round.RoundConstraintLayout(context, attrs) {
    private val binding = ChatPreviewReplyBinding.inflate(LayoutInflater.from(context), this, true)
    private val previewImgRadius = 12.dpFloat
    private val previewLocationRadius = 8.57.dpFloat
    private val TAG = "ReplyItemView"
    var data: ReplyPreviewData = ReplyPreviewData.EmptyReplyPreviewData
        private set

    var listener: ReplyItemListener? = null
        @MainThread
        set(value) {
            buzAssertMain()
            field = value
            updateListener()
        }

    private fun updateListener() {
        this.clickIfNotRecording {
            data.let { data ->
                listener?.onReplyClickItem(this, data)
            }
        }
//        binding.clVoiceMoji.click { listener?.onReplyClickPreviewIcon(this, data) }
        binding.ivPreviewThumbnail.clickIfNotRecording {
            listener?.onReplyClickPreviewIcon(this, data)
        }
        binding.tvClose.clickIfNotRecording {
            listener?.onReplyClose(this, data)
        }
    }

    private fun View.clickIfNotRecording(block: () -> Unit) {
        this.click {
            if (!RecordStatusHelper.isRecording) {
                block()
            }
        }
    }


    @MainThread
    fun show(data: ReplyPreviewData) {
        logInfo(TAG, "invoke data ${data}")
        this.data = data
        resetUi()
        if (data is ReplyPreviewData.EmptyReplyPreviewData) {
            resetAndGone()
            return
        }
        handleCommonConfig(data.commonConfig)
        visible()
        children.forEach {
            it.alpha = 1f
        }
        when (data) {
            is ReplyPreviewData.TextReplyPreviewData -> {
                handleText(data)
            }

            is ReplyPreviewData.LinkReplyPreviewData -> {
                handleLink(data)
            }

            is ReplyPreviewData.VoiceReplyPreviewData -> {
                handleVoice(data)
            }

            is ReplyPreviewData.LocationReplyPreviewData -> {
                handleLocation(data)
            }

            is ReplyPreviewData.PicReplyPreviewData -> {
                handlePic(data)
            }

            is ReplyPreviewData.FileReplyPreviewData -> {
                handleFile(data)
            }

            is ReplyPreviewData.ShareContactReplyPreviewData -> {
                handleShareContact(data)
            }

            is ReplyPreviewData.VideoReplyPreviewData -> {
                handleVideo(data)
            }

            is ReplyPreviewData.VEReplyPreviewData -> {
                handleVE(data)
            }

            is ReplyPreviewData.VGReplyPreviewData -> {
                handleVG(data)
            }

            is ReplyPreviewData.BlingBoxReplyPreviewData -> {
                handleBlingBox(data)
            }

            is ReplyPreviewData.UnsupportedReplyPreviewData -> {
                handleUnsupported(data)
            }

            is ReplyPreviewData.ExceptionReplyPreviewData -> {
                handleException(data)
            }

            is ReplyPreviewData.ExceptionNotLocalReplyPreviewData -> {
                handleExceptionNotLocal(data)
            }

            is ReplyPreviewData.DeletedReplyPreviewData -> {
                handleDelete(data)
            }

            else -> {
                buzAssert({ false }, { "Unknown type $data " })
                logInfo(TAG, "show => Unknown type $data")
            }
        }

    }

    private fun handleExceptionNotLocal(data: ReplyPreviewData.ExceptionNotLocalReplyPreviewData) {
        binding.tvSubTitle.visible()
        binding.tvSubTitle.text = R.string.im_reply_message_unavailable.asString()
    }

    private fun handleLink(data: ReplyPreviewData.LinkReplyPreviewData) {
        binding.tvTitle.visible()
        binding.tvTitle.text = data.title
        binding.tvSubTitle.visible()
        binding.tvSubTitle.text =
            ResUtil.getString(R.string.chat_pop_msg_link_tag, data.subTitle ?: "")
        if (!data.imgUrl.isNullOrEmpty()) {
            binding.ivPreviewThumbnail.visible()
            binding.ivPreviewThumbnail.load(
                data.imgUrl,
                ImageLoader
                    .Builder(context)
                    .allowHardware(false)
                    .error(R.drawable.ps_ic_placeholder)
                    .placeholder(R.drawable.ps_ic_placeholder)
                    .build()
            ) {
                crossfade(true)
                transformations(coil.transform.RoundedCornersTransformation(previewImgRadius))
            }
        }
    }

    private fun handleCommonConfig(commonConfig: CommonConfig) {
        if (commonConfig.showClose) {
            binding.tvClose.visible()
        } else {
            binding.tvClose.gone()
        }
    }

    private fun handleDelete(data: ReplyPreviewData.DeletedReplyPreviewData) {
        binding.tvSubTitle.visible()
        binding.tvSubTitle.text = R.string.im_reply_message_been_deleted.asString()

    }

    private fun handleException(data: ReplyPreviewData.ExceptionReplyPreviewData) {
        binding.tvSubTitle.visible()
        binding.tvSubTitle.text = R.string.im_reply_message_not_found.asString()
    }

    private fun handleUnsupported(data: ReplyPreviewData.UnsupportedReplyPreviewData) {
        binding.tvSubTitle.visible()
        binding.tvSubTitle.text = R.string.update_buz_tip.asString()
    }

    private fun handleBlingBox(data: ReplyPreviewData.BlingBoxReplyPreviewData) {
        binding.tvTitle.visible()
        binding.tvTitle.text = data.title
        binding.tvSubTitle.visible()
        binding.tvSubTitle.text = R.string.notification_tile_voicemoji.asString()
        binding.clVoiceMoji.visible()
        binding.ivVoiceMoji.setVoiceEmojiUrl(data.coverUri, VoiceEmojiType.Image)


    }

    private fun handleVE(data: ReplyPreviewData.VEReplyPreviewData) {
        binding.tvTitle.visible()
        binding.tvTitle.text = data.title
        binding.tvSubTitle.visible()
        binding.tvSubTitle.text = R.string.notification_tile_voicemoji.asString()
        binding.clVoiceMoji.visible()
        binding.ivVoiceMoji.setVoiceEmojiUrl(data.emoji, VoiceEmojiType.Text)
    }

    private fun handleVG(data: ReplyPreviewData.VGReplyPreviewData) {
        binding.tvTitle.visible()
        binding.tvTitle.text = data.title
        binding.tvSubTitle.visible()
        binding.tvSubTitle.text = R.string.voice_gif_tag.asString()
        data.thumbnailUrl?.let {
            binding.vgVoiceGif.visible()
            binding.avVoiceGif.loadThumbnailUrl(it)
            safe(data.width, data.height) { width, height ->
                if (width <= 0 || height <= 0) {
                    return
                }
                val adjustHeight = 40.dp.toFloat()
                val adjustWidth = adjustHeight / height * width
                binding.avVoiceGif.layoutSize(adjustWidth.roundToInt(), adjustHeight.roundToInt())
            }
        }
    }

    private fun handleVideo(data: ReplyPreviewData.VideoReplyPreviewData) {
        binding.tvTitle.visible()
        binding.tvTitle.text = data.title
        binding.tvSubTitle.visible()
        binding.tvSubTitle.text = R.string.chat_pop_msg_video_tag.asString()
        binding.ivPreviewThumbnail.visible()
        binding.ivPreviewThumbnailPlay.visible()
        binding.ivPreviewThumbnail.load(
            data.coverImgUrl,
            ImageLoader
                .Builder(context)
                .allowHardware(false)
                .error(R.drawable.ps_ic_placeholder)
                .placeholder(R.drawable.ps_ic_placeholder)
                .build()
        ) {
            crossfade(true)
            transformations(coil.transform.RoundedCornersTransformation(previewImgRadius))
        }
    }


    private fun handlePic(data: ReplyPreviewData.PicReplyPreviewData) {
        binding.tvTitle.visible()
        binding.tvTitle.text = data.title
        binding.tvSubTitle.visible()
        binding.tvSubTitle.text = R.string.chat_pop_msg_photo_tag.asString()
        binding.ivPreviewThumbnail.visible()
        binding.ivPreviewThumbnail.load(
            data.imgUrl,
            Coil.imageLoader(context).newBuilder()
                .error(R.drawable.ps_ic_placeholder)
                .placeholder(R.drawable.ps_ic_placeholder)
                // .allowHardware(false)
//                .diskCache()
                .build()
        ) {
            crossfade(true)
            transformations(coil.transform.RoundedCornersTransformation(previewImgRadius))
        }
    }

    @SuppressLint("SetTextI18n")
    private fun handleFile(data: ReplyPreviewData.FileReplyPreviewData) {
        binding.tvTitle.visible()
        binding.tvTitle.text = data.title
        binding.tvSubTitle.visible()
        binding.tvSubTitle.text = context.getString(R.string.chat_pop_msg_file_tag, data.fileName)
        binding.tvFilExt.visible()
        binding.tvFilExt.text = data.fileExtension.uppercase()
        binding.ivPreviewThumbnail.visible()
        binding.ivPreviewThumbnail.scaleType = FIT_CENTER
        binding.ivPreviewThumbnail.load(R.drawable.file_preview_image_drawable) {
            crossfade(true)
        }
    }

    private fun handleShareContact(data: ReplyPreviewData.ShareContactReplyPreviewData) {
        binding.tvTitle.visible()
        binding.tvTitle.text = data.title
        binding.tvSubTitle.visible()
        binding.tvSubTitle.text = String.format(R.string.contact_card_user_contact.asString(), data.subTitle)
        binding.groupContactPortrait.visible()
        val parentColor = (background as? ColorDrawable)?.color
        if (null != parentColor) {
            binding.roundView.backgroundColor(parentColor)
        }
        binding.portraitImageView.setPortrait(data.imgUrl)
    }

    private fun handleLocation(data: ReplyPreviewData.LocationReplyPreviewData) {
        binding.tvTitle.visible()
        binding.tvTitle.text = data.title
        binding.tvSubTitle.visible()
        binding.tvSubTitle.text = data.subTitle
        binding.ivPreviewThumbnail.visible()
        binding.ivPreviewThumbnail.load(R.drawable.home_pic_map) {
            crossfade(true)
            transformations(coil.transform.RoundedCornersTransformation(previewLocationRadius))
        }
    }

    @SuppressLint("SetTextI18n")
    private fun handleVoice(data: ReplyPreviewData.VoiceReplyPreviewData) {
        binding.tvTitle.visible()
        binding.tvTitle.text = data.title
        binding.tvSubTitle.visible()
        binding.tvSubTitle.text = (R.string.chat_pop_msg_voice_tag.asString() + (data.asr ?: ""))

    }

    private fun resetUi() {
        binding.tvTitle.gone()
        binding.tvSubTitle.gone()
        binding.tvClose.gone()
        binding.clVoiceMoji.gone()
        binding.vgVoiceGif.gone()
        binding.ivPreviewThumbnail.gone()
        binding.ivPreviewThumbnailPlay.gone()
        binding.groupContactPortrait.gone()
    }

    private fun handleText(data: ReplyPreviewData.TextReplyPreviewData) {
        binding.tvTitle.visible()
        binding.tvTitle.text = data.title
        binding.tvSubTitle.visible()
        binding.tvSubTitle.text = data.subTitle
    }

    private fun resetAndGone() {
        gone()
    }


    private fun obtainUserName(userId: Long): String {
        return if (userId.isMe()) {
            R.string.you.asString()
        } else {
            UserRelationCacheManager.getUserRelationInfoByUid(userId)?.getDisplayName()
                ?: ""
        }
    }

    fun quickShow(msg: IMessage?, config: CommonConfig, status: MsgReferenceStatus) {
        if (!quickShowImpl(msg, config, status)) {
            show(ReplyPreviewData.EmptyReplyPreviewData)
        }
    }

    fun setPlayVPTransitionName(tranName: String) {
        binding.ivPreviewThumbnail.transitionName = tranName
    }

    /**
     *
     * 分5步:
     * - step1: 如果引用状态为[MsgReferenceStatus.NONE] 证明没有引用,直接,返回false.
     *
     * - step2: 如果引用状态为[MsgReferenceStatus.MSG_DELETE]或者引用消息没有找到, 那么显示消息未找到并,返回true
     * - step3: 如果引用状态为[MsgReferenceStatus.MSG_RECALL]显示消息被删除,返回true
     * - step4: 判断引用状态是否为[MsgReferenceStatus.MSG_NORMAL]那么正常展示对应消息并,返回true
     * - step5: 最后兜底在[MsgReferenceStatus.MSG_NORMAL]不成立执行同step2逻辑, 返回true
     * @return true 显示回复预览, false 隐藏对组件
     */
    private fun quickShowImpl(
        msg: IMessage?,
        config: CommonConfig,
        status: MsgReferenceStatus
    ): Boolean {
        logInfo(
            TAG,
            "quickShow invoke  serMsgId: ${msg?.serMsgId} type: ${msg?.msgType} content: ${msg?.content} userId: ${msg?.userId} MsgReferenceStatus: ${status}"
        )
        val userId = msg?.userId
        //step1
        if (status == MsgReferenceStatus.NONE) {
            return false
        }
        //step2
        if (msg == null || msg.content == null || status == MsgReferenceStatus.MSG_DELETE) {
            show(
                ReplyPreviewData.ExceptionNotLocalReplyPreviewData(
                    config
                )
            )
            return true
        }


        if (userId == null) {
            return false
        }
        //step3
        if (status == MsgReferenceStatus.MSG_RECALL) {
            show(
                ReplyPreviewData.DeletedReplyPreviewData(
                    config,
                    msg
                )
            )
            return true
        }
        //step4
        if (status == MsgReferenceStatus.MSG_NORMAL) {
            val previewData = ReplyPreviewDataFactory.create(msg, config)
            return if (previewData != null) {
                show(previewData)
                true
            } else false
        } else {
            //step5
            show(
                ReplyPreviewData.ExceptionNotLocalReplyPreviewData(
                    config
                )
            )
        }
        return true
    }

}

internal object ReplyPreviewDataFactory {

    fun create(msg: IMessage, config: CommonConfig): ReplyPreviewData? {
        val userId = msg.userId ?: return null
        return when {
            msg.isTextMessage -> handleTextMessage(msg, userId, config)
            msg.isImageMessage -> handleImageMessage(msg, userId, config)
            msg.isBuzVoiceMsg -> handleBuzVoiceMsg(msg, userId, config)
            msg.isMediaTextMsg -> handleMediaTextMsg(msg, userId, config)
            msg.isRealTimeCallMsg -> handleRealTimeCallMsg(msg, userId, config)
            msg.msgType == IM5MessageType.TYPE_CONTENT_RECALLMESSAGE -> handleRecallMsg(msg, config)
            msg.msgType == IMType.TYPE_VOICE_TEXT_NEW -> handleVoiceTextMsgNew(msg, userId, config)
            msg.isVideoMessage -> handleVideoMessage(msg, userId, config)
            msg.isFileMessage -> handleFileMessage(msg, userId, config)
            msg.isShareContactMessage -> handleShareContactMessage(msg, userId, config)
            msg.isLocationMsg -> handleLocationMessage(msg, userId, config)
            msg.msgType == IMType.TYPE_VOICE_TEXT -> handleVoiceTextMsg(msg, userId, config)
            msg.msgType == IMType.TYPE_VOICE_GIF -> handleVoiceGifMsg(msg, userId, config)
            msg.msgType == IMType.TYPE_VOICE_EMOJI -> handleVoiceEmojiMsg(msg, userId, config)
            msg.msgType == IMType.TYPE_VOICE_EMOJI_IMG -> handleVoiceEmojiImgMsg(msg, userId, config)
            else -> ReplyPreviewData.UnsupportedReplyPreviewData(config, msg)
        }
    }

    private fun handleTextMessage(
        msg: IMessage,
        userId: Long,
        config: CommonConfig
    ): ReplyPreviewData? {
        val buzTextMsg = msg.content as? BuzTextMsg ?: return null
        val hyperlinkMetadataExtra =
            IMMessageContentExtra.parseFromJson(msg.content.extra).contentStyleExtra?.hyperlinkMetadataExtra
        return if (hyperlinkMetadataExtra != null && !hyperlinkMetadataExtra.isAllMetadataNullExceptIcon()) {
            ReplyPreviewData.LinkReplyPreviewData(
                obtainUserName(userId),
                buzTextMsg.mentionedInfoText().toString(),
                hyperlinkMetadataExtra.linkImagePath,
//                        "https://img2.baidu.com/it/u=3939798912,3502182651&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=667",
                msg,
                config
            )
        } else {
            ReplyPreviewData.TextReplyPreviewData(
                obtainUserName(userId),
                buzTextMsg.mentionedInfoText().toString(),
                msg,
                config
            )
        }
    }

    private fun handleImageMessage(
        msg: IMessage,
        userId: Long,
        config: CommonConfig
    ): ReplyPreviewData? {
        val picMsg = msg.content as? BuzImageMessage ?: return null
        val thumbnailUrl = picMsg.thumbUrl
        return if (thumbnailUrl.isNullOrEmpty()) null else ReplyPreviewData.PicReplyPreviewData(
            obtainUserName(userId),
            picMsg.thumbUrl,
            config, msg
        )
    }

    private fun handleFileMessage(
        msg: IMessage,
        userId: Long,
        config: CommonConfig
    ): ReplyPreviewData? {
        val fileMsg = msg.content as? BuzFileMessage ?: return null
        return ReplyPreviewData.FileReplyPreviewData(
            title = obtainUserName(userId),
            fileName = fileMsg.name,
            fileExtension = fileMsg.extName ?: "",
            commonConfig = config,
            msg = msg
        )
    }

    private fun handleShareContactMessage(
        msg: IMessage,
        userId: Long,
        config: CommonConfig
    ): ReplyPreviewData? {
        val msgContent = msg.content as? BuzShareContactMessage ?: return null
        return ReplyPreviewData.ShareContactReplyPreviewData(
            title = obtainUserName(userId),
            subTitle = msgContent.name,
            imgUrl = msgContent.portrait,
            msg = msg,
            commonConfig = config
        )
    }

    private fun handleBuzVoiceMsg(
        msg: IMessage,
        userId: Long,
        config: CommonConfig
    ): ReplyPreviewData? {
        val voiceMsg = msg.content as? BuzVoiceMsg ?: return null
        val messageLocalExtra = IMMessageLocalExtra.parseFromJson(msg.localExtra)
        return ReplyPreviewData.VoiceReplyPreviewData(
            obtainUserName(userId),
            if (messageLocalExtra.showAsrText) {
                voiceMsg.asrText
            } else "",
            config, msg
        )
    }

    private fun handleMediaTextMsg(
        msg: IMessage,
        userId: Long,
        config: CommonConfig
    ): ReplyPreviewData? {
        val mediaText = msg.content as? MediaTextMsg ?: return null
        return ReplyPreviewData.TextReplyPreviewData(
            obtainUserName(userId),
            mediaText.title,
            msg, config
        )
    }

    private fun handleRealTimeCallMsg(
        msg: IMessage,
        userId: Long,
        config: CommonConfig
    ): ReplyPreviewData? {
        val realTimeCallMsg = msg.content as? RealTimeCallInviteMsg ?: return null
        return ReplyPreviewData.TextReplyPreviewData(
            obtainUserName(userId),
            realTimeCallMsg.title,
            msg, config
        )
    }

    private fun handleVoiceTextMsgNew(
        msg: IMessage,
        userId: Long,
        config: CommonConfig
    ): ReplyPreviewData? {
        val voiceMsg = msg.content as? VoiceTextMsgNew ?: return null
        return ReplyPreviewData.VoiceReplyPreviewData(
            obtainUserName(userId),
            voiceMsg.text,
            config, msg
        )
    }

    private fun handleVideoMessage(
        msg: IMessage,
        userId: Long,
        config: CommonConfig
    ): ReplyPreviewData? {
        val picMsg = msg.content as? BuzVideoMsg ?: return null
        return ReplyPreviewData.VideoReplyPreviewData(
            obtainUserName(userId),
            picMsg.coverThumbUrl() ?: "",
            config, msg
        )
    }


    private fun handleLocationMessage(
        msg: IMessage,
        userId: Long,
        config: CommonConfig
    ): ReplyPreviewData? {
        val locMsg = msg.content as? BuzLocationMessage ?: return null
        val locationName = locMsg.locationName
        val adrName = locMsg.locationAddress
        val subTitle =
            R.string.location.asString() + locationName.ifEmpty {
                adrName.ifEmpty { "" }
            }
        return ReplyPreviewData.LocationReplyPreviewData(
            obtainUserName(userId), subTitle,
            config,
            msg
        )
    }

    private fun handleVoiceTextMsg(
        msg: IMessage,
        userId: Long,
        config: CommonConfig
    ): ReplyPreviewData? {
        val videoMsg = msg.content as? VoiceTextMsg ?: return null
        return ReplyPreviewData.VoiceReplyPreviewData(
            obtainUserName(userId),
            videoMsg.text,
            config, msg
        )
    }

    private fun handleVoiceGifMsg(
        msg: IMessage,
        userId: Long,
        config: CommonConfig
    ): ReplyPreviewData? {
        val vgMsg = msg.content as? BuzVoiceGifMsg ?: return null
        return ReplyPreviewData.VGReplyPreviewData(
            obtainUserName(userId),
            vgMsg.width,
            vgMsg.height,
            vgMsg.animationUrl,
            vgMsg.thumbnailUrl,
            config,
            msg
        )
    }


    private fun handleVoiceEmojiMsg(
        msg: IMessage,
        userId: Long,
        config: CommonConfig

    ): ReplyPreviewData? {
        val veMsg = msg.content as? WTVoiceEmojiMsg ?: return null
        return ReplyPreviewData.VEReplyPreviewData(
            obtainUserName(userId),
            veMsg.emojiIcon,
            config,
            msg
        )
    }


    private fun handleVoiceEmojiImgMsg(
        msg: IMessage,
        userId: Long,
        config: CommonConfig
    ): ReplyPreviewData? {
        val veMsg = msg.content as? WTVoiceEmojiImgMsg ?: return null
        return ReplyPreviewData.BlingBoxReplyPreviewData(
            obtainUserName(userId),
            veMsg.emojiIcon,
            config,
            msg
        )
    }

    private fun handleRecallMsg(
        msg: IMessage,
        config: CommonConfig
    ): ReplyPreviewData {
        return ReplyPreviewData.DeletedReplyPreviewData(
            commonConfig = config, msg
        )
    }

    private fun obtainUserName(userId: Long): String {
        return if (userId.isMe()) {
            R.string.you.asString()
        } else {
            UserRelationCacheManager.getUserRelationInfoByUid(userId)?.getDisplayName()
                ?: ""
        }
    }
}