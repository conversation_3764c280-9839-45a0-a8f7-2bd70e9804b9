package com.interfun.buz.chat.common.view.activity

import android.Manifest
import android.app.NotificationManager
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Build.VERSION_CODES
import android.os.Bundle
import android.os.PowerManager
import android.view.MotionEvent
import android.view.View
import androidx.activity.addCallback
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle.State
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.alibaba.android.arouter.core.LogisticsCenter
import com.alibaba.android.arouter.facade.annotation.Route
import com.alibaba.android.arouter.launcher.ARouter
import com.interfun.buz.album.manager.AlbumManager
import com.interfun.buz.base.ktx.*
import com.interfun.buz.base.utils.BusUtil
import com.interfun.buz.base.utils.FloatPermissionUtils
import com.interfun.buz.base.utils.PinyinUtils
import com.interfun.buz.biz.center.voicemoji.manager.VoiceEmojiPreloadHelper
import com.interfun.buz.biz.center.voicemoji.repository.tabnavigation.TabNavigationRepository
import com.interfun.buz.biz.center.voicemoji.repository.voiceemoji.VoiceEmojiRepository
import com.interfun.buz.chat.BuildConfig
import com.interfun.buz.chat.R
import com.interfun.buz.chat.common.manager.PushCompensationManager
import com.interfun.buz.chat.common.view.block.HomeSourceAndInviteRouterBlock
import com.interfun.buz.chat.common.view.fragment.ChatHomeFragment
import com.interfun.buz.chat.group.view.dialog.GroupInfoDialog
import com.interfun.buz.chat.voicemoji.manager.VoiceMojiManager
import com.interfun.buz.chat.wt.event.WTVoiceMojiHidePanelEvent
import com.interfun.buz.chat.wt.manager.WTQuietModeManager
import com.interfun.buz.chat.wt.manager.WTStatusManager
import com.interfun.buz.chat.wt.manager.WTVoiceEmojiManager
import com.interfun.buz.chat.wt.service.WalkieTalkieService
import com.interfun.buz.chat.wt.viewmodel.HomeActivityViewModel
import com.interfun.buz.common.arouter.NavManager
import com.interfun.buz.common.arouter.getFragmentByRouter
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.base.binding.VCMinimizeBaseActivity
import com.interfun.buz.common.base.binding.ViewPager2LazyBindingFragment
import com.interfun.buz.common.bean.chat.ChatJumpType
import com.interfun.buz.common.bean.chat.GroupChatJumpInfo
import com.interfun.buz.common.bean.chat.PrivateChatJumpInfo
import com.interfun.buz.common.constants.*
import com.interfun.buz.common.database.entity.PlaceType
import com.interfun.buz.common.database.entity.UserRelationInfo
import com.interfun.buz.common.eventbus.HomePageChangeEvent
import com.interfun.buz.common.eventbus.HomePageEnum
import com.interfun.buz.common.eventbus.HomePageEnum.PageHome
import com.interfun.buz.common.eventbus.HomePageJumpSourceEvent
import com.interfun.buz.common.eventbus.chat.ContactScrollToTopEvent
import com.interfun.buz.common.ktx.regionCode
import com.interfun.buz.common.ktx.uid
import com.interfun.buz.common.manager.*
import com.interfun.buz.common.manager.earphone.EarPhoneManager
import com.interfun.buz.common.manager.login.LoginMainABTestManager
import com.interfun.buz.common.manager.router.RouterManager
import com.interfun.buz.common.manager.update.UpdateVersionManager
import com.interfun.buz.common.service.ContactsService
import com.interfun.buz.common.service.IBuzSharedService
import com.interfun.buz.common.utils.BuzTracker
import com.interfun.buz.common.utils.CommonTracker
import com.interfun.buz.common.voiceemoji.IVoiceEmojiSupport
import com.interfun.buz.domain.record.helper.RecordStatusHelper
import com.interfun.buz.im.signal.PushAgentManager
import com.interfun.buz.liveplace.manager.LivePlaceCacheHelper
import com.lizhi.component.itnet.base.getVersionCodeFromManifest
import curtains.onNextDraw
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch
import kotlin.math.abs

/**
 * <AUTHOR>
 *
 * @date 2022/7/6
 *
 * @desc Include：
 * [com.interfun.buz.chat.common.view.fragment.ChatHomeFragment]
 * [com.interfun.buz.user.view.fragment.MyProfileFragment]
 * [com.interfun.buz.contacts.view.fragment.ContactsHomeFragment]
 */
@Route(path = PATH_CHAT_ACTIVITY_HOME)
@AndroidEntryPoint
class ChatHomeActivity : VCMinimizeBaseActivity(), IVoiceEmojiSupport {

    override var navBarColor = R.color.color_background_2_default.asColor()

    private var mHomeSourceAndInviteRouterBlock: HomeSourceAndInviteRouterBlock? = null
    private val TAG = "ChatHomeActivity"
    val vpHome get() = customContentView as ViewPager2
    private var fragmentList:List<Fragment> = emptyList()
    private val contactService by routerServices<ContactsService>()
    private val currentPositionFlow = MutableStateFlow(0)
    private val refreshMinimizedBgFlow = MutableSharedFlow<Any>(1)
    private val source by lazy { activity.intent?.getStringExtra(RouterParamKey.ChatHome.KEY_OPEN_SOURCE) }
    private val pushCompensationManager = PushCompensationManager()

    override fun dispatchTouchEvent(ev: MotionEvent): Boolean {
        return super.dispatchTouchEvent(ev)

    }

    companion object {

        fun createIntent(context: Context, router: String?): Intent {
            return Intent(context, ChatHomeActivity::class.java).apply {
                putExtra(RouterParamKey.ChatHome.KEY_ROUTER, router)
            }
        }
        private var currentHomePage: HomePageEnum? = null
            set(value) {
                field = value
                currentSelectPageFlow.value = field
            }
        val currentSelectPageFlow = MutableStateFlow(currentHomePage)

        fun isShowPageHome(): Boolean {
            return currentHomePage == PageHome
        }
    }

    private var lastBackTime: Long = 0
    private val activityViewModel by lazy { ViewModelProvider(this)[HomeActivityViewModel::class.java] }

    override fun createContentView(): View {
        return ViewPager2(this).apply {
            orientation = ViewPager2.ORIENTATION_HORIZONTAL
            id = R.id.id_chat_home_vp2
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        AppConfigRequestManager.requestAppConfigWithLogin()
        super.onCreate(savedInstanceState)
        GlobalScope.launchIO {
            AlbumManager.instance.clearAllVideoItemBitmapCache()
        }
        UpdateVersionManager.checkVersion()
        initHomeFragmentList()
        RecordStatusHelper.reset()
        if (savedInstanceState == null){
            handleAppRouter()
            BindPhonePopupManager.reset()
        }
        ABTestManager.requestIfFailed()
        PushAgentManager.connect()
        window.onNextDraw {
            logInfo(TAG,"ChatHomeActivity onNextDraw,${System.currentTimeMillis()}")
            mainThreadHandler.post {
                afterNextDraw()
            }
        }
        lifecycleScope.launchIO {
            CommonMMKV.hasLoginSuccess = true
        }
        onBackPressedDispatcher.addCallback {
            customOnBackPressed()
        }
    }

    private fun initHomeFragmentList() {
        var chatHomeFragment: Fragment? = null
        var profileFragment: Fragment? = null
        var contactsFragment: Fragment? = null

        val profileFragmentPostcard = ARouter.getInstance().build(PATH_USER_FRAGMENT_PROFILE)
        val contactsFragmentHomePostcard = ARouter.getInstance().build(PATH_CONTACTS_FRAGMENT_HOME)
        val homeFragmentNewPostcard = ARouter.getInstance().build(PATH_CHAT_FRAGMENT_HOME_NEW)
        LogisticsCenter.completion(profileFragmentPostcard)
        LogisticsCenter.completion(contactsFragmentHomePostcard)
        LogisticsCenter.completion(homeFragmentNewPostcard)
        for (fragment in supportFragmentManager.fragments) {
            val fragmentClass = fragment::class.java
//            Debug.waitForDebugger()
            logInfo(TAG,"reuse fragment ${fragmentClass}")
            if (fragmentClass == profileFragmentPostcard.destination) {
                profileFragment = fragment
                logInfo(TAG, "reuse fragment ${fragmentClass} found")
            } else if (fragmentClass == contactsFragmentHomePostcard.destination) {
                contactsFragment = fragment
                logInfo(TAG, "reuse fragment ${fragmentClass} found")
            } /*else if (fragment is ChatHomeFragment) {
                chatHomeFragment = fragment
                logInfo(TAG, "reuse fragment ${fragmentClass} found")
            }*/ else if (fragmentClass == homeFragmentNewPostcard.destination) {
                chatHomeFragment = fragment
                logInfo(TAG, "reuse fragment ${fragmentClass} found")
            }
        }
        val isShowNewHomePagePlanB = LoginMainABTestManager.isShowNewHomePagePlanB
        val homeFragment = if (isShowNewHomePagePlanB)
            getFragmentByRouter(PATH_CHAT_FRAGMENT_HOME_NEW)
        else getFragmentByRouter(PATH_CHAT_FRAGMENT_HOME)

        logInfo(TAG,"initHomeFragmentList==>isShowNewHomePagePlanB:${isShowNewHomePagePlanB}," +
                "homeFragment:${homeFragment}")
        fragmentList = listOf(
            profileFragment ?: getFragmentByRouter(PATH_USER_FRAGMENT_PROFILE),
            chatHomeFragment ?: homeFragment,
            contactsFragment ?: getFragmentByRouter(PATH_CONTACTS_FRAGMENT_HOME)
        )
    }

    override fun initView() {
        vpHome.isUserInputEnabled = false

        vpHome.apply {
            offscreenPageLimit = 1
            setPageTransformer { page, position ->
                val index = (page.parent as RecyclerView).getChildLayoutPosition(page)
                if (index == PageHome.value) {
                    page.x = 0f
                    page.z = -0.01f
                    page.alphaBySpeed(1f - abs(position), 0.8f)
                    page.scaleBySpeed(1f - abs(position), 0.5f, 0.95f)
                }
            }
            registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
                override fun onPageSelected(position: Int) {
                    currentPositionFlow.value = position
                    ContactsPushTipsManager.updateContactPageOpenStatus(
                        TAG,
                        position == HomePageEnum.PageContact.value
                    )
                }
            })
            val adapter = object : FragmentStateAdapter(supportFragmentManager, lifecycle) {
                override fun getItemCount() = fragmentList.size
                override fun createFragment(position: Int) : Fragment{
                    return fragmentList[position]
                }
            }
            this.adapter = adapter
            setCurrentItem(PageHome.value, false)
            currentHomePage = PageHome
            (getChildAt(0) as? RecyclerView)?.isNestedScrollingEnabled = true
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        mHomeSourceAndInviteRouterBlock?.onSaveInstanceState(outState)
    }

    override fun onRestoreInstanceState(savedInstanceState: Bundle) {
        super.onRestoreInstanceState(savedInstanceState)
        mHomeSourceAndInviteRouterBlock?.onRestoreInstanceState(savedInstanceState)
    }

    override fun initBlock() {
        mHomeSourceAndInviteRouterBlock = HomeSourceAndInviteRouterBlock(this).bind(this)
        contactService?.createContactAddFriendsGuideDialogBlock(this)?.bind(this)
    }

    override fun initData() {
        super.initData()
        logInfo(TAG, "initData")
        LivePlaceCacheHelper.updateExistsInfoToMem(
            targetId = UserSessionManager.uid,
            placeType = PlaceType.PRIVATE
        )
        BusUtil.observe<HomePageChangeEvent>(this) {
            val index = it.pageIndex.value
            if (vpHome.currentItem != index) {
                vpHome.setCurrentItem(index, true)
                currentHomePage = it.pageIndex
                val fragment = fragmentList[index]
                if (fragment is ViewPager2LazyBindingFragment<*>){
                    fragment.preLoad()
                }
                if (index == HomePageEnum.PageContact.value) {
                    BusUtil.post(ContactScrollToTopEvent())
                }
            }
        }
        handleSelfRouter()
        lifecycleScope.launch {
            refreshMinimizedBgFlow.emit(Any())
        }
        combine(refreshMinimizedBgFlow, currentPositionFlow) { _, pos ->
            pos
        }.collectInScope(lifecycleScope){ pos ->
            setMinimizedBackground(pos)
        }
        activityViewModel.viewPanelMaskAlphaFlow.collectInScope(lifecycleScope) { alpha ->
            setMinimizedViewBgAlpha(alpha)
        }
    }

    /**
     * 弹出新功能状态栏提示
     */
    private fun checkNeedShowNewFeatureNotification() {
        if (!CommonMMKV.checkShowNewFeatureNotification && CommonMMKV.showNotificationInThisVersion) {
            vpHome.postDelayed({
                FeatureNotificationManager.showNotification()
                CommonMMKV.checkShowNewFeatureNotification = true
            }, 2000)
        }
    }

    private fun handleAppRouter() {
        val router = intent.getStringExtra(RouterParamKey.Startup.KEY_ROUTER)
        logInfo("ChatHomeActivity", "handleAppRouter:$router ")
        clearRouterValue(intent, RouterParamKey.Startup.KEY_ROUTER)
        if (router != null) {
//            if (ChatHomeUtil.handleRouterInterceptor(this, router)) return
            RouterManager.handle(this, router)
        }
    }

    private fun handleSelfRouter(newIntent: Intent? = null) {
        val useIntent = newIntent ?: intent
        val wtTargetId = useIntent.getLongExtra(RouterParamKey.ChatHome.KEY_WT_TARGET_ID, 0)
        val wtOpenChatHistory = useIntent.getBooleanExtra(RouterParamKey.ChatHome.KEY_WT_NEED_CHAT_HISTORY, false)
        val wtTargetType = useIntent.getIntExtra(RouterParamKey.ChatHome.KEY_WT_TARGET_TYPE, 1)
        val openVE = useIntent.getBooleanExtra(RouterParamKey.ChatHome.KEY_OPEN_VE_PANEL, false)
        val openMorePanel = useIntent.getBooleanExtra(RouterParamKey.ChatHome.KEY_OPEN_MORE_PANEL, false)
        val exclusiveId = useIntent.getStringExtra(RouterParamKey.Common.KEY_EXCLUSIVE_ID)
        // notify the source
        val source = useIntent.getIntExtra(RouterParamKey.ChatHome.KEY_SOURCE, -1)
        clearRouterValue(useIntent, RouterParamKey.ChatHome.KEY_OPEN_VE_PANEL)
        clearRouterValue(useIntent, RouterParamKey.ChatHome.KEY_OPEN_MORE_PANEL)
        val createLivePanel = useIntent.getBooleanExtra(RouterParamKey.ChatHome.KEY_CREATE_LIVE_PLACE, false)

        logInfo(TAG, "handleSelfRouter-->wtTargetId=$wtTargetId, openVE=$openVE, openMorePanel=$openMorePanel, createLivePanel=$createLivePanel")
        if (wtTargetId > 0L) {
            //Turn on wt mode, if the current wt mode is off
            BusUtil.post(HomePageChangeEvent(HomePageEnum.PageHome))
            val info = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                useIntent.getParcelableExtra(
                    RouterParamKey.ChatHome.KEY_USER_INFO,
                    UserRelationInfo::class.java
                )
            } else {
                useIntent.getParcelableExtra(
                    RouterParamKey.ChatHome.KEY_USER_INFO
                )
            }
            GlobalEventManager.homesRouterTarget.postValue(wtTargetId to info)
            closeTargetDialog()

            if (wtOpenChatHistory) {
                if (wtTargetType==2) {
                    NavManager.startGroupChatActivity(
                        activity,
                        GroupChatJumpInfo(wtTargetId, ChatJumpType.WT)
                    )
                } else {
                    NavManager.startPrivateChatActivity(
                        activity,
                        PrivateChatJumpInfo(wtTargetId, ChatJumpType.WT)
                    )
                }
            }
        }

        if (openVE) {
            VoiceMojiManager.dismissPreviewView()
            // 新版跳去指定的VE tab
            GlobalEventManager.openVEPanel.postValue(true)
        }

        if (openMorePanel) {
            VoiceMojiManager.dismissPreviewView()
            GlobalEventManager.openMorePanel.postValue(true)
        }
        //是否切换到[MyProfileFragment]页引导创建liveplace
        if (createLivePanel) {
            HomePageChangeEvent.post(HomePageEnum.PageUserSetting)
            GlobalEventManager.createLivePlace.postValue(true)
        }

        if (source != -1) {
            HomePageJumpSourceEvent.post(source, wtTargetId, exclusiveId)
        }
    }

    private fun closeTargetDialog() {
        try {
            val list = supportFragmentManager.fragments.toMutableList()
            list.forEach {
                if (it is GroupInfoDialog ||
                    it.javaClass == routerServices<ContactsService>().value?.getProfileDialogFragmentClass()
                ) {
                    supportFragmentManager.beginTransaction().remove(it).commit()
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    // clear router to prevent it from triggering more than one time when activity recreate
    private fun clearRouterValue(intent: Intent, name: String) {
        val nullStr: String? = null
        intent.putExtra(name, nullStr)
    }

    override fun onDestroy() {
        super.onDestroy()
        RecordStatusHelper.reset()
        UpdateVersionManager.onDestroy()
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        logInfo(TAG, "onNewIntent")
        setIntent(intent)
        handleAppRouter()
        handleSelfRouter(intent)
    }


    private fun customOnBackPressed() {
        if (vpHome.currentItem != PageHome.value) {
            currentHomePage = PageHome
            vpHome.setCurrentItem(PageHome.value, true)
            return
        }
        if (WTVoiceEmojiManager.onBackPress()) {
            return
        }
        if (WTVoiceEmojiManager.isHomeVEPanelShowing) {
            WTVoiceEmojiManager.isHomeVEPanelShowing = false
            WTVoiceMojiHidePanelEvent.post()
            return
        }
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastBackTime > 2000) {
            lastBackTime = currentTime
            toast(R.string.tips_back_exit_app)
        } else {
            finishAllActivities()
        }
    }

    private fun afterNextDraw(){
        logInfo(TAG,"afterNextDraw")
        WTStatusManager.requestChangeSwitchStatus(true)
        EarPhoneManager(this@ChatHomeActivity)
        lifecycleScope.launchAsync(Dispatchers.IO) {
            checkUserStatus()
            checkPermissionPost()
            StreamVolumeManager.registerReceiver()
            checkAudioVolume()
            reportUserQuietModeInfo()
            reportRegisterRetainEvent()
            removeOldNotification()
            if (BuildConfig.DEBUG){
                BindIPTagManager.queryCurrentIpTag()
            }
            pushCompensationManager.requestCheckPushCompensation()
            routerServices<IBuzSharedService>().value?.clearRes()
            lazyInitPinyinUtil()
            reportHomeTestAB()
            reportFTUEGuidanceABTest()
            reportFTUEV3AbResult()
            TabNavigationRepository.syncTabNavigationList()
            VoiceEmojiPreloadHelper.preloadVoiceEmoji()
            VoiceEmojiRepository.initVELatestTimestamp()
            initVFLatestTimestamp()
        }
    }

    fun initVFLatestTimestamp() {
        if (CommonMMKV.voiceFilterLatestEntryNotifyTimestamp == 0) {
            CommonMMKV.voiceFilterLatestEntryNotifyTimestamp = AppConfigRequestManager.voiceFilterLatestTimestamp
        }
        if (CommonMMKV.voiceFilterLatestTabNotifyTimestamp == 0) {
            CommonMMKV.voiceFilterLatestTabNotifyTimestamp = AppConfigRequestManager.voiceFilterLatestTimestamp
        }
    }

    private fun checkPermissionPost() {
        CommonTracker.onReportNotificationSettingResult("home")
        CommonTracker.postContactPermissionCheckResult(
            if (isPermissionGranted(Manifest.permission.READ_CONTACTS)) "enable" else "disable"
        )
        CommonTracker.postAudioPermissionCheckResult(
            if (isPermissionGranted(Manifest.permission.RECORD_AUDIO)) "enable" else "disable",
            isUserBehaviour = false
        )
        CommonTracker.postOverlayPermissionResult(FloatPermissionUtils.checkPermission())
        CommonTracker.postRunInBackgroundPermissionResult(isIgnoringBatteryOptimizations())
    }

    private fun isIgnoringBatteryOptimizations(): Boolean {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M) {
            return true
        }
        var isIgnoring = false
        val powerManager = appContext.getSystemService(Context.POWER_SERVICE) as PowerManager?
        if (powerManager != null) {
            isIgnoring = powerManager.isIgnoringBatteryOptimizations(appContext.getPackageName())
        }
        return isIgnoring
    }

    private fun checkAudioVolume() {
        if (isVolumeMuted) {
            toastIconFontMsg(
                message = R.string.chat_low_audio_volume.asString(),
                textColor= R.color.text_white_default.asColor(),
                iconFont = R.string.ic_sound_close.asString(),
                iconFontColor = R.color.text_white_important.asColor(),
                style = IconToastStyle.ICON_TOP_TEXT_BOTTOM
            )
        }
    }

    private fun checkUserStatus(){
        activityViewModel.checkAccountStatus()
    }

    private fun reportUserQuietModeInfo() {
        WTQuietModeManager.reportUserQuietMode(isStartUp = true)
    }

    private fun reportRegisterRetainEvent() {
        if (CommonMMKV.isUserRegister.not()) return
        CommonTracker.reportRegisterRetain()
    }

    override fun obtainTargetID(): Long {
        if (isPaused || isDestroyed) {
            return -1
        }
        return WTStatusManager.wtTargetId ?: -1
    }

    override fun attachCondition(): Boolean {
        if (LoginMainABTestManager.isShowNewHomePagePlanB) {
            val homeFragmentNewPostcard = ARouter.getInstance().build(PATH_CHAT_FRAGMENT_HOME_NEW)
            LogisticsCenter.completion(homeFragmentNewPostcard)
            return supportFragmentManager.fragments.firstOrNull {
                it::class.java == homeFragmentNewPostcard.destination
            }?.isResumed == true
        }
        return supportFragmentManager.fragments.filterIsInstance<ChatHomeFragment>()
            .firstOrNull()?.isResumed == true
    }


    private fun removeOldNotification(){
        if (Build.VERSION.SDK_INT < VERSION_CODES.O) return
        val currentVersion = getVersionCodeFromManifest()
        logInfo(TAG,"removeOldNotification: currentVersion = $currentVersion, lastClearNotificationChannelVersion = ${CommonMMKV.lastClearNotificationChannelVersion}")
        if (currentVersion == CommonMMKV.lastClearNotificationChannelVersion) return
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        CommonConstant.oldChannelId.forEach {
            notificationManager.deleteNotificationChannel(it)
        }
        CommonMMKV.lastClearNotificationChannelVersion = currentVersion
    }

    /**
     * 这个组件的初始化比较耗时，所以放在首页加载完后延时初始化
     * 仅在可能使用中文的国家做预初始化处理
     */
    private fun lazyInitPinyinUtil() {
        GlobalScope.launch(Dispatchers.IO) {
            delay(3000)
            val regionCode = UserSessionManager.regionCode
            if (regionCode.equalsIgnoreCase("CN") ||
                regionCode.equalsIgnoreCase("MY") ||
                regionCode.equalsIgnoreCase("PH") ||
                regionCode.equalsIgnoreCase("ID") ||
                regionCode.equalsIgnoreCase("TH") ||
                regionCode.equalsIgnoreCase("SG")
            ) {
                PinyinUtils.initialization()
            }
        }
    }

    override fun onResume() {
        super.onResume()
        val openFromRegister = source == RouterParamValues.ChatHome.OPEN_FROM_REGISTER
        WTStatusManager.showBlindBoxDialogForNewUser = openFromRegister.not()
        lifecycleScope.launchWhenResumed {
            delay(1000)
            if (isAppInForeground){
                WTStatusManager.requestChangeSwitchStatus(true)
            }
            if (<EMAIL>(State.RESUMED)) {
                if (!WalkieTalkieService.isStartedForeground()) {
                    BuzTracker.onForegroundServiceNotStated(
                        "2",
                        WalkieTalkieService.launchWalkietalkieFlag,
                        WalkieTalkieService.isServiceStartForeground
                    )
                }
            }
        }
    }

    private fun setMinimizedBackground(position: Int) {
        when (position) {
            HomePageEnum.PageHome.value -> setMinimizedViewBgColor(R.color.color_background_2_default.asColor(),R.color.color_overlay_black_medium.asColor())
            else -> setMinimizedViewBgColor(R.color.basic_vanta.asColor(),R.color.basic_vanta.asColor())
        }
    }

    override fun showVoiceCallMinimize(
        view: View,
        width: Int,
        height: Int,
        onEnd: () -> Unit
    ): Boolean {
        val result = super.showVoiceCallMinimize(view, width, height, onEnd)
        lifecycleScope.launch {
            refreshMinimizedBgFlow.emit(Any())
        }
        return result
    }



    // 上报最终的首页AB结果
    private fun reportHomeTestAB() {
        CommonTracker.onResultRB2024091106(
            key = ABTestWithoutLoginManager.TYPE_HOME_PAGE_AB,
            value = if (LoginMainABTestManager.isShowNewHomePagePlanB) "B" else "A"
        )
    }

    private fun reportFTUEGuidanceABTest() {
        if (source == RouterParamValues.ChatHome.OPEN_FROM_REGISTER) {
            CommonTracker.onResultRB2024103101(
                id = ABTestManager.TYPE_FTUE_GUIDANCE,
                isPlanB = CommonMMKV.currentUserIsGuidanceTestB
            )
        }
    }

    private fun reportFTUEV3AbResult() {
        lifecycleScope.launch {
            ABTestManager.subscribe(ABTestManager.TYPE_FTUE_CREATE_GROUP, false) {
                val isPlanB = ABTestManager.isFTUECreateGroupPlanB
                CommonTracker.onResultRB2024091106(
                    key = ABTestManager.TYPE_FTUE_CREATE_GROUP,
                    value = if (isPlanB) "B" else "A"
                )
            }
        }

        lifecycleScope.launch {
            ABTestManager.subscribe(ABTestManager.TYPE_ADD_FRIEND_DIALOG_JAPAN, false) {
                val isPlanB = ABTestManager.isAddFriendDialogJapanExperimental
                CommonTracker.onResultRB2024091106(
                    key = ABTestManager.TYPE_ADD_FRIEND_DIALOG_JAPAN,
                    value = if (isPlanB) "B" else "A"
                )
            }
        }
    }
}