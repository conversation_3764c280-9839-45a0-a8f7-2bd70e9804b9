<?xml version="1.0" encoding="utf-8"?>
<com.interfun.buz.base.widget.round.RoundConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rootLayout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:background="@color/color_background_1_default">

    <androidx.cardview.widget.CardView
        android:id="@+id/startDiv"
        android:layout_width="2dp"
        android:layout_height="0dp"
        android:layout_marginStart="12dp"
        android:layout_marginTop="3dp"
        android:layout_marginBottom="3dp"
        app:cardBackgroundColor="@color/color_text_white_disable"
        app:cardCornerRadius="1dp"
        app:layout_constraintBottom_toBottomOf="@id/tvSubTitle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvTitle" />


    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/tvClose"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginEnd="12dp"
        android:gravity="center"
        android:text="@string/ic_exit"
        android:textColor="@color/color_text_white_tertiary"
        android:textSize="20dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvTitle"
        style="@style/text_body_large"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="10dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/color_text_white_secondary"
        android:visibility="gone"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toStartOf="@+id/brTitleEnd"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toEndOf="@id/startDiv"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="AnnaAnnaAnnaAnnaAnnaAnnaAnnaAnnaAnnaAnnaAnnaAnnaAnnaAnnaAnnaAnnaAnnaAnna"
        tools:visibility="visible" />

    <com.interfun.buz.common.widget.view.AutoDirectionTextView
        android:id="@+id/tvSubTitle"
        style="@style/text_body_medium"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="10dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/color_text_white_tertiary"
        android:visibility="gone"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toStartOf="@id/brTitleEnd"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toEndOf="@id/startDiv"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle"
        tools:text="AnnaAnnaAnnaAnnaAnnaAnnaAnnaAnnaAnnaAnnaAnnaAnnaAnnaAnnaAnnaAnnaAnnaAnna"
        tools:visibility="visible" />

    <View
        android:id="@+id/viewPortraitBg"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:background="@drawable/share_contact_group_portrait_background"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/portraitImageView"
        app:layout_constraintEnd_toEndOf="@+id/portraitImageView"
        app:layout_constraintStart_toStartOf="@+id/portraitImageView"
        app:layout_constraintTop_toTopOf="@+id/portraitImageView" />


    <com.interfun.buz.common.widget.view.PortraitImageView
        android:id="@+id/portraitImageView"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginEnd="10dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/tvClose"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_goneMarginEnd="12dp"
        tools:srcCompat="@drawable/common_user_default_portrait_round" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivPreviewThumbnail"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginEnd="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/tvClose"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_goneMarginEnd="12dp"
        tools:srcCompat="@drawable/home_pic_map" />

    <com.interfun.buz.base.widget.round.RoundView
        android:id="@+id/roundView"
        android:layout_width="18dp"
        android:layout_height="18dp"
        android:layout_marginEnd="-3dp"
        android:layout_marginBottom="-3dp"
        android:background="@color/color_background_1_default"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/portraitImageView"
        app:layout_constraintEnd_toEndOf="@+id/portraitImageView"
        app:round_top_left_radius="4dp" />

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftvContact"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/ic_contacts_solid"
        android:textColor="@color/text_white_important"
        android:textSize="16dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/roundView"
        app:layout_constraintEnd_toEndOf="@+id/roundView"
        app:layout_constraintStart_toStartOf="@+id/roundView"
        app:layout_constraintTop_toTopOf="@+id/roundView" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupContactPortrait"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="iftvContact,viewPortraitBg,roundView,portraitImageView"
        tools:visibility="visible" />

    <com.interfun.buz.common.widget.view.AutoDirectionTextView
        android:id="@+id/tvFilExt"
        style="@style/text_body_medium"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="4dp"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:textColor="@color/color_text_white_primary"
        android:textSize="9sp"
        android:visibility="gone"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="@id/ivPreviewThumbnail"
        app:layout_constraintEnd_toEndOf="@id/ivPreviewThumbnail"
        app:layout_constraintStart_toStartOf="@id/ivPreviewThumbnail"
        app:layout_constraintTop_toTopOf="@id/ivPreviewThumbnail"
        tools:text="PDF" />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/brTitleEnd"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="start"
        app:constraint_referenced_ids="tvClose,ivPreviewThumbnail,clVoiceMoji,SpaceEnd,vgVoiceGif,viewPortraitBg" />

    <androidx.legacy.widget.Space
        android:id="@+id/SpaceEnd"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent" />

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/ivPreviewThumbnailPlay"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:background="@drawable/chat_circle_overlay_medium"
        android:gravity="center"
        android:text="@string/ic_play_solid"
        android:textColor="@color/color_text_white_important"
        android:textSize="12dp"
        app:layout_constraintBottom_toBottomOf="@id/ivPreviewThumbnail"
        app:layout_constraintEnd_toEndOf="@id/ivPreviewThumbnail"
        app:layout_constraintStart_toStartOf="@id/ivPreviewThumbnail"
        app:layout_constraintTop_toTopOf="@id/ivPreviewThumbnail" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clVoiceMoji"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="10dp"
        app:layout_constraintEnd_toStartOf="@id/tvClose"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_goneMarginEnd="12dp"
        tools:visibility="gone">

        <com.interfun.buz.chat.voicemoji.view.widget.VoiceEmojiTextView
            android:id="@+id/ivVoiceMoji"
            android:layout_width="37dp"
            android:layout_height="37dp"
            android:layout_marginEnd="-1dp"
            android:scaleType="centerCrop"
            app:autoSizeTextType="uniform"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintEnd_toStartOf="@id/replyClBubble"
            app:layout_constraintTop_toTopOf="parent"
            app:round_radius="12dp"
            tools:background="@color/white" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/replyClBubble"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/ivVoiceMoji"
            tools:visibility="visible">

            <com.interfun.buz.base.widget.round.RoundView
                android:id="@+id/smallCircle"
                android:layout_width="2.5dp"
                android:layout_height="2.5dp"
                android:background="@color/overlay_grey_26"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:round_radius="2.5dp" />

            <com.interfun.buz.base.widget.round.RoundView
                android:id="@+id/middleCircle"
                android:layout_width="5dp"
                android:layout_height="5dp"
                android:layout_marginStart="-1dp"
                android:background="@color/overlay_grey_26"
                app:layout_constraintBottom_toTopOf="@id/smallCircle"
                app:layout_constraintStart_toEndOf="@id/smallCircle"
                app:round_radius="5dp" />

            <com.interfun.buz.base.widget.round.RoundConstraintLayout
                android:id="@+id/bigCircle"
                android:layout_width="13dp"
                android:layout_height="13dp"
                android:background="@color/overlay_grey_26"
                android:paddingStart="2dp"
                android:paddingEnd="2dp"
                app:layout_constraintBottom_toBottomOf="@id/middleCircle"
                app:layout_constraintStart_toStartOf="@id/middleCircle"
                app:round_radius="20dp">

                <ImageView
                    android:id="@+id/iftvVoiceMojiLogo"
                    android:layout_width="13dp"
                    android:layout_height="13dp"
                    android:gravity="center"
                    android:src="@drawable/common_ic_voice_moji_play"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/voiceMojiName"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <com.interfun.buz.base.widget.view.animContainer.AnimContainerView
                    android:id="@+id/pagPlaying"
                    android:layout_width="13dp"
                    android:layout_height="13dp"
                    android:visibility="invisible"
                    app:layout_constraintBottom_toBottomOf="@id/iftvVoiceMojiLogo"
                    app:layout_constraintEnd_toEndOf="@id/iftvVoiceMojiLogo"
                    app:layout_constraintStart_toStartOf="@id/iftvVoiceMojiLogo"
                    app:layout_constraintTop_toTopOf="@id/iftvVoiceMojiLogo" />

                <TextView
                    android:id="@+id/voiceMojiName"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="3dp"
                    android:textColor="@color/text_white_secondary"
                    android:textSize="10dp"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="@id/iftvVoiceMojiLogo"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/iftvVoiceMojiLogo"
                    app:layout_constraintTop_toTopOf="@id/iftvVoiceMojiLogo"
                    tools:text="Drum2" />

            </com.interfun.buz.base.widget.round.RoundConstraintLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/vgVoiceGif"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="10dp"
        app:layout_constraintEnd_toStartOf="@id/tvClose"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_goneMarginEnd="12dp"
        tools:visibility="gone">

        <com.interfun.buz.base.widget.round.RoundFrameLayout
            android:id="@+id/rfVoiceGif"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="1dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:round_bottom_end_radius="12dp"
            app:round_bottom_start_radius="12dp"
            app:round_top_end_radius="12dp"
            app:round_top_start_radius="12dp">

            <com.interfun.buz.common.widget.view.VoiceGifWebpAnimView
                android:id="@+id/avVoiceGif"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:scaleType="centerCrop" />
        </com.interfun.buz.base.widget.round.RoundFrameLayout>

        <com.interfun.buz.base.widget.round.RoundView
            android:id="@+id/middleCircleVG"
            android:layout_width="6dp"
            android:layout_height="6dp"
            android:background="@drawable/chat_circle_overlay_grey_26"
            app:layout_constraintBottom_toBottomOf="@id/bigCircleVG"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintStart_toStartOf="@id/bigCircleVG" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/bigCircleVG"
            android:layout_width="15dp"
            android:layout_height="15dp"
            android:layout_marginStart="2dp"
            android:background="@drawable/chat_circle_overlay_grey_26"
            app:layout_constraintStart_toEndOf="@id/rfVoiceGif"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/iftvVoiceGifLogo"
                android:layout_width="12dp"
                android:layout_height="12dp"
                android:src="@drawable/chat_ic_ve_unread"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1:1"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>


</com.interfun.buz.base.widget.round.RoundConstraintLayout>