<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/color_background_5_default"
    android:padding="12dp"
    tools:layout_width="match_parent">

    <com.interfun.buz.base.widget.round.RoundImageView
        android:id="@+id/ivContactPortrait"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:background="@drawable/share_contact_group_portrait_background"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:round_radius="24dp"
        tools:src="@drawable/common_user_default_portrait_round"
        tools:visibility="visible" />

    <com.interfun.buz.base.widget.round.RoundView
        android:id="@+id/roundView"
        android:layout_width="18dp"
        android:layout_height="18dp"
        android:layout_marginEnd="-3dp"
        android:layout_marginBottom="-3dp"
        android:background="@color/color_background_5_default"
        app:layout_constraintBottom_toBottomOf="@+id/ivContactPortrait"
        app:layout_constraintEnd_toEndOf="@+id/ivContactPortrait"
        app:round_top_left_radius="4dp" />

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftvContact"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/ic_contacts_solid"
        android:textColor="@color/text_white_important"
        android:textSize="16dp"
        app:layout_constraintBottom_toBottomOf="@+id/roundView"
        app:layout_constraintEnd_toEndOf="@+id/roundView"
        app:layout_constraintStart_toStartOf="@+id/roundView"
        app:layout_constraintTop_toTopOf="@+id/roundView" />

    <FrameLayout
        android:id="@+id/flLog"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        app:layout_constraintBottom_toBottomOf="@id/tvTitle"
        app:layout_constraintStart_toEndOf="@+id/ivContactPortrait"
        app:layout_constraintTop_toTopOf="@id/tvTitle">

        <ImageView
            android:id="@+id/ivAiIcon"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:layout_marginEnd="2dp"
            android:visibility="gone"
            tools:src="@drawable/common_icon_ai_flag" />


        <com.interfun.buz.common.widget.view.IconFontTextView
            android:id="@+id/iftvOfficialTag"
            style="@style/iconfont_base"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="2dp"
            android:text="@string/ic_official"
            android:textColor="@color/basic_primary"
            android:textSize="18dp"
            android:visibility="gone" />
    </FrameLayout>


    <TextView
        android:id="@+id/tvTitle"
        style="@style/text_label_large"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="12dp"
        android:ellipsize="end"
        android:gravity="start"
        android:singleLine="true"
        android:textColor="@color/text_white_important"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toTopOf="@+id/tvDesc"
        app:layout_constraintEnd_toStartOf="@+id/iftvArrow"
        app:layout_constraintStart_toEndOf="@+id/flLog"
        app:layout_constraintTop_toTopOf="@+id/ivContactPortrait"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="Buz TeamTeamTeamTeamTeamTeamTeam" />

    <TextView
        android:id="@+id/tvDesc"
        style="@style/text_body_medium"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginTop="2dp"
        android:layout_marginEnd="12dp"
        android:ellipsize="end"
        android:gravity="start"
        android:singleLine="true"
        android:textColor="@color/text_white_secondary"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="@+id/ivContactPortrait"
        app:layout_constraintEnd_toStartOf="@+id/iftvArrow"
        app:layout_constraintStart_toEndOf="@+id/ivContactPortrait"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle"
        app:layout_goneMarginStart="12dp"
        tools:text="\@marcel123点进来看飞机上看得见风景的深刻理解法律监督苏里科夫" />

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftvArrow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/ic_arrow_right"
        android:textColor="@color/color_text_white_tertiary"
        android:textSize="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>