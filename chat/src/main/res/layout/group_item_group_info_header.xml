<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/clRoot"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="20dp"
    tools:background="@color/color_background_2_default">

    <androidx.compose.ui.platform.ComposeView
        android:id="@+id/cvLivePlaceEntrance"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_height="30dp" />

    <View
        android:id="@+id/viewPortraitBg"
        android:layout_width="116dp"
        android:layout_height="116dp"
        android:layout_marginTop="-24dp"
        android:background="@drawable/common_oval_background_2_default"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/cvLivePlaceEntrance" />

    <com.interfun.buz.common.widget.view.PortraitImageView
        android:id="@+id/ivPortrait"
        android:layout_width="100dp"
        android:layout_height="100dp"
        app:layout_constraintBottom_toBottomOf="@+id/viewPortraitBg"
        app:layout_constraintEnd_toEndOf="@+id/viewPortraitBg"
        app:layout_constraintStart_toStartOf="@+id/viewPortraitBg"
        app:layout_constraintTop_toTopOf="@+id/viewPortraitBg"
        tools:background="@tools:sample/avatars" />

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftvMuteMessage"
        style="@style/iconfont_base"
        android:layout_width="100dp"
        android:layout_height="100dp"
        android:background="@drawable/common_oval_overlay_mask_black_light"
        android:text="@string/ic_sound_close"
        android:textColor="@color/color_text_white_important"
        android:textSize="36dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/ivPortrait"
        app:layout_constraintEnd_toEndOf="@+id/ivPortrait"
        app:layout_constraintStart_toStartOf="@+id/ivPortrait"
        app:layout_constraintTop_toTopOf="@+id/ivPortrait"
        tools:visibility="visible" />

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftvShareQRCode"
        style="@style/iconfont_24"
        android:text="@string/ic_qrcode"
        android:layout_marginEnd="4dp"
        android:textColor="@color/text_white_main"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/ivPortrait"
        app:layout_constraintBottom_toBottomOf="@+id/ivPortrait"/>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clGroupInfo"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="20dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/viewPortraitBg">

        <TextView
            android:id="@+id/tvGroupName"
            style="@style/text_title_large"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:includeFontPadding="false"
            android:textColor="@color/color_text_white_important"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Anna Wilson" />

        <TextView
            android:id="@+id/tvMemberDesc"
            style="@style/text_body_medium"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:gravity="center"
            android:singleLine="true"
            android:textColor="@color/color_text_white_secondary"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvGroupName"
            tools:text="Buz001" />



        <LinearLayout
            android:id="@+id/llMuteNotification"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:background="@drawable/chat_rect_overlay_background_5_default_radius_30"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingHorizontal="8dp"
            android:paddingVertical="4dp"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvMemberDesc"
            tools:visibility="visible">

            <com.interfun.buz.common.widget.view.IconFontTextView
                android:id="@+id/iftvMuteNotification"
                style="@style/iconfont_14"
                android:text="@string/ic_ring_off"
                android:textColor="@color/color_text_white_primary" />

            <TextView
                android:id="@+id/tvMuteNotification"
                style="@style/text_body_medium"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:text="@string/notification"
                android:textColor="@color/color_text_white_secondary" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/btnStartBuz"
        android:layout_width="0dp"
        android:layout_height="72dp"
        android:layout_marginStart="20dp"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="8dp"
        app:layout_goneMarginEnd="20dp"
        android:background="@drawable/common_rect_button_secondary_radius_16"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/btnGroupVoiceCall"
        app:layout_constraintTop_toBottomOf="@+id/clGroupInfo"
        app:layout_constraintHorizontal_chainStyle="packed"
        tools:visibility="visible">

        <com.interfun.buz.common.widget.view.IconFontTextView
            android:id="@+id/iconBtnStart"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:text="@string/ic_voice"
            android:textColor="@color/basic_primary"
            android:textSize="24sp"
            app:layout_constraintBottom_toTopOf="@id/tvBtnStartText"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed" />

        <TextView
            android:id="@+id/tvBtnStartText"
            style="@style/text_label_small"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:maxLines="2"
            android:gravity="center"
            android:text="@string/profile_send_message"
            android:textColor="@color/text_white_main"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/iconBtnStart" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <ViewStub
        android:id="@+id/vsBigSendMessageBtn"
        android:layout="@layout/profile_big_send_message_btn"
        android:layout_width="0dp"
        android:layout_height="56dp"
        android:layout_marginTop="30dp"
        android:layout_marginHorizontal="20dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/clGroupInfo"/>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/btnGroupVoiceCall"
        android:layout_width="0dp"
        android:layout_height="72dp"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="8dp"
        android:background="@drawable/common_rect_button_secondary_radius_16"
        android:visibility="gone"
        app:layout_goneMarginEnd="20dp"
        app:layout_constraintEnd_toStartOf="@+id/btnGroupVideoCall"
        app:layout_constraintStart_toEndOf="@+id/btnStartBuz"
        app:layout_constraintTop_toBottomOf="@id/clGroupInfo"
        tools:visibility="visible">

        <com.interfun.buz.common.widget.view.IconFontTextView
            android:id="@+id/iconBtnVoiceCall"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:text="@string/ic_tel"
            android:textColor="@color/basic_primary"
            android:textSize="24sp"
            app:layout_constraintBottom_toTopOf="@id/tvBtnVoiceCall"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed" />

        <TextView
            android:id="@+id/tvBtnVoiceCall"
            style="@style/caption"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:includeFontPadding="false"
            android:text="@string/profile_btn_voice_call"
            android:maxLines="2"
            android:ellipsize="end"
            android:textColor="@color/text_white_main"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/iconBtnVoiceCall" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/btnGroupVideoCall"
        android:layout_width="0dp"
        android:layout_height="72dp"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="20dp"
        android:background="@drawable/common_rect_button_secondary_radius_16"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/btnGroupVoiceCall"
        app:layout_constraintTop_toBottomOf="@id/clGroupInfo"
        app:layout_goneMarginEnd="20dp"
        tools:visibility="visible">

        <com.interfun.buz.common.widget.view.IconFontTextView
            android:id="@+id/iconBtnVideoCall"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:text="@string/ic_video"
            android:textColor="@color/basic_primary"
            android:textSize="24sp"
            app:layout_constraintBottom_toTopOf="@id/tvBtnVideoCall"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed" />

        <TextView
            android:id="@+id/tvBtnVideoCall"
            style="@style/caption"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:includeFontPadding="false"
            android:text="@string/rtc_videocall"
            android:maxLines="2"
            android:ellipsize="end"
            android:textColor="@color/text_white_main"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:gravity="center"
            app:layout_constraintTop_toBottomOf="@+id/iconBtnVideoCall" />
    </androidx.constraintlayout.widget.ConstraintLayout>


    <com.interfun.buz.common.widget.button.CommonButton
        android:id="@+id/btnJoinGroup"
        android:layout_width="0dp"
        android:layout_height="56dp"
        android:layout_marginHorizontal="20dp"
        android:layout_marginTop="30dp"
        android:visibility="gone"
        app:iconFont="@string/ic_group"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/clGroupInfo"
        app:text="@string/chat_join_group"
        app:type="primary_larger" />
    <TextView
        android:id="@+id/tvInviterDesc"
        style="@style/text_body_medium"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:gravity="center"
        android:singleLine="true"
        android:text="@string/profile_name_not_on_contacts"
        android:textColor="@color/color_text_white_primary"
        android:visibility="gone"
        app:layout_constrainedWidth="true"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/btnJoinGroup"
        tools:visibility="visible" />
    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/barrierBottom"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="bottom"
        app:constraint_referenced_ids="btnStartBuz, vsBigSendMessageBtn" />

    <View
        android:id="@+id/viewEditAndCreateBg"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp"
        android:background="@drawable/common_rect_background_4_default_radius_16"
        app:layout_constraintBottom_toBottomOf="@+id/flShareContact"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/flEdit"
        app:layout_goneMarginTop="20dp" />

    <FrameLayout
        android:id="@+id/flEdit"
        android:layout_width="0dp"
        android:layout_height="48dp"
        android:layout_marginHorizontal="20dp"
        android:layout_marginTop="20dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/barrierBottom">

        <TextView
            android:id="@+id/tvEdit"
            style="@style/text_label_large"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:gravity="start|center_vertical"
            android:paddingHorizontal="20dp"
            android:singleLine="true"
            android:text="@string/edit_group_name"
            android:textAlignment="viewStart"
            android:textColor="@color/color_text_white_primary" />

        <com.interfun.buz.common.widget.view.IconFontTextView
            android:id="@+id/iftvArrow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical|end"
            android:layout_marginEnd="20dp"
            android:text="@string/ic_arrow_right"
            android:textColor="@color/text_white_secondary"
            android:textSize="16sp"
            app:autoRTL="true" />
    </FrameLayout>

    <View
        android:id="@+id/viewShareContactLine"
        android:layout_width="0dp"
        android:layout_height="0.4dp"
        android:layout_marginStart="20dp"
        android:background="@color/color_foreground_neutral_important_disable"
        app:layout_constraintEnd_toEndOf="@+id/viewEditAndCreateBg"
        app:layout_constraintStart_toStartOf="@+id/viewEditAndCreateBg"
        app:layout_constraintTop_toTopOf="@+id/flShareContact" />

    <FrameLayout
        android:id="@+id/flShareContact"
        android:layout_width="0dp"
        android:layout_height="48dp"
        android:layout_marginHorizontal="20dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/flEdit"
        app:layout_goneMarginTop="20dp">


        <TextView
            android:id="@+id/tvShareContact"
            style="@style/text_label_large"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:gravity="start|center_vertical"
            android:paddingHorizontal="20dp"
            android:singleLine="true"
            android:text="@string/contact_card_group_share"
            android:textAlignment="viewStart"
            android:textColor="@color/color_text_white_primary" />

        <com.interfun.buz.common.widget.view.IconFontTextView
            android:id="@+id/iftvShareContactArrow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical|end"
            android:layout_marginEnd="20dp"
            android:text="@string/ic_arrow_right"
            android:textColor="@color/text_white_secondary"
            android:textSize="16sp"
            app:autoRTL="true" />
    </FrameLayout>

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupShareContact"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="viewShareContactLine,flShareContact"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>