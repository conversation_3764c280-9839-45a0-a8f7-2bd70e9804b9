package com.interfun.buz.di;

import android.content.Context;
import androidx.datastore.core.DataStore;
import androidx.datastore.preferences.core.Preferences;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata({
    "com.interfun.buz.di.MediaPlayQualifier",
    "dagger.hilt.android.qualifiers.ApplicationContext"
})
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class MediaPlayModule_Companion_ProvideMediaPlayDeviceDatastoreFactory implements Factory<DataStore<Preferences>> {
  private final Provider<Context> appContextProvider;

  public MediaPlayModule_Companion_ProvideMediaPlayDeviceDatastoreFactory(
      Provider<Context> appContextProvider) {
    this.appContextProvider = appContextProvider;
  }

  @Override
  public DataStore<Preferences> get() {
    return provideMediaPlayDeviceDatastore(appContextProvider.get());
  }

  public static MediaPlayModule_Companion_ProvideMediaPlayDeviceDatastoreFactory create(
      Provider<Context> appContextProvider) {
    return new MediaPlayModule_Companion_ProvideMediaPlayDeviceDatastoreFactory(appContextProvider);
  }

  public static DataStore<Preferences> provideMediaPlayDeviceDatastore(Context appContext) {
    return Preconditions.checkNotNullFromProvides(MediaPlayModule.Companion.provideMediaPlayDeviceDatastore(appContext));
  }
}
