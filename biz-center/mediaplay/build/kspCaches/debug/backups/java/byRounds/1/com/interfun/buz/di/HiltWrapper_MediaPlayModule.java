package com.interfun.buz.di;

import dagger.Module;
import dagger.hilt.InstallIn;
import dagger.hilt.codegen.OriginatingElement;
import dagger.hilt.components.SingletonComponent;
import javax.annotation.processing.Generated;

@OriginatingElement(
    topLevelClass = MediaPlayModule.class
)
@InstallIn(SingletonComponent.class)
@Module(
    includes = MediaPlayModule.class
)
@Generated("dagger.hilt.processor.internal.aggregateddeps.PkgPrivateModuleGenerator")
public final class HiltWrapper_MediaPlayModule {
}
