package com.interfun.buz.di;

import android.app.Application;
import androidx.datastore.core.DataStore;
import androidx.datastore.preferences.core.Preferences;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;
import kotlinx.coroutines.CoroutineScope;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata({
    "com.interfun.buz.di.MediaPlayQualifier",
    "com.interfun.buz.component.hilt.GlobalQualifier"
})
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class MediaPlayVolumeRepository_Factory implements Factory<MediaPlayVolumeRepository> {
  private final Provider<DataStore<Preferences>> dataStoreProvider;

  private final Provider<CoroutineScope> globalScopeProvider;

  private final Provider<Application> appContextProvider;

  public MediaPlayVolumeRepository_Factory(Provider<DataStore<Preferences>> dataStoreProvider,
      Provider<CoroutineScope> globalScopeProvider, Provider<Application> appContextProvider) {
    this.dataStoreProvider = dataStoreProvider;
    this.globalScopeProvider = globalScopeProvider;
    this.appContextProvider = appContextProvider;
  }

  @Override
  public MediaPlayVolumeRepository get() {
    return newInstance(dataStoreProvider.get(), globalScopeProvider.get(), appContextProvider.get());
  }

  public static MediaPlayVolumeRepository_Factory create(
      Provider<DataStore<Preferences>> dataStoreProvider,
      Provider<CoroutineScope> globalScopeProvider, Provider<Application> appContextProvider) {
    return new MediaPlayVolumeRepository_Factory(dataStoreProvider, globalScopeProvider, appContextProvider);
  }

  public static MediaPlayVolumeRepository newInstance(DataStore<Preferences> dataStore,
      CoroutineScope globalScope, Application appContext) {
    return new MediaPlayVolumeRepository(dataStore, globalScope, appContext);
  }
}
