package com.interfun.buz.social.datasource

import androidx.collection.LruCache
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import com.interfun.buz.base.coroutine.withReentrantLock
import com.interfun.buz.base.ktx.and
import com.interfun.buz.base.ktx.awaitInScopeIO
import com.interfun.buz.component.hilt.UserQualifier
import com.interfun.buz.social.db.dao.UserDao
import com.interfun.buz.social.db.entity.BuzUser
import com.interfun.buz.social.db.entity.BuzUserComposite
import com.interfun.buz.social.db.entity.BuzUserRelationEntity
import com.interfun.buz.social.db.entity.UserUnUploadedSetting
import com.interfun.buz.social.di.SocialUserQualifier
import com.interfun.buz.social.storage.SocialDataStoreKey
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.sync.Mutex
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject

@OptIn(ExperimentalCoroutinesApi::class)
internal class UserLocalDataSourceImpl @Inject constructor(
    @UserQualifier val userDao: UserDao,
    @UserQualifier val userScope: CoroutineScope,
    @SocialUserQualifier private val userDataStore: DataStore<Preferences>,
) : UserLocalDataSource {
    private val buzUserLruCache = LruCache<Long, BuzUser>(3000)
    //todo @Vincent 考虑改成有变更才发送
    private val buzUserUpdateFlow = MutableSharedFlow<Set<Long>>()
    private val buzUserRelationLruCache = LruCache<Long, BuzUserRelationEntity>(3000)
    private val buzUserRelationUpdateFlow = MutableSharedFlow<Set<Long>>()

    @Volatile
    private var isAllUnUploadedSettingCacheInit = false
    private val buzUserUnUploadedSettingCache = ConcurrentHashMap<Long, UserUnUploadedSetting>()

    //数据库跟内存的更新是非原子性的，所以需要加锁将两者绑起来，保证数据库与内存数据的一致性
    private val userMutex = Mutex()
    private val unUploadedSettingMutex = Mutex()

    override suspend fun getUser(userId: Long): BuzUser? {
        return buzUserLruCache[userId] ?: queryUserFromDb(userId)
    }

    @Deprecated("不要使用这个，请使用getUser")
    override fun getUserUnSuspend(userId: Long): BuzUser? {
        return buzUserLruCache[userId] ?: queryUserFromDBUnSuspend(userId)
    }

    /**
     * @return 返回的数据跟查询的对应index，如果查不到，则对应的位置为null
     */
    override suspend fun getUser(userIds: List<Long>): List<BuzUser?> {
        if (userIds.isEmpty()) {
            return emptyList()
        }
        if (userIds.size == 1) {
            return listOf(getUser(userIds[0]))
        }
        //尽量复用缓存，没有缓存的在批量从数据库查，提升性能
        val memoryCacheList = arrayOfNulls<BuzUser>(userIds.size)
        //这个map的作用是为了保证返回的数据跟查询的用户顺序对应
        var notCachedMap: MutableMap<Long, Int>? = null
        userIds.forEachIndexed { index, id ->
            val user = buzUserLruCache[id]
            memoryCacheList[index] = user
            if (user == null) {
                if (notCachedMap == null) {
                    notCachedMap = HashMap()
                }
                notCachedMap!!.put(id, index)
            }
        }
        val map = notCachedMap
        if (map != null) {
            val notCachedList = map.keys.toList()
            queryUserFromDb(notCachedList)?.forEach {
                val index = map[it.userId]
                memoryCacheList[index!!] = it
            }
        }
        return memoryCacheList.toList()
    }

    override fun getUserFlow(userId: Long): Flow<BuzUser?> =
        buzUserUpdateFlow.onSubscription { emit(hashSetOf(userId)) }
            .filter { it.contains(userId) }
            .mapLatest { getUser(userId) }
            .distinctUntilChanged()
            .flowOn(Dispatchers.Default)

    override fun getUserFlow(userIds: List<Long>): Flow<Map<Long, BuzUser>> = channelFlow {
        val userMap = HashMap<Long, BuzUser>(userIds.size)
        val userIdSet = userIds.toSet()
        val updateFlow = buzUserUpdateFlow.mapNotNull { updateSet ->
            val intersectionSet = updateSet and userIdSet
            intersectionSet.ifEmpty {
                null
            }
        }.shareIn(this, SharingStarted.WhileSubscribed(5000))
        updateFlow.onSubscription {
            emit(userIdSet)
        }.collect { updatedData ->
            val userList = getUser(updatedData.toList())
            userList.forEach {
                if (it != null) {
                    userMap[it.userId] = it
                }
            }
            send(userMap.toMap())
        }
    }

    override suspend fun saveUser(user: BuzUser): BuzUser {
        return saveUser(listOf(user))[0]
    }

    override suspend fun markUserAccountDeleted(userId: Long): BuzUser? {
        return awaitInScopeIO(userScope) {
            userMutex.withReentrantLock {
                val newUser = userDao.markUserAccountDeleted(userId)
                if (newUser != null) {
                    buzUserLruCache.put(userId, newUser)
                }
                newUser
            }?.also {
                buzUserUpdateFlow.emit(hashSetOf(userId))
            }
        }
    }

    override suspend fun saveUser(user: List<BuzUser>): List<BuzUser> {
        return awaitInScopeIO(userScope) {
            userMutex.withReentrantLock {
                userDao.insertUser(user)
                user.forEach {
                    buzUserLruCache.put(it.userId, it)
                }
            }
            buzUserUpdateFlow.emit(user.mapTo(hashSetOf()) { it.userId })
            user
        }
    }

    override suspend fun getUserRelation(userId: Long): BuzUserRelationEntity? {
        return buzUserRelationLruCache[userId] ?: queryUserRelationFromDb(userId)
    }

    override suspend fun updateRelation(userId: Long, userRelation: Int): BuzUserRelationEntity? {
        return awaitInScopeIO(userScope) {
            userMutex.withReentrantLock {
                userDao.updateOrInsertUserRelation(userId, userRelation)
                queryUserRelationFromDb(userId)
            }.apply {
                buzUserRelationUpdateFlow.emit(hashSetOf(userId))
            }
        }
    }

    override suspend fun updateRelation(userRelationList: List<Pair<Long, Int>>): List<BuzUserRelationEntity>? {
        return awaitInScopeIO(userScope) {
            userMutex.withReentrantLock {
                userDao.updateOrInsertUserRelation(userRelationList)
                val userIdList = userRelationList.map { it.first }
                queryUserRelationFromDb(userIdList)
            }.apply {
                buzUserRelationUpdateFlow.emit(userRelationList.mapTo(HashSet()) { it.first })
            }
        }
    }

    override suspend fun replaceAllBlockList(newBlockList : List<Long>) {
        awaitInScopeIO(userScope) {
            userMutex.withReentrantLock {
                val changedList = userDao.replaceAllBlockList(newBlockList)
                //update memory cache
                queryUserRelationFromDb(changedList)
            }?.apply {
                buzUserRelationUpdateFlow.emit(this.mapTo(HashSet()) { it.userId })
            }
        }
    }

    /**
     * @return 返回的数据跟查询的对应index，如果查不到，则对应的位置为null
     */
    override suspend fun getUserRelation(userIds: List<Long>): List<BuzUserRelationEntity?> {
        if (userIds.isEmpty()) {
            return emptyList()
        }
        if (userIds.size == 1) {
            return listOf(getUserRelation(userIds[0]))
        }
        //尽量复用缓存，没有缓存的在批量从数据库查，提升性能
        val memoryCacheList = arrayOfNulls<BuzUserRelationEntity>(userIds.size)
        //这个map的作用是为了保证返回的数据跟查询的用户顺序对应
        var notCachedMap: MutableMap<Long, Int>? = null
        userIds.forEachIndexed { index, id ->
            val userRelation = buzUserRelationLruCache[id]
            memoryCacheList[index] = userRelation
            if (userRelation == null) {
                if (notCachedMap == null) {
                    notCachedMap = HashMap()
                }
                notCachedMap!!.put(id, index)
            }
        }
        val map = notCachedMap
        if (map != null) {
            val notCachedList = map.keys.toList()
            queryUserRelationFromDb(notCachedList)?.forEach {
                val index = map[it.userId]
                memoryCacheList[index!!] = it
            }
        }
        return memoryCacheList.toList()
    }

    @Deprecated("为了兼容旧代码，新代码别用")
    override fun getUserRelationNotSuspend(userId: Long): BuzUserRelationEntity? {
        return buzUserRelationLruCache[userId] ?: queryUserRelationFromDbNotSuspend(userId)
    }

    override suspend fun getFriendsSize(): Int {
        return awaitInScopeIO(userScope) {
            userDao.getFriendSize()
        }
    }

    override suspend fun getAllFriends(): List<BuzUserComposite> {
        return awaitInScopeIO(userScope) {
            userDao.getAllFriends().mapNotNull {
                it.toBuzUserComposite()
            }
        }
    }

    override suspend fun getAllFriendIds(): List<Long> {
        return awaitInScopeIO(userScope) {
            userDao.getAllFriendsIds()
        }
    }

    override fun getAllFriendIdsFlow(): Flow<List<Long>> {
        return userDao.getAllFriendIdsFlow()
    }

    override fun getBlockedUserIdsFlow(): Flow<List<Long>> {
        return userDao.getBlockUserIdsFromCacheFlow()
    }

    override suspend fun getAllOfficialFriends(): List<BuzUserComposite> {
        return awaitInScopeIO(userScope) {
            userDao.getAllOfficialFriends()
        }
    }

    override suspend fun getLastRequestFriendTimestamp(): Long {
        return userDataStore.data.first()[SocialDataStoreKey.lastGetFriendListTimeStamp] ?: 0
    }

    override suspend fun updateLastRequestFriendTimestamp(timestamp: Long) {
        userDataStore.edit {
            it[SocialDataStoreKey.lastGetFriendListTimeStamp] = timestamp
        }
    }

    @Deprecated("为了兼容旧代码，新代码别用")
    override fun getAllFriendsNotSuspend(): List<BuzUserComposite> {
        //为了兼容，没有上锁，可能会导致缓存不一致问题
        return userDao.getAllFriends().mapNotNull {
            it.toBuzUserComposite()
        }
    }

    override fun getUserRelationFlow(userId: Long): Flow<BuzUserRelationEntity?> =
        buzUserRelationUpdateFlow.onSubscription { emit(hashSetOf(userId)) }
            .filter { it.contains(userId) }
            .mapLatest { getUserRelation(userId) }
            .distinctUntilChanged()
            .flowOn(Dispatchers.Default)

    override fun getUserRelationFlow(userIds: List<Long>): Flow<Map<Long, BuzUserRelationEntity>> =
        channelFlow {
            val relationMap = HashMap<Long, BuzUserRelationEntity>(userIds.size)
            val userIdSet = userIds.toSet()
            val updateFlow = buzUserRelationUpdateFlow.mapNotNull {
                val intersectionSet = it and userIdSet
                intersectionSet.ifEmpty {
                    null
                }
            }.shareIn(this, SharingStarted.WhileSubscribed())
            updateFlow.onSubscription {
                emit(userIdSet)
            }.collect { updatedSet ->
                val uidList = updatedSet.toList()
                for (uid in uidList) {
                    val relation = getUserRelation(uid)
                    if (relation != null) {
                        relationMap[uid] = relation
                    }
                }
                send(relationMap.toMap())
            }
        }.flowOn(Dispatchers.Default)

    override suspend fun saveUserRelation(relationInfo: BuzUserRelationEntity): BuzUserRelationEntity {
        return saveUserRelation(listOf(relationInfo))[0]
    }

    override suspend fun saveUserRelation(relationInfoList: List<BuzUserRelationEntity>): List<BuzUserRelationEntity> {
        return awaitInScopeIO(userScope) {
            userMutex.withReentrantLock {
                userDao.insertUserRelation(relationInfoList)
                relationInfoList.forEach {
                    buzUserRelationLruCache.put(it.userId, it)
                }
                relationInfoList
            }.apply {
                buzUserRelationUpdateFlow.emit(relationInfoList.mapTo(hashSetOf()) { it.userId })
            }
        }
    }

    /**
     * 之所以不分开掉用saveUserRelation和saveUser，是因为会导致多次触发Composite刷新
     * 合并的话即使会触发两次更新，但是前后的数据都是一样的，最后Composite的flow只会刷新一次
     */
    override suspend fun saveUserComposite(
        userComposite: BuzUserComposite
    ): BuzUserComposite {
        return awaitInScopeIO(userScope) {
            userMutex.withReentrantLock {
                userDao.insertUserAndRelation(userComposite.user, userComposite.relationInfo)
                buzUserLruCache.put(userComposite.user.userId, userComposite.user)
                userComposite.relationInfo?.let {
                    buzUserRelationLruCache.put(it.userId, it)
                }
                userComposite
            }.apply {
                buzUserUpdateFlow.emit(hashSetOf(userComposite.user.userId))
                userComposite.relationInfo?.let {
                    buzUserRelationUpdateFlow.emit(hashSetOf(it.userId))
                }
            }
        }
    }

    override suspend fun saveUserComposite(userComposites: List<BuzUserComposite>): List<BuzUserComposite> {
        return awaitInScopeIO(userScope) {
            userMutex.withReentrantLock {
                userDao.insertUserAndRelation(
                    userComposites.map { it.user },
                    userComposites.mapNotNull { it.relationInfo })
                userComposites.forEach {
                    buzUserLruCache.put(it.user.userId, it.user)
                    it.relationInfo?.let { buzUserRelationLruCache.put(it.userId, it) }
                }
            }
            val userSet = userComposites.mapTo(hashSetOf()) { it.user.userId }
            buzUserUpdateFlow.emit(userSet)
            buzUserRelationUpdateFlow.emit(userSet)
            userComposites
        }
    }

    override suspend fun getUserComposite(userId: Long): BuzUserComposite? {
        val user = getUser(userId)
        val relation = getUserRelation(userId)
        return if (user != null) {
            BuzUserComposite(user, relation)
        } else {
            null
        }
    }

    override suspend fun getUserComposite(userIds: List<Long>): List<BuzUserComposite?> {
        val buzUser = getUser(userIds)
        val buzUserRelation = getUserRelation(userIds)
        return buzUser.mapIndexed { index, user ->
            if (user != null) {
                BuzUserComposite(user, buzUserRelation[index])
            } else {
                null
            }
        }
    }

    @Deprecated("为了兼容旧代码，新代码别用")
    override fun getUserCompositeNotSuspend(userId: Long): BuzUserComposite? {
        val user = getUserUnSuspend(userId)
        val relation = getUserRelationNotSuspend(userId)
        return if (user != null) {
            BuzUserComposite(user, relation)
        } else {
            null
        }
    }

    @Deprecated("为了兼容旧代码，新代码别用")
    override fun getUserCompositeFromMemory(userId: Long): BuzUserComposite? {
        val user = buzUserLruCache[userId]
        val relation = buzUserRelationLruCache[userId]
        return if (user != null) {
            BuzUserComposite(user, relation)
        } else {
            null
        }
    }

    override suspend fun updateUserRelationSettings(
        userId: Long,
        muteMessages: Int?,
        muteNotification: Int?,
        remark: String?
    ): BuzUserRelationEntity? {
        return awaitInScopeIO(userScope) {
            userMutex.withReentrantLock {
                userDao.updateUserRelationSettings(
                    userId,
                    muteMessages,
                    muteNotification,
                    remark
                )?.also { userRelationEntity ->
                    buzUserRelationLruCache.put(userId, userRelationEntity)
                }
            }?.also { userRelationEntity ->
                buzUserRelationUpdateFlow.emit(hashSetOf(userRelationEntity.userId))
            }
        }
    }

    private suspend fun ensureLoadAllUploadedSetting() {
        if (isAllUnUploadedSettingCacheInit) {
            return
        }
        awaitInScopeIO(userScope) {
            unUploadedSettingMutex.withReentrantLock {
                val settings = userDao.loadAllUnLoadedSettings()
                settings.forEach {
                    buzUserUnUploadedSettingCache[it.userId] = it
                }
                isAllUnUploadedSettingCacheInit = true
            }
        }
    }

    override suspend fun insertOrUpdateUnUploadedUserRelationSettings(
        userId: Long,
        muteMessages: Int?,
        muteNotification: Int?,
        remark: String?
    ) {
        ensureLoadAllUploadedSetting()
        unUploadedSettingMutex.withReentrantLock {
            val newUnUploadedSetting = userDao.insertOrUpdateUnUploadedUserRelationSettings(
                userId,
                muteMessages,
                muteNotification,
                remark
            )
            newUnUploadedSetting?.let {
                buzUserUnUploadedSettingCache[userId] = newUnUploadedSetting
            }
        }
    }

    override suspend fun deleteUserUnUploadedSettingsIfMatched(
        userId: Long,
        updatedMuteMessages: Int?,
        updatedMuteNotification: Int?,
        updatedRemark: String?
    ) {
        unUploadedSettingMutex.withReentrantLock {
            val unUploadedSetting = getUserUnUploadedSettings(userId) ?: return@withReentrantLock
            val isRemakeChanged = unUploadedSetting.remark != updatedRemark
            val isMuteMessagesChanged = unUploadedSetting.muteMessages != updatedMuteMessages
            val isMuteNotificationChanged =
                unUploadedSetting.muteNotification != updatedMuteNotification
            if (!isRemakeChanged && !isMuteMessagesChanged && !isMuteNotificationChanged) {
                //向服务端更新的内容跟本地的内容一致，说明服务端请求过程中本地没有更新，可以删除本地记录
                userDao.deleteUserUnUploadedSetting(userId)
                buzUserUnUploadedSettingCache.remove(userId)
            } else {
                //部分内容在请求期间又更新了，需要将没有变更的内容设置为null，下次就不需要再请求了
                val newSetting = userDao.setUpLoadUserRelationSettingsNull(
                    userId,
                    setMuteMessagesNull = !isMuteMessagesChanged,
                    setMuteNotificationNull = !isMuteNotificationChanged,
                    setRemarkNull = !isRemakeChanged
                )
                newSetting?.let {
                    buzUserUnUploadedSettingCache.put(userId, newSetting)
                }
            }
        }
    }

    override suspend fun getUserUnUploadedSettings(userId: Long): UserUnUploadedSetting? {
        ensureLoadAllUploadedSetting()
        return buzUserUnUploadedSettingCache[userId]
    }

    override suspend fun getAllUserUnUploadedSettings(): List<UserUnUploadedSetting>? {
        ensureLoadAllUploadedSetting()
        return buzUserUnUploadedSettingCache.values.toList()
    }

    override fun getUserCompositeFlow(userId: Long): Flow<BuzUserComposite?> =
        combine(getUserFlow(userId), getUserRelationFlow(userId)) { user, relationInfo ->
            if (user != null) {
                BuzUserComposite(user, relationInfo)
            } else {
                null
            }
        }

    override fun getUserCompositeFlow(userIds: List<Long>): Flow<Map<Long, BuzUserComposite>> =
        combine(
            getUserFlow(userIds),
            getUserRelationFlow(userIds)
        ) { userMap, relationMap ->
            val resultMap = HashMap<Long, BuzUserComposite>(userIds.size)
            userIds.forEach {
                val user = userMap[it]
                val relation = relationMap[it]
                if (user != null) {
                    resultMap[it] = BuzUserComposite(user, relation)
                }
            }
            resultMap
        }.flowOn(Dispatchers.Default)

    private suspend fun queryUserFromDb(userIds: List<Long>): List<BuzUser>? {
        return awaitInScopeIO(userScope) {
            userMutex.withReentrantLock {
                userDao.queryUser(userIds)?.apply {
                    this.forEach {
                        buzUserLruCache.put(it.userId, it)
                    }
                }
            }
        }
    }

    @Deprecated("请使用queryUserFromDb", replaceWith = ReplaceWith("queryUserFromDb(userId)"))
    private fun queryUserFromDBUnSuspend(userId: Long): BuzUser? {
        return userDao.queryUser(userId)?.apply {
            //这里没有加锁，理论上可能会有并发问题，但是历史访问太多了，没办法，先这么搞
            buzUserLruCache.put(userId, this)
        }
    }

    private suspend fun queryUserFromDb(userId: Long): BuzUser? {
        return awaitInScopeIO(userScope) {
            userMutex.withReentrantLock {
                userDao.queryUser(userId)?.apply {
                    buzUserLruCache.put(userId, this)
                }
            }
        }
    }

    @Deprecated("为了兼容旧代码，新代码别用")
    private fun queryUserRelationFromDbNotSuspend(userId: Long): BuzUserRelationEntity? {
        return userDao.queryUserRelation(userId)?.apply {
            //这里没有加锁，理论上可能会有并发问题，但是历史代码调用太多了，没办法，先这么搞
            buzUserRelationLruCache.put(userId, this)
        }
    }

    private suspend fun queryUserRelationFromDb(userId: Long): BuzUserRelationEntity? {
        return awaitInScopeIO(userScope) {
            userMutex.withReentrantLock {
                userDao.queryUserRelation(userId)?.apply {
                    buzUserRelationLruCache.put(userId, this)
                }
            }
        }
    }

    private suspend fun queryUserRelationFromDb(userIds: List<Long>): List<BuzUserRelationEntity>? {
        return awaitInScopeIO(userScope) {
            userMutex.withReentrantLock {
                userDao.queryUserRelation(userIds)?.apply {
                    this.forEach {
                        buzUserRelationLruCache.put(it.userId, it)
                    }
                }
            }
        }
    }

    override suspend fun clearBuzUserTable() {
        userDao.clearBuzUserTable()
    }
}