package com.interfun.buz.social.repo

import com.interfun.buz.base.ktx.asString
import com.interfun.buz.common.R
import com.interfun.buz.component.hilt.UserQualifier
import com.interfun.buz.social.entity.SelectableUser
import com.interfun.buz.social.entity.SelectableUserList
import com.interfun.buz.social.entity.SimpleBuzUser
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.*
import java.util.regex.Pattern
import javax.inject.Inject

/**
 * Author: ChenYouSheng
 * Date: 2025/7/9
 * Email: <EMAIL>
 * Desc: 支持搜索、单选/多选的好友列表
 */
class SearchFriendRepositoryImpl @Inject constructor(
    @UserQualifier private val userRepository: UserRepository,
) : SearchFriendRepository {

    // 默认单选
    private val supportMultiSelect: Boolean = false

    private val myFriendListFlow = userRepository.getAllFriendsFlow().map { buzUserComposites ->
        buzUserComposites.filter { it.user.isNormalUser }
            .map { buzUserComposites ->
                SimpleBuzUser(
                    userId = buzUserComposites.user.userId,
                    displayName = buzUserComposites.fullNickName,
                    portrait = buzUserComposites.user.portrait,
                    buzId = if (!buzUserComposites.user.buzId.isNullOrEmpty()) R.string.common_symbol_at.asString()
                            + buzUserComposites.user.buzId else "",
                )
            }
    }

    private val searchKeywordFlow = MutableStateFlow<String?>(null)

    private val selectedFriendsFlow = MutableStateFlow<LinkedHashSet<Long>>(LinkedHashSet())

    override fun getSelectableFriendListFlow(): Flow<SelectableUserList> {
        return combine(
            myFriendListFlow,
            selectedFriendsFlow,
            searchKeywordFlow
        ) { allFriends, selectedMemberSet, searchKeyword ->
            val isInSearch = !searchKeyword.isNullOrEmpty()
            val selectableList = allFriends.mapNotNull { user ->
                val isSelected = selectedMemberSet.contains(user.userId)
                if (searchKeyword.isNullOrEmpty()) {
                    SelectableUser(
                        user,
                        isSelected,
                        keywordIndex = null
                    )
                } else {
                    val foundIndex = findKeywordIndex(user.displayName, searchKeyword)
                    if (foundIndex == null) {
                        null
                    } else {
                        SelectableUser(
                            user,
                            isSelected,
                            keywordIndex = foundIndex
                        )
                    }
                }
            }
            SelectableUserList(
                selectableList = selectableList,
                isInSearch = isInSearch,
                selectedSize = selectedMemberSet.size,
            )
        }.flowOn(Dispatchers.Default)
    }


    private fun findKeywordIndex(source: String, keyword: String): List<IntRange>? {
        val pattern = Pattern.compile(
            Pattern.quote(keyword),
            Pattern.CASE_INSENSITIVE
        )
        val matcher = pattern.matcher(source)
        var list: MutableList<IntRange>? = null
        while (matcher.find()) {
            val startIndex = matcher.start()
            val endIndex = matcher.end()
            if (list == null) {
                list = ArrayList(3)
            }
            list.add(IntRange(startIndex, endIndex))
        }
        return list
    }

    override suspend fun searchFriend(keyword: String) {
        searchKeywordFlow.emit(keyword)
    }

    override fun selectFriend(userId: Long) {
        selectedFriendsFlow.update { oldSet ->
            val newSet = LinkedHashSet(oldSet)
            if (supportMultiSelect) {
                if (newSet.add(userId)) newSet else oldSet
            } else {
                if (oldSet.size == 1 && oldSet.contains(userId)) {
                    oldSet
                } else {
                    linkedSetOf(userId)
                }
            }
        }
    }

    override fun unSelectFriend(userId: Long) {
        selectedFriendsFlow.update { oldSet ->
            if (userId in oldSet) {
                LinkedHashSet(oldSet).apply { remove(userId) }
            } else {
                oldSet
            }
        }
    }

    override fun unSelectAllFriend() {
        selectedFriendsFlow.update {
            LinkedHashSet()
        }
    }

}