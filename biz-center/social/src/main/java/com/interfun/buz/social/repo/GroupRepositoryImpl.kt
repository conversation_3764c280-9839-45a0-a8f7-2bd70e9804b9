package com.interfun.buz.social.repo

import androidx.lifecycle.Lifecycle
import androidx.lifecycle.withStarted
import com.buz.idl.group.bean.GroupBaseInfo
import com.buz.idl.group.bean.GroupExtraInfo
import com.buz.idl.group.bean.GroupInfo
import com.buz.idl.group.response.ResponseJoinGroup
import com.interfun.buz.base.ktx.collectInScope
import com.interfun.buz.base.ktx.log
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.base.ktx.onFirst
import com.interfun.buz.common.bean.Resp
import com.interfun.buz.common.bean.Resp.Error
import com.interfun.buz.common.bean.Resp.Success
import com.interfun.buz.common.bean.push.PushBusinessType
import com.interfun.buz.common.bean.push.PushOP
import com.interfun.buz.common.di.NetworkStatus
import com.interfun.buz.common.eventbus.MuteStatusUpdateEvent
import com.interfun.buz.common.eventbus.group.GroupInfoDidUpdateEvent
import com.interfun.buz.common.eventbus.group.GroupLeaveSuccessEvent
import com.interfun.buz.common.ktx.codeRequestSuccess
import com.interfun.buz.common.net.dispatcher.*
import com.interfun.buz.common.utils.parse
import com.interfun.buz.component.hilt.GlobalQualifier
import com.interfun.buz.component.hilt.UserAuth
import com.interfun.buz.component.hilt.UserQualifier
import com.interfun.buz.push.model.PushPayloadType
import com.interfun.buz.push.repository.PushRepository
import com.interfun.buz.signal.ISignalManagerPresenter
import com.interfun.buz.signal.getProtocolDataChangeFlow
import com.interfun.buz.social.datasource.GroupLocalDataSource
import com.interfun.buz.social.datasource.GroupRemoteDataSource
import com.interfun.buz.social.db.entity.*
import com.interfun.buz.social.entity.CreateGroupResult
import com.interfun.buz.social.entity.JoinGroupResult
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

internal class GroupRepositoryImpl @Inject constructor(
    @UserQualifier private val userAuth: UserAuth,
    @UserQualifier private val userScope: CoroutineScope,
    @UserQualifier private val groupLocalDS: GroupLocalDataSource,
    @UserQualifier private val groupRemoteDS: GroupRemoteDataSource,
    @UserQualifier private val userRepository: UserRepository,
    private val signalRepository : ISignalManagerPresenter,
    @GlobalQualifier private val networkStatus: NetworkStatus,
    @GlobalQualifier private val appLifecycle: Lifecycle,
) : GroupRepository {

    private val TAG = "GroupRepository"
    private val syncGroupDispatcher = RequestDispatcher("syncGroupDispatcher") { groupIdList ->
        when (groupIdList.size) {
            0 -> true
            1 -> {
                // 单个群组使用合并请求
                val resp = loadGroupCompositeFromServer(groupIdList.first())
                if (resp is Resp.Error && resp.code?.codeRequestSuccess == false) {
                    syncGroupComposite(groupIdList, true)
                    false
                } else {
                    true
                }
            }

            else -> {
                val resp = loadGroupCompositeFromServer(groupIdList)
                if (resp is Resp.Error && resp.code?.codeRequestSuccess == false) {
                    syncGroupComposite(groupIdList, true)
                    false
                } else {
                    true
                }
            }
        }
    }

    private val syncJoinedGroupsDispatcher = UniqueRequestDispatcher<Retryable<Unit>>(
        userScope,
        dispatchInterval = DefaultRetryableDispatchInterval(networkStatus, appLifecycle),
        debugTag = "syncJoinedGroupsDispatcher",
        onUndeliveredElement = null,
        uniqueKey = { it.data })
    { request, _ ->
        val resp = loadJoinedGroupsFromServer()
        if (resp is Resp.Error && resp.code?.codeRequestSuccess == false) {
            syncJoinedGroupsFromServer(true)
            false
        } else {
            true
        }
    }

    private val groupSettingUpdateDispatcher = UniqueRequestDispatcher<Retryable<Long>>(
        userScope,
        dispatchInterval = DefaultRetryableDispatchInterval(networkStatus, appLifecycle),
        debugTag = "groupSettingUpdateDispatcher",
        onUndeliveredElement = null,
        uniqueKey = { it.data })
    { request, _ ->
        val groupId = request.data
        val resp = updateGroupSettingToServer(groupId)
        if (resp is Resp.Error && resp.code?.codeRequestSuccess == false) {
            syncGroupSettingToServer(groupId, true)
            false
        } else {
            true
        }
    }

    @Deprecated("为了兼容旧代码，别用")
    override fun getUserScope(): CoroutineScope {
        return userScope
    }

    override fun init() {
        logInfo(TAG,"Group Repo init,uid:${userAuth.userId}")
        observeSignalPush()
        startSyncAllUnUploadedGroupSettingFromCache()
    }

    private fun observeSignalPush() {
        signalRepository.getProtocolDataChangeFlow()
            .filter { it.businessType == PushBusinessType.GROUP_INFO_CHANGE.type }
            .collectInScope(userScope) {
                val groupId = it.businessId?.toLongOrNull() ?: return@collectInScope
                logInfo(TAG,"group info changed from signal:$groupId")
                syncGroupComposite(arrayListOf(groupId))
            }
    }

    private fun startSyncAllUnUploadedGroupSettingFromCache() {
        userScope.launch {
            appLifecycle.withStarted {
                userScope.launch {
                    val list = groupLocalDS.getAllGroupUnUploadedSettings()
                    logInfo(TAG, "startSyncAllUnUploadedGroupSettingFromCache,size:${list?.size}")
                    list?.forEach {
                        //2秒一个，做个简单的周期，避免瞬间太多请求
                        delay(2000L)
                        syncGroupSettingToServer(it.groupId, false)
                    }
                }
            }
        }
    }

    override suspend fun getGroup(groupId: Long): BuzGroup? {
        return loadGroupFromLocal(groupId)
            ?: (loadGroupCompositeFromServer(groupId) as? Resp.Success)?.data?.buzGroup
    }

    override fun getGroupFlow(groupId: Long, forceRefresh: Boolean): Flow<BuzGroup> = flow {
        var isSync = false
        getGroupFromCacheFlow(groupId).collect { localData ->
            if (!isSync && (localData == null || forceRefresh)) {
                syncGroupComposite(arrayListOf(groupId))
                isSync = true
            }
            if (localData != null) {
                emit(localData)
            }
        }
    }

    override fun getGroupFlow(groupList: List<Long>): Flow<Map<Long, BuzGroup>> = flow {
        var isSync = false
        getGroupFromCacheFlow(groupList).collect { localData ->
            if (!isSync && localData.isEmpty()) {
                syncGroupComposite(groupList)
                isSync = true
            }
            emit(localData)
        }
    }

    override suspend fun getGroupFromCache(groupId: Long): BuzGroup? {
        return loadGroupFromLocal(groupId)
    }

    override fun getGroupFromCacheFlow(groupId: Long): Flow<BuzGroup?> = channelFlow {
        groupLocalDS.getGroupFlow(groupId).collectLatest { groupTable ->
            if (groupTable == null) {
                send(null)
                return@collectLatest
            }
            val servGroupName = groupTable.serverGroupName
            if (!servGroupName.isNullOrEmpty()) {
                //有自定义名称，直接用自定义名称
                send(groupTable.toBuzGroup())
                return@collectLatest
            }
            val firstFewUserIds = groupTable.firstFewUsers?.map { it.userId }
            if (firstFewUserIds.isNullOrEmpty()) {
                //理论上不会存在这种情况
                send(groupTable.toBuzGroup())
                return@collectLatest
            }
            userRepository.syncUserCompositeFromServerIfCacheAbsent(firstFewUserIds)
            userRepository.getUserCompositeFromCacheFlow(firstFewUserIds).collectLatest { userMap ->
                //根据firstFewUsers生成群组名称，并动态变更
                send(groupTable.toBuzGroup())
            }
        }
    }

    override fun getGroupFromCacheFlow(groupList: List<Long>): Flow<Map<Long, BuzGroup>> {
        return groupLocalDS.getGroupFlow(groupList).map { groupTableMap ->
            groupTableMap.mapValues {
                it.value.toBuzGroup()
            }
        }
    }

    override suspend fun clearGroupInfo() {
        groupLocalDS.clearAllGroupTables()
    }

    override suspend fun getGroupExtra(groupId: Long): BuzGroupExtra? {
        return loadGroupExtraFromLocal(groupId)
            ?: (loadGroupCompositeFromServer(groupId) as? Resp.Success)?.data?.buzGroup?.let {
                // 如果没有额外数据，但有群组数据，创建一个默认额外信息
                BuzGroupExtra(groupId = it.groupId)
            }
    }

    override fun getGroupExtraFlow(
        groupId: Long,
        forceRefresh: Boolean
    ): Flow<BuzGroupExtra> = flow {
        var isSync = false
        getGroupExtraFromCacheFlow(groupId).collect { localData ->
            if (!isSync && (localData == null || forceRefresh)) {
                syncGroupComposite(arrayListOf(groupId))
                isSync = true
            }
            if (localData != null) {
                emit(localData)
            }
        }
    }

    override fun getGroupExtraFlow(groupList: List<Long>): Flow<Map<Long, BuzGroupExtra>> {
        return groupLocalDS.getGroupExtraFlow(groupList)
    }

    override suspend fun getGroupExtraFromCache(groupId: Long): BuzGroupExtra? {
        return loadGroupExtraFromLocal(groupId)
    }

    @Deprecated("为了兼容旧代码，新代码不要使用")
    override fun getGroupExtraFromCacheNotSuspend(groupId: Long): BuzGroupExtra? {
        return groupLocalDS.getGroupExtraNotSuspend(groupId)
    }

    override fun getGroupExtraFromCacheFlow(groupId: Long): Flow<BuzGroupExtra?> {
        return groupLocalDS.getGroupExtraFlow(groupId)
    }

    override fun getGroupExtraFromCacheFlow(groupList: List<Long>): Flow<Map<Long, BuzGroupExtra>> {
        return groupLocalDS.getGroupExtraFlow(groupList)
    }

    override fun syncGroupExtraFromServer(groupId: Long) {
        syncGroupCompositeFromServer(groupId)
    }

    override suspend fun updateLocalGroupExtra(extraInfo: List<BuzGroupExtra>): List<BuzGroupExtra> {
        return groupLocalDS.saveGroupExtra(extraInfo)
    }

    override suspend fun updateLocalGroupExtra(extra: BuzGroupExtra): BuzGroupExtra {
        return groupLocalDS.saveGroupExtra(extra)
    }

    override suspend fun getGroupComposite(groupId: Long): BuzGroupComposite? {
        return loadGroupCompositeFromLocal(groupId)
            ?: (loadGroupCompositeFromServer(groupId) as? Resp.Success)?.data
    }

    @Deprecated("兼容旧代码，新代码别用")
    override fun getGroupCompositeNotSuspend(groupId: Long): BuzGroupComposite? {
        return groupLocalDS.getGroupCompositeNotSuspend(groupId)?.toBuzGroupCompositeCompat()
    }

    @Deprecated("兼容旧代码，新代码别用")
    override fun getGroupCompositeFromMem(groupId: Long): BuzGroupComposite? {
        return groupLocalDS.getGroupCompositeFromMem(groupId)?.toBuzGroupCompositeCompat()
    }

    override fun getGroupCompositeFlow(
        groupId: Long,
        forceRefresh: Boolean
    ): Flow<BuzGroupComposite> =
        flow {
            var isSync = false
            getGroupCompositeFromCacheFlow(groupId).collect { localData ->
                if (!isSync && (localData == null || forceRefresh || localData.buzGroupExtra == null)) {
                    syncGroupComposite(arrayListOf(groupId))
                    isSync = true
                }
                if (localData != null) {
                    emit(localData)
                }
            }
        }

    override fun getGroupCompositeFlow(groupList: List<Long>): Flow<Map<Long, BuzGroupComposite>> =
        flow {
            var isSync = false
            getGroupCompositeFromCacheFlow(groupList).collect { localData ->
                if (!isSync) {
                    val needSyncGroupList = groupList.filter {
                        val localComposite = localData[it]
                        localComposite == null || localComposite.buzGroupExtra == null
                    }
                    syncGroupComposite(needSyncGroupList)
                    isSync = true
                }
                emit(localData)
            }
        }

    override suspend fun getGroupCompositeFromCache(groupId: Long): BuzGroupComposite? {
        return loadGroupCompositeFromLocal(groupId)
    }

    override fun getGroupCompositeFromCacheFlow(
        groupId: Long,
        refreshIfNull: Boolean
    ): Flow<BuzGroupComposite?> {
        return groupLocalDS.getGroupCompositeFlow(groupId).map { it?.toBuzGroupComposite() }
            .onFirst {
                if (it == null && refreshIfNull) {
                    syncGroupComposite(arrayListOf(groupId))
                }
            }
    }

    override fun getGroupCompositeFromCacheFlow(groupList: List<Long>): Flow<Map<Long, BuzGroupComposite>> {
        return groupLocalDS.getGroupCompositeFlow(groupList)
            .map { it.mapValues { it.value.toBuzGroupComposite() } }
    }

    override suspend fun getGroupCompositeFromServer(groupId: Long): Resp<BuzGroupComposite?> {
        return loadGroupCompositeFromServer(groupId)
    }

    override fun syncGroupCompositeFromServer(groupId: Long) {
        syncGroupComposite(arrayListOf(groupId))
    }

    override suspend fun getAllJoinedGroupsFromCache(): List<BuzGroupComposite> {
        return groupLocalDS.getAllJoinedGroups().map { it.toBuzGroupCompositeCompat() }
    }

    override suspend fun getAllJoinedGroups(): List<BuzGroupComposite> {
        val cached =  getAllJoinedGroupsFromCache()
        if (cached.isNotEmpty()) {
            return cached
        }
        return (loadJoinedGroupsFromServer() as? Resp.Success)?.data ?: emptyList()
    }

    override suspend fun getAllJoinedGroupsFromServer(): List<BuzGroupComposite> {
        return (loadJoinedGroupsFromServer() as? Resp.Success)?.data ?: emptyList()
    }

    //todo @Vincent 改一下实现，改成首次查询后续监听变更，提升性能
    override fun getAllJoinedGroupsFromCacheFlow(): Flow<List<BuzGroupComposite>> {
        return groupLocalDS.getAllJoinedGroupsFlow().map { list -> list.map { it.toBuzGroupComposite() } }
    }

    override fun getAllJoinedGroupsFlow(forceRefresh: Boolean): Flow<List<BuzGroupComposite>> =
        flow {
            var isSync = false
            if (forceRefresh) {
                syncJoinedGroupsFromServer(true)
                isSync = true
            }
            getAllJoinedGroupsFromCacheFlow().collect { localData ->
                if (!isSync && localData.isEmpty()) {
                    syncJoinedGroupsFromServer(true)
                    isSync = true
                }
                emit(localData)
            }
        }

    override fun syncAllJoinedGroupsFromServer() {
        syncJoinedGroupsFromServer(false)
    }

    override suspend fun updateGroupSetting(
        groupId: Long,
        muteMessages: Int?,
        muteNotification: Int?
    ) {
        //先本地更新，然后再请求服务端更新，期间会重试，进程关闭没有更新成功会在下次启动前后台切换重试
        groupLocalDS.insertOrUpdateUnUploadedGroupSettings(
            groupId,
            muteMessages,
            muteNotification
        )
        groupLocalDS.updateGroupSettings(groupId, muteMessages, muteNotification)
        syncGroupSettingToServer(groupId, false)
        //兼容旧代码
        MuteStatusUpdateEvent.post(groupId)
    }

    override suspend fun createGroup(
        groupName: String?,
        invitedUserIds: List<Long>?,
        portraitUploadId: Long?
    ): Resp<CreateGroupResult> {
        val resp = groupRemoteDS.createGroup(groupName, invitedUserIds, portraitUploadId)
        return when (resp) {
            is Success -> {
                val groupInfo = resp.data?.groupInfo
                val groupComposite = if (groupInfo != null) {
                    saveGroupInfo(groupInfo).toBuzGroupComposite()
                } else {
                    null
                }
                if (groupComposite == null) {
                    Error(999999, "group info from server is null", resp.prompt)
                }else {
                    Success(CreateGroupResult(groupComposite, resp.data?.rejectedMsg), resp.prompt)
                }
            }

            is Error -> {
                Error(resp.code, resp.msg, resp.prompt)
            }
        }
    }

    override suspend fun joinGroup(groupId: Long, inviterId: Long?): Resp<JoinGroupResult> {
        val resp = groupRemoteDS.joinGroup(groupId, inviterId)
        return when (resp) {
            is Resp.Success -> {
                val groupInfo = resp.data?.groupInfo
                if (groupInfo != null){
                    saveGroupInfo(groupInfo)
                } else {
                    //异常场景，理论不会出现，同步一下数据
                    syncGroupComposite(arrayListOf(groupId))
                }
                Resp.Success(JoinGroupResult(resp.data?.rejectedMsg),resp.prompt)
            }
            is Resp.Error -> {
                Resp.Error(resp.code, resp.msg, resp.prompt)
            }
        }
    }

    private suspend fun saveGroupInfo(groupInfo: GroupInfo) : GroupTableComposite{
        val groupComposite = groupInfo.toBuzGroupTableComposite()
        return groupLocalDS.saveGroupComposite(groupComposite)
    }

    override suspend fun quitGroup(groupId: Long): Resp<Unit> {
        val resp = groupRemoteDS.quitGroup(groupId)
        return when (resp) {
            is Resp.Success -> {
                groupLocalDS.updateQuitGroup(groupId)
                //同步一下数据
                syncGroupComposite(arrayListOf(groupId))
                //兼容旧逻辑
                GroupLeaveSuccessEvent.post(groupId)
                Resp.Success(Unit, resp.prompt)
            } is Resp.Error -> {
                Resp.Error(resp.code, resp.msg, resp.prompt)
            }
        }
    }

    override suspend fun reportGroup(groupId: Long): Resp<Unit> {
        val resp = groupRemoteDS.reportGroup(groupId)
        return resp
    }

    private fun syncGroupComposite(groupIds: List<Long>, isRetry: Boolean = false) {
        if (groupIds.isEmpty()) {
            return
        }
        syncGroupDispatcher.request(Retryable(groupIds, isRetry))
    }

    private fun syncGroupSettingToServer(groupId: Long, isRetry: Boolean) {
        groupSettingUpdateDispatcher.request(Retryable(groupId, isRetry))
    }

    private fun syncJoinedGroupsFromServer(isRetry: Boolean) {
        syncJoinedGroupsDispatcher.request(Retryable(Unit, isRetry))
    }

    override suspend fun updateGroupInfo(
        groupId: Long,
        name: String?,
        portraitUploadId: Long?
    ): Resp<BuzGroupComposite?> {
        val resp = groupRemoteDS.updateGroupInfo(groupId, name, portraitUploadId)
        return when (resp) {
            is Error -> {
                Resp.Error(resp.code, resp.msg, resp.prompt)
            }

            is Success -> {
                val groupInfo = resp.data
                if (groupInfo != null) {
                    val groupComposite = saveGroupInfo(groupInfo).toBuzGroupComposite()
                    GroupInfoDidUpdateEvent.post(groupInfo)
                    Resp.Success(groupComposite, resp.prompt)
                } else {
                    //异常场景，理论不会出现，同步一下数据
                    syncGroupComposite(arrayListOf(groupId))
                    Resp.Success(null, resp.prompt)
                }
            }
        }
    }

    private suspend fun updateGroupSettingToServer(
        groupId: Long
    ): Resp<BuzGroupExtra?> {
        //查出来本地记录
        val localSetting =
            groupLocalDS.getGroupUnUploadedSettings(groupId) ?: return Resp.Success(null)
        //上传到服务端
        val serverResp = groupRemoteDS.updateSetting(
            groupId, localSetting.muteMessages, localSetting.muteNotification
        )
        return when (serverResp) {
            is Resp.Success -> {
                //更新完成后把未请求的记录删除
                groupLocalDS.deleteGroupUnUploadedSettingsIfMatched(
                    groupId,
                    localSetting.muteMessages,
                    localSetting.muteNotification,
                )
                Resp.Success(getGroupExtraFromCache(groupId))
            }

            is Resp.Error -> {
                Resp.Error(serverResp.code, serverResp.msg, serverResp.prompt)
            }
        }
    }

    private suspend fun loadGroupFromLocal(groupId: Long): BuzGroup? {
        return groupLocalDS.getGroup(groupId)?.toBuzGroup()
    }

    private suspend fun loadGroupCompositeFromServer(groupId: Long): Resp<BuzGroupComposite?> {
        val resp = groupRemoteDS.requestGroup(groupId)
        resp.prompt?.parse()
        return when (resp) {
            is Resp.Success -> {
                val data = resp.data?.toBuzGroupTableComposite()
                if (data != null) {
                    val savedData = groupLocalDS.saveGroupComposite(data).toBuzGroupComposite()
                    resp.data?.let { GroupInfoDidUpdateEvent.post(it) }
                    Resp.Success(savedData)
                } else {
                    Resp.Success(null)
                }
            }

            is Resp.Error -> {
                Resp.Error(resp.code, resp.msg, resp.prompt)
            }
        }
    }

    private suspend fun loadGroupCompositeFromServer(groupIds: List<Long>): Resp<List<BuzGroupComposite>?> {
        val resp = groupRemoteDS.requestGroupList(groupIds)
        resp.prompt?.parse()
        return when (resp) {
            is Resp.Success -> {
                val groupListFromServer = resp.data?.map { it.toBuzGroupTableComposite() }
                if (groupListFromServer == null) {
                    Resp.Success(null)
                } else {
                    val result = groupLocalDS.saveGroupComposite(groupListFromServer)
                        .map { it.toBuzGroupComposite() }
                    resp.data?.forEach {
                        GroupInfoDidUpdateEvent.post(it)
                    }
                    Resp.Success(result)
                }
            }

            is Resp.Error -> {
                Resp.Error(resp.code, resp.msg, resp.prompt)
            }
        }
    }

    private suspend fun loadJoinedGroupsFromServer(): Resp<List<BuzGroupComposite>?> {
        val resp = groupRemoteDS.getJoinedGroups()
        resp.prompt?.parse()
        return when (resp) {
            is Resp.Success -> {
                val groupListFromServer = resp.data?.map { it.toBuzGroupTableComposite() }
                if (groupListFromServer == null) {
                    Resp.Success(null)
                } else {
                    val result = groupLocalDS.saveGroupComposite(groupListFromServer)
                        .map { it.toBuzGroupComposite() }
                    Resp.Success(result)
                }
            }

            is Resp.Error -> {
                Resp.Error(resp.code, resp.msg, resp.prompt)
            }
        }
    }

    private suspend fun loadGroupExtraFromLocal(groupId: Long): BuzGroupExtra? {
        return groupLocalDS.getGroupExtra(groupId)
    }

    private suspend fun loadGroupCompositeFromLocal(groupId: Long): BuzGroupComposite? {
        return groupLocalDS.getGroupComposite(groupId)?.toBuzGroupComposite()
    }

    private suspend fun createGroupName(groupTable: BuzGroupTable): String {
        return createGroupName(
            groupTable.groupId,
            groupTable.serverGroupName,
            groupTable.firstFewUsers
        )
    }

    private suspend fun createGroupName(
        groupId: Long,
        serverGroupName: String?,
        firstFewUsers: List<GroupSimpleUser>?
    ): String {
        if (!serverGroupName.isNullOrEmpty()) {
            return serverGroupName
        }
        if (firstFewUsers.isNullOrEmpty()) {
            //正常情况理论上不会出现
            return groupId.toString()
        }
        //根据firstFewUsers生成群组名称
        val userNameBuffer = StringBuffer()
        val userComposite =
            userRepository.getUserCompositeFromCache(firstFewUsers.map { it.userId })
        for ((index, simpleUser) in firstFewUsers.withIndex()) {
            val nickName = userComposite[index]?.firstNickName
                ?: simpleUser.firstNickName
            userNameBuffer.append(nickName)
            if (index < firstFewUsers.size - 1) {
                userNameBuffer.append(",")
            }
        }
        return userNameBuffer.toString()
    }

    private fun createGroupName(
        firstFewUsers: List<GroupSimpleUser>,
        cachedUserMap: Map<Long, BuzUserComposite>
    ): String {
        //根据firstFewUsers生成群组名称
        val userNameBuffer = StringBuffer()
        for ((index, simpleUser) in firstFewUsers.withIndex()) {
            val nickName = cachedUserMap[simpleUser.userId]?.firstNickName
                ?: simpleUser.firstNickName
            userNameBuffer.append(nickName)
            if (index < firstFewUsers.size - 1) {
                userNameBuffer.append(",")
            }
        }
        return userNameBuffer.toString()
    }

    private suspend fun GroupTableComposite.toBuzGroupComposite(): BuzGroupComposite {
        return BuzGroupComposite(
            buzGroup = buzGroupTable.toBuzGroup(),
            buzGroupExtra = buzGroupExtra
        )
    }

    @Deprecated("兼容旧代码，请不要使用")
    private fun GroupTableComposite.toBuzGroupCompositeCompat(): BuzGroupComposite {
        return BuzGroupComposite(
            buzGroup = buzGroupTable.toBuzGroupCompat(),
            buzGroupExtra = buzGroupExtra
        )
    }

    @Deprecated("兼容旧代码，请不要使用")
    private fun BuzGroupTable.toBuzGroupCompat(): BuzGroup {
        return BuzGroup(
            groupId = this.groupId,
            serverGroupName = this.serverGroupName,
            groupName = this.displayName ?: "${this.groupId}",
            portraitUrl = this.portraitUrl,
            firstFewPortraits = this.firstFewUsers?.map { it.portrait } ?: this.oldFirstFewPortraits,
            memberNum = this.memberNum,
            maxMemberNum = this.maxMemberNum,
            groupStatus = this.groupStatus,
            groupType = this.groupType,
            serverPortraitUrl = this.serverPortraitUrl
        )
    }

    private suspend fun BuzGroupTable.toBuzGroup(): BuzGroup {
        val groupName = createGroupName(this)
        return BuzGroup(
            groupId = this.groupId,
            serverGroupName = this.serverGroupName,
            groupName = groupName,
            portraitUrl = this.portraitUrl,
            firstFewPortraits = this.firstFewUsers?.map { it.portrait } ?: this.oldFirstFewPortraits,
            memberNum = this.memberNum,
            maxMemberNum = this.maxMemberNum,
            groupStatus = this.groupStatus,
            groupType = this.groupType,
            serverPortraitUrl = this.serverPortraitUrl
        )
    }


    private suspend fun GroupInfo.toBuzGroupTableComposite(): GroupTableComposite {
        return GroupTableComposite(
            buzGroupTable = this.groupBaseInfo.toBuzGroupTable(),
            buzGroupExtra = this.groupExtraInfo?.toBuzGroupExtra(this.groupBaseInfo.groupId)
        )
    }

    private suspend fun GroupExtraInfo.toBuzGroupExtra(groupId: Long): BuzGroupExtra {
        val unUploadedSetting = groupLocalDS.getGroupUnUploadedSettings(groupId)
        return BuzGroupExtra(
            groupId = groupId,
            canInvite = this.canInvite,
            canEdit = this.canEdit,
            userRole = this.userRole,
            userStatus = this.userStatus,
            muteMessages = unUploadedSetting?.muteMessages ?: this.muteMessages,
            muteNotification = unUploadedSetting?.muteNotification ?: this.muteNotification,
        )
    }

    private suspend fun GroupBaseInfo.toBuzGroupTable(): BuzGroupTable {
        val firstFewUsers = this.firstFewMemberInfos.map {
            GroupSimpleUser(
                it.userId ?: 0L,
                it.firstName,
                it.lastName,
                it.userName,
                it.portrait
            )
        }
        //为了兼容旧代码，本可以不用的，但是旧逻辑没办法，不能每次获取时组装
        val displayGroupName = createGroupName(this.groupId, this.groupName, firstFewUsers)
        return BuzGroupTable(
            groupId = this.groupId,
            serverGroupName = this.groupName,
            displayName = displayGroupName,
            portraitUrl = this.portraitUrl,
            serverPortraitUrl = this.serverPortraitUrl,
            memberNum = this.memberNum,
            maxMemberNum = this.maxMemberNum,
            groupStatus = this.groupStatus,
            groupType = this.groupType,
            firstFewUsers = firstFewUsers
        )
    }

    private fun RequestDispatcher(
        debugTag: String,
        handler: suspend (List<Long>) -> Boolean
    ) = MergedRequestDispatcher<Retryable<List<Long>>>(
        scope = userScope,
        dispatchInterval = DefaultRetryableDispatchInterval(networkStatus, appLifecycle),
        debugTag = debugTag,
        onUndeliveredElement = null,
        requestHandler = { requestList ->
            val groupIds = requestList.flatMapTo(hashSetOf()) { it.data }
            handler(groupIds.toList())
        }
    )
} 