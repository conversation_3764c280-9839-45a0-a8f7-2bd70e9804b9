package com.interfun.buz.social.repo

import androidx.lifecycle.Lifecycle
import androidx.lifecycle.withStarted
import com.buz.idl.user.bean.UserInfo
import com.buz.idl.user.bean.UserRelation
import com.buz.idl.user.bean.UserRelationInfo
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.bean.Resp
import com.interfun.buz.common.bean.chat.MuteType
import com.interfun.buz.common.bean.push.PushBusinessType
import com.interfun.buz.common.di.NetworkStatus
import com.interfun.buz.common.eventbus.FriendsSyncCompleteEvent
import com.interfun.buz.common.eventbus.MuteStatusUpdateEvent
import com.interfun.buz.common.eventbus.user.AddFriendEvent
import com.interfun.buz.common.eventbus.user.BlockEventType
import com.interfun.buz.common.eventbus.user.BlockUserEvent
import com.interfun.buz.common.eventbus.user.UserInfoUpdateEvent
import com.interfun.buz.common.ktx.codeRequestSuccess
import com.interfun.buz.common.ktx.toastSolidWarning
import com.interfun.buz.common.net.dispatcher.*
import com.interfun.buz.common.utils.parse
import com.interfun.buz.component.hilt.GlobalQualifier
import com.interfun.buz.component.hilt.UserAuth
import com.interfun.buz.component.hilt.UserQualifier
import com.interfun.buz.push.model.PushPayloadType
import com.interfun.buz.push.repository.PushRepository
import com.interfun.buz.signal.ISignalManagerPresenter
import com.interfun.buz.signal.getProtocolDataChangeFlow
import com.interfun.buz.social.datasource.UserLocalDataSource
import com.interfun.buz.social.datasource.UserRemoteDataSource
import com.interfun.buz.social.db.entity.BuzUser
import com.interfun.buz.social.db.entity.BuzUserRelationEntity
import com.interfun.buz.social.db.entity.BuzUserComposite
import com.interfun.buz.social.db.entity.UserUnUploadedSetting
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

internal class UserRepositoryImpl @Inject constructor(
    @UserQualifier private val userAuth: UserAuth,
    @UserQualifier private val userScope: CoroutineScope,
    @UserQualifier private val userLocalDS: UserLocalDataSource,
    @UserQualifier private val userRemoteDS: UserRemoteDataSource,
    @UserQualifier private val userOnlineStatusRepo: UserOnlineStatusRepository,
    @GlobalQualifier private val networkStatus: NetworkStatus,
    @GlobalQualifier private val appLifecycle: Lifecycle,
    private val pushRepository: PushRepository,
    private val signalRepository : ISignalManagerPresenter,
) : UserRepository {

    private val TAG = "UserRepository"
    private val syncUserDispatcher = RequestDispatcher("syncUserDispatcher") { userIdList ->
        when (userIdList.size) {
            0 -> true
            1 -> {
                val resp = loadUserFromServer(userIdList.first())
                if (resp is Resp.Error && resp.code?.codeRequestSuccess == false) {
                    syncBuzUser(userIdList, true)
                    false
                } else {
                    true
                }
            }

            else -> {
                val resp = loadUserBatchFromServer(userIdList)
                if (resp is Resp.Error && resp.code?.codeRequestSuccess == false) {
                    syncBuzUser(userIdList, true)
                    false
                } else {
                    true
                }
            }
        }
    }

    private val syncUserRelationDispatcher =
        RequestDispatcher("syncUserRelationDispatcher") { userIdList ->
            when (userIdList.size) {
                0 -> true
                1 -> {
                    val resp = loadUserRelationFromServer(userIdList.first())
                    if (resp is Resp.Error && resp.code?.codeRequestSuccess == false) {
                        syncUserRelation(userIdList, true)
                        false
                    } else {
                        true
                    }
                }

                else -> {
                    val resp = loadUserRelationFromServer(userIdList)
                    if (resp is Resp.Error && resp.code?.codeRequestSuccess == false) {
                        syncUserRelation(userIdList, true)
                        false
                    } else {
                        true
                    }
                }
            }
        }

    private val syncUserCompositeDispatcher =
        RequestDispatcher("syncUserCompositeDispatcher") { userIdList ->
            when (userIdList.size) {
                0 -> true
                1 -> {
                    val id = userIdList.first()
                    val resp = loadUserCompositeFromServer(id)
                    if (resp is Resp.Error && resp.code?.codeRequestSuccess == false) {
                        syncUserComposite(userIdList, true)
                        false
                    } else {
                        true
                    }
                }

                else -> {
                    val resp = loadUserCompositeFromServer(userIdList)
                    if (resp is Resp.Error && resp.code?.codeRequestSuccess == false) {
                        syncUserComposite(userIdList, true)
                        false
                    } else {
                        true
                    }
                }
            }
        }

    private val userSettingUpdateDispatcher = UniqueRequestDispatcher<Retryable<Long>>(
        userScope,
        dispatchInterval = DefaultRetryableDispatchInterval(networkStatus, appLifecycle),
        debugTag = "userSettingUpdateDispatcher",
        onUndeliveredElement = null,
        uniqueKey = { it.data })
    { request, _ ->
        val userId = request.data
        val resp = updateUserSettingToServer(userId)
        if (resp is Resp.Error && resp.code?.codeRequestSuccess == false) {
            syncUserSettingToServer(userId, true)
            false
        } else {
            true
        }
    }

    private val syncFriendsDispatcher = UniqueRequestDispatcher<Retryable<Unit>>(
        userScope,
        dispatchInterval = DefaultRetryableDispatchInterval(networkStatus, appLifecycle),
        debugTag = "syncFriendsDispatcher",
        onUndeliveredElement = null,
        uniqueKey = { it.data })
    { request, _ ->
        val result = loadFriendsFromServer()
        if (!result) {
            syncAllFriendsInternal(true)
        }
        result
    }

    private val syncBlockListDispatcher = UniqueRequestDispatcher<Retryable<Unit>>(
        userScope,
        dispatchInterval = DefaultRetryableDispatchInterval(networkStatus, appLifecycle),
        debugTag = "syncBlockListDispatcher",
        onUndeliveredElement = null,
        uniqueKey = { it.data })
    { request, _ ->
        val result = loadBlockListFromServer()
        if (!result) {
            syncBlockListInternal(true)
        }
        result
    }

    override fun init() {
        logInfo(TAG,"user Repo init,uid:${userAuth.userId}")
        if (userAuth is UserAuth.Unauthenticated) {
            //未登录，不处理
            return
        }
        startSyncAllUnUploadedUserSettingFromCache()
        observeFcmPush()
        observeFriendListChange()
        fetchSelfInfoOnceWhenPageStart()
    }

    @Deprecated("兼容旧代码用")
    override fun getUserScope(): CoroutineScope {
        return userScope
    }

    override suspend fun getUser(userId: Long): BuzUser? {
        return loadUserFromLocal(userId)
            ?: (loadUserCompositeFromServer(userId) as? Resp.Success)?.data?.user
    }

    override fun getUserFlow(userId: Long, forceRefresh: Boolean): Flow<BuzUser> = flow {
        var isSync = false
        if (forceRefresh){
            syncBuzUser(arrayListOf(userId))
            isSync = true
        }
        getUserFromCacheFlow(userId).collect { localData ->
            if (!isSync && localData == null) {
                syncBuzUser(arrayListOf(userId))
                isSync = true
            }
            if (localData != null) {
                emit(localData)
            }
        }
    }

    override fun getUserFlow(userList: List<Long>): Flow<Map<Long, BuzUser>> = flow {
        var isSync = false
        getUserFromCacheFlow(userList).collect { localData ->
            if (!isSync) {
                val needSyncList = userList.filter { localData[it] == null }
                syncBuzUser(needSyncList)
                isSync = true
            }
            emit(localData)
        }
    }

    override suspend fun getUserFromCache(userId: Long): BuzUser? {
        return loadUserFromLocal(userId)
    }

    override suspend fun getUserFromCache(userList: List<Long>): List<BuzUser?> {
        return loadUserFromLocal(userList)
    }

    @Deprecated("请使用getUserFromCache")
    override fun getUserFromCacheUnSuspend(userId: Long): BuzUser? {
        return userLocalDS.getUserUnSuspend(userId)
    }

    /**
     * 当本地数据没有时，会先emit一个null
     */
    override fun getUserFromCacheFlow(userId: Long): Flow<BuzUser?> {
        return userLocalDS.getUserFlow(userId)
    }

    override fun getUserFromCacheFlow(userList: List<Long>): Flow<Map<Long, BuzUser>> {
        return userLocalDS.getUserFlow(userList)
    }

    override suspend fun clearUserInfo() {
        userLocalDS.clearBuzUserTable()
    }

    override suspend fun updateUser(user: UserInfo): BuzUser {
        return userLocalDS.saveUser(user.toBuzUser())
    }

    override suspend fun markUserAccountDeleted(userId: Long): BuzUser? {
        if (isDebug){
            //todo 测试代码，防止部分地方拿了错误的用户id来请求，比如群id
            toastSolidWarning("!!!注意注意,有个注销用户请求，需要确认是否是错误参数：$userId")
        }
        return userLocalDS.markUserAccountDeleted(userId)
    }

    override suspend fun updateUser(user: List<UserInfo>): List<BuzUser> {
        return userLocalDS.saveUser(user.map { it.toBuzUser() })
    }

    override suspend fun getUserRelation(userId: Long): BuzUserRelationEntity? {
        return loadUserRelationFromLocal(userId)
            ?: (loadUserRelationFromServer(userId) as? Resp.Success)?.data
    }

    override suspend fun getUserRelationFromServer(userId: Long): Resp<BuzUserRelationEntity?> {
        return loadUserRelationFromServer(userId)
    }

    override suspend fun getFriendsSizeFromCache(): Int {
        return userLocalDS.getFriendsSize()
    }

    override fun syncBlockList() {
        syncBlockListInternal(false)
    }

    private fun syncBlockListInternal(isRetry: Boolean) {
        syncBlockListDispatcher.request(Retryable(Unit, isRetry))
    }

    override fun getBlockListFromCacheFlow(): Flow<List<BuzUserComposite>> = channelFlow {
        userLocalDS.getBlockedUserIdsFlow().map { it.toHashSet() }.distinctUntilChanged()
            .collectLatest { blockedUserIdSet ->
                getUserCompositeFlow(blockedUserIdSet.toList()).collectLatest { userMap ->
                    send(userMap.values.toList())
                }
            }
    }.flowOn(Dispatchers.Default)

    override fun getAllFriendIdsFromCacheFlow(): Flow<List<Long>> {
        return userLocalDS.getAllFriendIdsFlow().distinctUntilChanged()
    }

    //todo @Vincent 改一下实现，改成首次查询后续监听变更，提升性能
    override fun getAllFriendsFlow(forceRefresh: Boolean): Flow<List<BuzUserComposite>> {
        var isSync = false
        return getAllFriendIdsFromCacheFlow().flatMapLatest { friendIds ->
            if (!isSync && (friendIds.isEmpty() || forceRefresh)) {
                syncAllFriendsInternal()
                isSync = true
            }
            getUserCompositeFlow(friendIds.toList()).map { userMap -> friendIds.mapNotNull { userMap[it] } }
        }
    }

    override suspend fun getAllFriendsFromCache(): List<BuzUserComposite> {
        return userLocalDS.getAllFriends()
    }

    override suspend fun getAllOfficialFriendsFromCache(): List<BuzUserComposite> {
        return userLocalDS.getAllOfficialFriends()
    }

    @Deprecated("为了兼容旧代码，新代码别用")
    override fun getAllFriendsFromCacheNotSuspend(): List<BuzUserComposite> {
        return userLocalDS.getAllFriendsNotSuspend()
    }

    override fun syncAllFriends() {
        syncAllFriendsInternal()
    }

    private fun syncAllFriendsInternal(isRetry: Boolean = false) {
        syncFriendsDispatcher.request(Retryable(Unit, isRetry))
    }

    override fun getUserRelationFlow(
        userId: Long, forceRefresh: Boolean
    ): Flow<BuzUserRelationEntity> = flow {
        var isSync = false
        if (forceRefresh){
            syncUserRelation(arrayListOf(userId))
            isSync = false
        }
        getUserRelationFromCacheFlow(userId).collect { localData ->
            if (!isSync && localData == null) {
                syncUserRelation(arrayListOf(userId))
                isSync = true
            }
            if (localData != null) {
                emit(localData)
            }
        }
    }

    override fun getUserRelationFlow(userList: List<Long>): Flow<Map<Long, BuzUserRelationEntity>> =
        flow {
            var isSync = false
            getUserRelationFromCacheFlow(userList).collect { localData ->
                if (!isSync) {
                    val needSyncList = userList.filter { localData[it] == null }
                    syncUserRelation(needSyncList)
                    isSync = true
                }
                emit(localData)
            }
        }

    override suspend fun getUserRelationFromCache(userId: Long): BuzUserRelationEntity? {
        return loadUserRelationFromLocal(userId)
    }

    @Deprecated("为了兼容旧代码，新代码别用")
    override fun getUserRelationFromCacheNotSuspend(userId: Long): BuzUserRelationEntity? {
        return userLocalDS.getUserRelationNotSuspend(userId)
    }

    override fun getUserRelationFromCacheFlow(userId: Long): Flow<BuzUserRelationEntity?> {
        return userLocalDS.getUserRelationFlow(userId)
    }

    override fun getUserRelationFromCacheFlow(userList: List<Long>): Flow<Map<Long, BuzUserRelationEntity>> {
        return userLocalDS.getUserRelationFlow(userList)
    }

    override fun syncUserRelationFromServer(userId: Long) {
        syncUserRelation(arrayListOf(userId))
    }

    private suspend fun updateUserRelation(relationInfo: List<UserRelation>): List<BuzUserRelationEntity> {
        return userLocalDS.saveUserRelation(relationInfo.map { it.toBuzUserRelationInfo() })
    }

    private suspend fun updateUserRelation(relation: UserRelation): BuzUserRelationEntity {
        return userLocalDS.saveUserRelation(relation.toBuzUserRelationInfo())
    }

    override suspend fun getUserComposite(userId: Long): BuzUserComposite? {
        return loadUserCompositeFromLocal(userId)
            ?: (loadUserCompositeFromServer(userId) as? Resp.Success)?.data
    }

    override suspend fun getUserCompositeFromCache(userId: Long): BuzUserComposite? {
        return loadUserCompositeFromLocal(userId)
    }

    /**
     * @return 返回的数据跟查询的对应index，如果查不到，则对应的位置为null
     */
    override suspend fun getUserCompositeFromCache(userList: List<Long>): List<BuzUserComposite?> {
        return loadUserCompositeFromLocal(userList)
    }

    @Deprecated("为了兼容旧代码，新代码别用")
    override fun getUserCompositeFromCacheNotSuspend(userId: Long): BuzUserComposite? {
        return userLocalDS.getUserCompositeNotSuspend(userId)
    }

    @Deprecated("为了兼容旧代码，新代码别用")
    override fun getUserCompositeFromMemory(userId: Long): BuzUserComposite? {
        return userLocalDS.getUserCompositeFromMemory(userId)
    }

    override fun getUserCompositeFlow(userId: Long, forceRefresh: Boolean): Flow<BuzUserComposite> =
        flow {
            var isSync = false
            if (forceRefresh){
                syncUserComposite(arrayListOf(userId))
                isSync = true
            }
            getUserCompositeFromCacheFlow(userId).collect { localData ->
                if (!isSync && (localData == null || localData.relationInfo == null)) {
                    syncUserComposite(arrayListOf(userId))
                    isSync = true
                }
                if (localData != null) {
                    emit(localData)
                }
            }
        }

    override fun getUserCompositeFlow(userList: List<Long>): Flow<Map<Long, BuzUserComposite>> =
        flow {
            var isSync = false
            getUserCompositeFromCacheFlow(userList).collect { localData ->
                if (!isSync) {
                    val needSyncList = userList.filter {
                        val localComposite = localData[it]
                        localComposite == null || localComposite.relationInfo == null
                    }
                    syncUserComposite(needSyncList)
                    isSync = true
                }
                emit(localData)
            }
        }


    override fun getUserCompositeFromCacheFlow(userId: Long,refreshIfNull: Boolean): Flow<BuzUserComposite?> {
        return userLocalDS.getUserCompositeFlow(userId).onFirst {
            if (refreshIfNull && it == null) {
                syncUserComposite(arrayListOf(userId))
            }
        }
    }

    override fun getUserCompositeFromCacheFlow(userList: List<Long>): Flow<Map<Long, BuzUserComposite>> {
        return userLocalDS.getUserCompositeFlow(userList)
    }

    override suspend fun getUserCompositeFromServer(userId: Long): Resp<BuzUserComposite?> {
        return loadUserCompositeFromServer(userId)
    }

    override suspend fun getUserCompositeFromServer(userList: List<Long>): Resp<List<BuzUserComposite>?> {
        return loadUserCompositeFromServer(userList)
    }

    override fun syncUserCompositeFromServer(userId: Long) {
        syncUserComposite(arrayListOf(userId))
    }

    override fun syncUserCompositeFromServerIfCacheAbsent(userList: List<Long>) {
        userScope.launch {
            val localList = getUserCompositeFromCache(userList)
            val needSyncList = userList.filterIndexed { index, _ -> localList[index] == null }
            syncUserComposite(needSyncList)
        }
    }

    override suspend fun updateUserAndRelation(userRelationInfo: UserRelationInfo): BuzUserComposite {
        return userLocalDS.saveUserComposite(userRelationInfo.toBuzUserComposite())
    }

    override suspend fun updateUserAndRelation(userRelationInfo: List<UserRelationInfo>): List<BuzUserComposite> {
        return userLocalDS.saveUserComposite(userRelationInfo.map { it.toBuzUserComposite() })
    }

    override suspend fun addFriend(
        userId: Long, source: Int, businessId: String?
    ): Resp<BuzUserComposite> {
        val serverResp = userRemoteDS.addFriend(userId, source, businessId)
        return when (serverResp) {
            is Resp.Success -> {
                val userComposite = updateUserAndRelation(serverResp.data)
                AddFriendEvent.post(userId)
                Resp.Success(userComposite)
            }

            is Resp.Error -> {
                Resp.Error(serverResp.code, serverResp.msg, serverResp.prompt)
            }
        }
    }

    override suspend fun addBuzAIAsFriend(): Resp<BuzUserComposite> {
        val serverResp = userRemoteDS.addBuzAIAsFriend()
        return when (serverResp) {
            is Resp.Success -> {
                val userComposite = updateUserAndRelation(serverResp.data)
                serverResp.data.userInfo.userId?.let { AddFriendEvent.post(it) }
                Resp.Success(userComposite)
            }

            is Resp.Error -> {
                Resp.Error(serverResp.code, serverResp.msg, serverResp.prompt)
            }
        }
    }

    override suspend fun agreeAddResearchAccount(): Resp<BuzUserComposite> {
        val serverResp = userRemoteDS.agreeAddResearchAccount()
        return when (serverResp) {
            is Resp.Success -> {
                val data = serverResp.data
                if (data != null) {
                    val userComposite = updateUserAndRelation(data)
                    AddFriendEvent.post(userComposite.user.userId)
                    Resp.Success(userComposite)
                } else {
                    Resp.Error(
                        0,
                        "data is null",
                        serverResp.prompt
                    )
                }
            }

            is Resp.Error -> {
                Resp.Error(serverResp.code, serverResp.msg, serverResp.prompt)
            }
        }
    }

    override suspend fun refuseAddResearchAccount(): Resp<BuzUserComposite?> {
        val serverResp = userRemoteDS.agreeAddResearchAccount()
        return when (serverResp) {
            is Resp.Success -> {
                val data = serverResp.data
                if (data != null) {
                    val userComposite = updateUserAndRelation(data)
                    Resp.Success(userComposite)
                } else {
                    Resp.Success(null)
                }
            }

            is Resp.Error -> {
                Resp.Error(serverResp.code, serverResp.msg, serverResp.prompt)
            }
        }
    }


    override suspend fun agreeFriendApply(userId: Long): Resp<BuzUserRelationEntity> {
        val serverResp = userRemoteDS.agreeFriendApply(userId)
        return when (serverResp) {
            is Resp.Success -> {
                val userComposite = updateUserRelation(serverResp.data)
                //为了兼容旧版本，旧版本很多地方同意好友申请等都会抛出这个事件，理论上不需要这么重，
                //但是为了控制改动量，先这么搞，以后哪位仁兄改动到可以再考虑移除
                FriendsSyncCompleteEvent.post()
                AddFriendEvent.post(userId)
                Resp.Success(userComposite)
            }

            is Resp.Error -> {
                Resp.Error(serverResp.code, serverResp.msg, serverResp.prompt)
            }
        }
    }

    override suspend fun refuseFriendApply(userId: Long): Resp<BuzUserRelationEntity> {
        val serverResp = userRemoteDS.refuseFriendApply(userId)
        return when (serverResp) {
            is Resp.Success -> {
                val userComposite = updateUserRelation(serverResp.data)
                Resp.Success(userComposite)
            }

            is Resp.Error -> {
                Resp.Error(serverResp.code, serverResp.msg, serverResp.prompt)
            }
        }
    }

    override suspend fun deleteFriend(userId: Long): Resp<BuzUserRelationEntity> {
        val serverResp = userRemoteDS.deleteFriend(userId)
        return when (serverResp) {
            is Resp.Success -> {
                val userComposite = updateUserRelation(serverResp.data)
                Resp.Success(userComposite)
            }

            is Resp.Error -> {
                Resp.Error(serverResp.code, serverResp.msg, serverResp.prompt)
            }
        }
    }

    override suspend fun blockUser(userId: Long): Resp<BuzUserRelationEntity> {
        val serverResp = userRemoteDS.blockUser(userId)
        return when (serverResp) {
            is Resp.Success -> {
                val userComposite = updateUserRelation(serverResp.data)
                //兼容旧代码
                BlockUserEvent.post(userId, BlockEventType.BlockJoin.value)
                Resp.Success(userComposite)
            }

            is Resp.Error -> {
                Resp.Error(serverResp.code, serverResp.msg, serverResp.prompt)
            }
        }
    }

    override suspend fun unblockUser(userId: Long): Resp<BuzUserRelationEntity> {
        val serverResp = userRemoteDS.unblockUser(userId)
        return when (serverResp) {
            is Resp.Success -> {
                val userComposite = updateUserRelation(serverResp.data)
                //兼容旧代码
                BlockUserEvent.post(userId, BlockEventType.BlockRemove.value)
                Resp.Success(userComposite)
            }

            is Resp.Error -> {
                Resp.Error(serverResp.code, serverResp.msg, serverResp.prompt)
            }
        }
    }

    override suspend fun reportUser(userId: Long): Resp<Boolean> {
        val serverResp = userRemoteDS.reportUser(userId)
        return when (serverResp) {
            is Resp.Success -> {
                Resp.Success(true)
            }

            is Resp.Error -> {
                Resp.Error(serverResp.code, serverResp.msg, serverResp.prompt)
            }
        }
    }

    override suspend fun updateUserSetting(
        userId: Long,
        muteMessages: Int?,
        muteNotification: Int?,
        remark: String?
    ) {
        //先本地更新，然后再请求服务端更新，期间会重试，进程关闭没有更新成功会在下次启动前后台切换重试
        userLocalDS.insertOrUpdateUnUploadedUserRelationSettings(
            userId,
            muteMessages,
            muteNotification,
            remark
        )
        userLocalDS.updateUserRelationSettings(userId, muteMessages, muteNotification, remark)
        syncUserSettingToServer(userId, false)
        if (muteMessages != null || muteNotification != null) {
            //兼容旧代码
            MuteStatusUpdateEvent.post(userId)
        }
        if (remark != null) {
            //兼容旧代码
            UserInfoUpdateEvent.post(userId)
        }
    }

    private fun syncUserSettingToServer(userId: Long, isRetry: Boolean) {
        userSettingUpdateDispatcher.request(Retryable(userId, isRetry))
    }

    private suspend fun updateUserSettingToServer(
        userId: Long
    ): Resp<BuzUserRelationEntity?> {
        //查出来本地记录
        val localSetting = getUserUnUploadedSettings(userId) ?: return Resp.Success(null)
        //上传到服务端
        val serverResp = userRemoteDS.updateUserSetting(
            userId, localSetting.muteMessages, localSetting.muteNotification, localSetting.remark
        )
        return when (serverResp) {
            is Resp.Success -> {
                //更新完成后把未请求的记录删除
                deleteUnLoadedUserSettings(
                    userId,
                    localSetting.muteMessages,
                    localSetting.muteNotification,
                    localSetting.remark
                )
                Resp.Success(getUserRelationFromCache(userId))
            }

            is Resp.Error -> {
                Resp.Error(serverResp.code, serverResp.msg, serverResp.prompt)
            }
        }
    }

    private fun fetchSelfInfoOnceWhenPageStart() {
        userScope.launch {
            appLifecycle.withStarted {
                //线上有个bug,部分用户的数据库不确定什么原因，个人信息的relation可能不是BuzUserRelationValue.MYSELF
                //为了以防其他可能的情况，这里专门请求一下用户的数据更新数据库
                logInfo(TAG, "fetchSelfInfoOnceWhenAppStart:${userAuth.userId}")
                syncUserCompositeFromServer(userAuth.userId)
            }
        }
    }

    private fun observeFriendListChange() {
        signalRepository.getProtocolDataChangeFlow()
            .filter { it.businessType == PushBusinessType.FRIEND_LIST_CHANGE.type }
            .collectInScope(userScope) {
                logInfo(TAG, "friendListChange from signal:FRIEND_LIST_CHANGE")
                syncAllFriends()
            }
    }

    private fun observeFcmPush() {
        pushRepository.fcmPushFlow.collectInScope(userScope) { fcmData ->
            val type = fcmData.payload.type
            when (type) {
                PushPayloadType.TYPE_FRIEND_APPLY_PASSED -> {
                    logInfo(TAG, "syncAllFriends from fcm TYPE_FRIEND_APPLY_PASSED")
                    //这里应该不用同步全部好友的，但是由于历史代码原因，先这样处理，
                    //等后面所有地方都不依赖FriendsSyncCompleteEvent的时候，可以再优化，只更新变更的好友即可
                    //这次迭代改动太多了，延期严重，先这样吧
                    syncAllFriends()
                }
            }
        }
    }

    private fun startSyncAllUnUploadedUserSettingFromCache() {
        userScope.launch {
            appLifecycle.withStarted {
                userScope.launch {
                    val list = userLocalDS.getAllUserUnUploadedSettings()
                    logInfo(TAG, "startSyncAllUnUploadedUserSettingFromCache,size:${list?.size}")
                    list?.forEach {
                        //2秒一个，做个简单的周期，避免瞬间太多请求
                        delay(2000L)
                        syncUserSettingToServer(it.userId, false)
                    }
                }
            }
        }
    }

    override fun syncUserInfoFromServer(userId: Long) {
        syncBuzUser(arrayListOf(userId))
    }

    override fun syncUserInfoFromServer(userList: List<Long>) {
        syncBuzUser(userList)
    }

    private fun syncBuzUser(userIds: List<Long>, isRetry: Boolean = false) {
        if (userIds.isEmpty()) {
            return
        }
        syncUserDispatcher.request(Retryable(userIds, isRetry))
    }

    private fun syncUserRelation(userIds: List<Long>, isRetry: Boolean = false) {
        if (userIds.isEmpty()) {
            return
        }
        syncUserRelationDispatcher.request(Retryable(userIds, isRetry))
    }

    private fun syncUserComposite(userIds: List<Long>, isRetry: Boolean = false) {
        if (userIds.isEmpty()) {
            return
        }
        syncUserCompositeDispatcher.request(Retryable(userIds, isRetry))
    }

    private suspend fun loadUserFromLocal(userList: List<Long>): List<BuzUser?> {
        return userLocalDS.getUser(userList)
    }

    private suspend fun loadUserFromLocal(userId: Long): BuzUser? {
        return userLocalDS.getUser(userId)
    }

    private suspend fun loadUserFromServer(userId: Long): Resp<BuzUser?> {
        val resp = userRemoteDS.getUserInfo(userId)
        resp.prompt?.parse()
        val result: Resp<BuzUser?> = when (resp) {
            is Resp.Success -> {
                val user = resp.data
                if (user == null) {
                    //返回空代表用户注销了，目前服务端的接口是这样设计，应该要改成标识为注销才对，目前先这么处理，跟秋林聊了以后有一些内容涉及到注销的再优化
                    val saved = markUserAccountDeleted(userId)
                    userOnlineStatusRepo.clearStatus(userId)
                    Resp.Success(saved)
                } else {
                    val saved = updateUser(user)
                    userOnlineStatusRepo.updateStatus(resp.data)
                    Resp.Success(saved)
                }
            }

            is Resp.Error -> {
                Resp.Error(resp.code, resp.msg, resp.prompt)
            }
        }
        return result
    }

    private suspend fun loadUserBatchFromServer(userIds: List<Long>): Resp<List<BuzUser>?> {
        val resp = userRemoteDS.requestUserInfoBatch(userIds)
        resp.prompt?.parse()
        val result: Resp<List<BuzUser>?> = when (resp) {
            is Resp.Success -> {
                val userListFromServer = resp.data
                if (userListFromServer == null) {
                    Resp.Success(null)
                } else {
                    val saved = updateUser(userListFromServer)
                    userListFromServer.forEach { userOnlineStatusRepo.updateStatus(it) }
                    Resp.Success(saved)
                }
            }

            is Resp.Error -> {
                Resp.Error(resp.code, resp.msg, resp.prompt)
            }
        }
        return result
    }

    private suspend fun loadUserRelationFromLocal(userId: Long): BuzUserRelationEntity? {
        return userLocalDS.getUserRelation(userId)
    }

    private suspend fun loadUserRelationFromServer(userId: Long): Resp<BuzUserRelationEntity?> {
        val resp = userRemoteDS.requestUserRelation(userId)
        resp.prompt?.parse()
        val result: Resp<BuzUserRelationEntity?> = when (resp) {
            is Resp.Success -> {
                val userRelation = resp.data
                if (userRelation == null) {
                    Resp.Success(null)
                } else {
                    Resp.Success(updateUserRelation(userRelation))
                }
            }

            is Resp.Error -> {
                Resp.Error(resp.code, resp.msg, resp.prompt)
            }
        }
        return result
    }

    private suspend fun loadUserRelationFromServer(userId: List<Long>): Resp<List<BuzUserRelationEntity>?> {
        val resp = userRemoteDS.requestUserRelationBatch(userId)
        resp.prompt?.parse()
        val result: Resp<List<BuzUserRelationEntity>?> = when (resp) {
            is Resp.Success -> {
                val buzUserList = resp.data
                if (buzUserList == null) {
                    Resp.Success(null)
                } else {
                    Resp.Success(updateUserRelation(buzUserList))
                }
            }

            is Resp.Error -> {
                Resp.Error(resp.code, resp.msg, resp.prompt)
            }
        }
        return result
    }

    /**
     * @return 返回的数据跟查询的对应index，如果查不到，则对应的位置为null
     */
    private suspend fun loadUserCompositeFromLocal(userList: List<Long>): List<BuzUserComposite?> {
        return userLocalDS.getUserComposite(userList)
    }

    private suspend fun loadUserCompositeFromLocal(userId: Long): BuzUserComposite? {
        return userLocalDS.getUserComposite(userId)
    }

    private suspend fun loadUserCompositeFromServer(userId: Long): Resp<BuzUserComposite?> {
        val resp = userRemoteDS.requestUserComposite(userId)
        resp.prompt?.parse()
        val result: Resp<BuzUserComposite?> = when (resp) {
            is Resp.Success -> {
                val buzUser = resp.data?.userInfo?.toBuzUser()
                if (buzUser == null) {
                    //返回空代表用户注销了，目前服务端的接口是这样设计，应该要改成标识为注销才对，目前先这么处理，跟秋林聊了以后有一些内容涉及到注销的再优化
                    markUserAccountDeleted(userId)
                    userOnlineStatusRepo.clearStatus(userId)
                    Resp.Success(getUserCompositeFromCache(userId))
                } else {
                    val userRelation = resp.data?.userRelation?.toBuzUserRelationInfo()
                    userLocalDS.saveUserComposite(
                        arrayListOf(
                            BuzUserComposite(
                                buzUser, userRelation
                            )
                        )
                    )
                    userOnlineStatusRepo.updateStatus(resp.data?.userInfo)
                    Resp.Success(BuzUserComposite(buzUser, userRelation))
                }
            }

            is Resp.Error -> {
                Resp.Error(resp.code, resp.msg, resp.prompt)
            }
        }
        return result
    }

    private suspend fun loadUserCompositeFromServer(userIds: List<Long>): Resp<List<BuzUserComposite>?> {
        val resp = userRemoteDS.requestUserCompositeBatch(userIds)
        resp.prompt?.parse()
        val result: Resp<List<BuzUserComposite>?> = when (resp) {
            is Resp.Success -> {
                val buzUserList = resp.data
                if (buzUserList == null) {
                    Resp.Success(null)
                } else {
                    val saved = updateUserAndRelation(buzUserList)
                    buzUserList.forEach { userOnlineStatusRepo.updateStatus(it.userInfo) }
                    Resp.Success(saved)
                }
            }

            is Resp.Error -> {
                Resp.Error(resp.code, resp.msg, resp.prompt)
            }
        }
        return result
    }

    private suspend fun deleteUnLoadedUserSettings(
        userId: Long,
        updatedMuteMessages: Int? = null,
        updatedMuteNotification: Int? = null,
        updatedRemark: String? = null
    ) {
        userLocalDS.deleteUserUnUploadedSettingsIfMatched(
            userId, updatedMuteMessages, updatedMuteNotification, updatedRemark
        )
    }

    private suspend fun getUserUnUploadedSettings(userId: Long): UserUnUploadedSetting? {
        return userLocalDS.getUserUnUploadedSettings(userId)
    }

    private suspend fun loadBlockListFromServer(): Boolean {
        val resp = userRemoteDS.getBlockList()
        resp.prompt?.parse()
        return when (resp) {
            is Resp.Success -> {
                val userList = resp.data
                updateUser(userList)
                userLocalDS.replaceAllBlockList(userList.mapNotNull { it.userId })
                true
            }

            is Resp.Error -> {
                false
            }
        }
    }

    private suspend fun loadFriendsFromServer(): Boolean {
        val lastTimestamp = userLocalDS.getLastRequestFriendTimestamp()
        val resp = userRemoteDS.getFriendList(lastTimestamp)
        if (resp is Resp.Success) {
            val data = resp.data ?: return true
            val userList = data.friendList ?: return true
            val relationMap = data.userRelationList?.associateBy { it.userId } ?: return true
            val compositeList = userList.mapNotNull {
                val relation = relationMap[it.userId] ?: return@mapNotNull null
                val user = it.toBuzUser()
                val relationEntity = relation.toBuzUserRelationInfo()
                BuzUserComposite(user, relationEntity)
            }
            userLocalDS.saveUserComposite(compositeList)
            val localFriendSet = userLocalDS.getAllFriendIds().toHashSet()
            val remoteFriendSet = userList.mapNotNull { it.userId }.toHashSet()
            val deletedFriendSet = localFriendSet - remoteFriendSet
            //重新同步已经删除的好友
            syncUserRelation(deletedFriendSet.toList(), false)
            userOnlineStatusRepo.updateStatus(userList)
            FriendsSyncCompleteEvent.post()
            return true
        }
        return false
    }

    private suspend fun UserRelationInfo.toBuzUserComposite(): BuzUserComposite {
        return BuzUserComposite(
            user = this.userInfo.toBuzUser(),
            relationInfo = this.userRelation.toBuzUserRelationInfo()
        )
    }

    private fun UserInfo.toBuzUser(): BuzUser {
        return BuzUser(
            userId = userId ?: 0L,
            userName = userName,
            firstName = firstName,
            lastName = lastName,
            portrait = portrait ?: "",
            registerTime = registerTime,
            buzId = buzId,
            userType = userType,
            userStatus = userStatus
        )
    }

    private suspend fun UserRelation.toBuzUserRelationInfo(): BuzUserRelationEntity {
        //对于部分信息优先使用本地数据的，如果本地有还没有上传成功的信息，先使用本地的，本地优先
        val unUploadedSettings = getUserUnUploadedSettings(userId)
        return BuzUserRelationEntity(
            userId = userId,
            muteMessages = unUploadedSettings?.muteMessages ?: muteMessages
            ?: MuteType.DEFAULT.value,
            muteNotification = unUploadedSettings?.muteNotification ?: muteNotification
            ?: MuteType.DEFAULT.value,
            relation = relation,
            remark = unUploadedSettings?.remark ?: remark,
            friendTime = becomeFriendTime,
            isBlocked = isBlocked ?: false,
        )
    }

    private fun RequestDispatcher(
        debugTag: String, handler: suspend (List<Long>) -> Boolean
    ) = MergedRequestDispatcher<Retryable<List<Long>>>(scope = userScope,
        dispatchInterval = DefaultRetryableDispatchInterval(networkStatus, appLifecycle),
        debugTag = debugTag,
        onUndeliveredElement = null,
        requestHandler = { requestList ->
            val userIds = requestList.flatMapTo(hashSetOf()) { it.data }
            handler(userIds.toList())
        })
}